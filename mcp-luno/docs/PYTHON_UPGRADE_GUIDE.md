# 🐍 Python Upgrade Guide for FastMCP

This guide walks you through upgrading Python to 3.12 for full FastMCP support.

## 📦 Step 1: Install Python 3.12 (In Progress)

```bash
brew install python@3.12
```

**Status:** Currently installing...

## 🔗 Step 2: Set Up Python 3.12 (After installation)

After the installation completes, run these commands:

```bash
# Add Python 3.12 to your PATH
echo 'export PATH="/opt/homebrew/opt/python@3.12/bin:$PATH"' >> ~/.zshrc

# Reload your shell configuration
source ~/.zshrc

# Create an alias for easier access
echo 'alias python3.12="/opt/homebrew/opt/python@3.12/bin/python3.12"' >> ~/.zshrc
echo 'alias pip3.12="/opt/homebrew/opt/python@3.12/bin/pip3.12"' >> ~/.zshrc

# Reload again
source ~/.zshrc

# Verify the new Python version
python3.12 --version
```

## 📦 Step 3: Install FastMCP and Dependencies

```bash
# Install FastMCP with Python 3.12
pip3.12 install fastmcp httpx pydantic pydantic-settings python-dotenv

# Verify installation
python3.12 -c "import fastmcp; print('FastMCP version:', fastmcp.__version__)"
```

## 🔧 Step 4: Update Claude Desktop Config

Replace your `claude_desktop_config.json` with:

```json
{
  "mcpServers": {
    "luno": {
      "command": "/opt/homebrew/opt/python@3.12/bin/python3.12",
      "args": [
        "/Users/<USER>/Documents/mcp/mcp-luno/src/luno_mcp_server/server.py"
      ],
      "cwd": "/Users/<USER>/Documents/mcp/mcp-luno",
      "env": {
        "LUNO_API_KEY": "your_api_key_here",
        "LUNO_API_SECRET": "your_api_secret_here"
      }
    }
  }
}
```

**Key change:** Using the full path to Python 3.12

## ✅ Step 5: Test the Full FastMCP Implementation

After setup, you'll have access to:

**All 7 Luno Tools:**
1. `get_crypto_price` - Real-time prices for any pair
2. `get_market_overview` - Market data
3. `get_account_balance` - Real account balances
4. `place_order` - Place trades
5. `cancel_order` - Cancel orders
6. `get_order_status` - Order status
7. `get_transaction_history` - Transaction history
8. `get_fees` - Trading fees

**Real API Integration:**
- Live data from Luno API
- All currency pairs (ZAR, EUR, GBP, USD)
- Proper async/await handling
- Production-ready error handling

## 🧪 Test Commands

After upgrade, test with:

```bash
# Test the FastMCP server directly
cd /Users/<USER>/Documents/mcp/mcp-luno
python3.12 src/luno_mcp_server/server.py

# Test imports
python3.12 -c "
from src.luno_mcp_server.server import mcp
print('✅ FastMCP server loaded')
print(f'✅ Tools available: {len(mcp._tools)}')
for tool in mcp._tools:
    print(f'  - {tool}')
"
```

## 🔄 Alternative: Use Python 3.12 as Default

If you want to make Python 3.12 your default:

```bash
# Create symlinks (optional)
ln -sf /opt/homebrew/opt/python@3.12/bin/python3.12 /opt/homebrew/bin/python3
ln -sf /opt/homebrew/opt/python@3.12/bin/pip3.12 /opt/homebrew/bin/pip3

# Update your Claude config to just use "python3"
```

## 🎯 Benefits of Python 3.12 + FastMCP

✅ **Latest FastMCP features** - All newest capabilities  
✅ **Better performance** - Python 3.12 is faster  
✅ **Real async support** - Proper async/await handling  
✅ **Production ready** - Stable, mature implementation  
✅ **Full Luno integration** - All 7 tools working perfectly  
✅ **Multi-currency support** - All trading pairs  

## 🚨 Backup Plan

If anything goes wrong, you can always fall back to the Python 3.9 compatible version:

```json
{
  "mcpServers": {
    "luno": {
      "command": "python3",
      "args": [
        "/Users/<USER>/Documents/mcp/mcp-luno/src/sync_working_server.py"
      ],
      "cwd": "/Users/<USER>/Documents/mcp/mcp-luno",
      "env": {
        "LUNO_API_KEY": "your_api_key_here",
        "LUNO_API_SECRET": "your_api_secret_here"
      }
    }
  }
}
```

---

**Next:** Wait for Python installation to complete, then follow Step 2!