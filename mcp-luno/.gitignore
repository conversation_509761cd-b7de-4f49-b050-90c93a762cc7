# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/

# Virtual Environments
.env
.venv
venv/
ENV/
env/

# VS Code specific
.vscode/*
!.vscode/launch.json
!.vscode/tasks.json
!.vscode/mcp.json
!.vscode/settings.json
*.code-workspace

# Logs
*.log
logs/

# OS specific files
.DS_Store
Thumbs.db
.directory
Desktop.ini

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
coverage.xml
*.cover

# API keys and secrets
.env
credentials.json

# Documentation builds
docs/_build/
site/

# Jupyter Notebook
.ipynb_checkpoints

# Local development configuration
instance/
.webassets-cache
node_modules/

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Backup files
*~
*.bak
*.swp
.roomodes