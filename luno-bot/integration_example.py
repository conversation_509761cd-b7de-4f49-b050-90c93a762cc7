import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
import json

# Import the Market Structure Analysis components
from models.market_structure import MarketStructureAnalyzer, MarketStructurePredictor

# Import other predictors for comparison
from models.indicators.macd import MACDPredictor
from models.indicators.rsi import RSIPredictor
from models.indicators.scalping import ScalpingPredictor


class TradingSystem:
    """Example trading system using the enhanced Market Structure Analysis."""
    
    def __init__(
        self,
        lookback_period: int = 20,
        timeframes: List[str] = None,
        confidence_threshold: float = 0.7,
        use_multi_timeframe: bool = True
    ):
        """Initialize the trading system.
        
        Args:
            lookback_period: Period for trend analysis
            timeframes: List of timeframes to analyze
            confidence_threshold: Minimum confidence for signals
            use_multi_timeframe: Whether to use multi-timeframe analysis
        """
        self.lookback_period = lookback_period
        self.timeframes = timeframes if timeframes is not None else ["1d", "4h", "1h"]
        self.confidence_threshold = confidence_threshold
        self.use_multi_timeframe = use_multi_timeframe
        
        # Initialize the market structure predictor
        self.market_predictor = MarketStructurePredictor(
            lookback_period=lookback_period,
            swing_threshold=0.01,
            confidence_threshold=confidence_threshold,
            timeframes=self.timeframes
        )
        
        # Initialize individual indicator predictors for comparison
        self.rsi_predictor = RSIPredictor(window_size=14)
        self.macd_predictor = MACDPredictor(fast=12, slow=26, signal=9)
        self.scalping_predictor = ScalpingPredictor(
            price_deviation_threshold=0.002,
            volume_spike_threshold=1.3,
            momentum_lookback=5,
            use_bollinger=True
        )
        
        # Store signals history
        self.signals_history = []
        
    def resample_data(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """Resample data to a different timeframe."""
        # Map string timeframes to pandas offset aliases
        timeframe_map = {
            '1d': '1D',
            '4h': '4H',
            '1h': '1H',
            '30m': '30min',
            '15m': '15min',
            '5m': '5min',
            '1m': '1min'
        }
        
        if timeframe not in timeframe_map:
            raise ValueError(f"Unsupported timeframe: {timeframe}")
        
        # Set timestamp as index for resampling
        data_indexed = data.set_index('timestamp')
        
        # Resample
        resampled = data_indexed.resample(timeframe_map[timeframe]).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        # Reset index to get timestamp as a column again
        resampled = resampled.reset_index()
        
        return resampled
    
    def prepare_multi_timeframe_data(self, data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Prepare data for multiple timeframes."""
        multi_tf_data = {}
        
        # Start with the original data as base timeframe
        base_timeframe = self.timeframes[0]  # Typically the longest timeframe
        multi_tf_data[base_timeframe] = data.copy()
        
        # Create shorter timeframes by resampling if needed
        if len(self.timeframes) > 1:
            for tf in self.timeframes[1:]:
                multi_tf_data[tf] = self.resample_data(data, tf)
        
        return multi_tf_data
    
    def analyze_market(self, data: pd.DataFrame) -> Dict:
        """Analyze market data and generate signals.
        
        Args:
            data: DataFrame with OHLC price data
            
        Returns:
            Dict with analysis results and signals
        """
        results = {
            "timestamp": datetime.now(),
            "signals": {},
            "analysis": {}
        }
        
        # Prepare data for different timeframes
        if self.use_multi_timeframe:
            multi_tf_data = self.prepare_multi_timeframe_data(data)
            # Generate market structure signal
            market_signal = self.market_predictor.generate_signal(data=multi_tf_data)
        else:
            # Use single timeframe analysis
            market_signal = self.market_predictor.generate_signal(single_data=data)
        
        # Store market structure signal
        results["signals"]["market_structure"] = market_signal
        
        # Train and generate signals from other predictors for comparison
        try:
            # RSI signal
            self.rsi_predictor.train(data, target_col="close")
            rsi_prediction = self.rsi_predictor.predict(data)
            
            # MACD signal
            self.macd_predictor.train(data, target_col="close")
            macd_prediction = self.macd_predictor.predict(data)
            
            # Scalping signal
            self.scalping_predictor.train(data, target_col="close")
            scalping_signal = self.scalping_predictor.generate_signals(data)
            
            # Store comparison signals
            results["signals"]["rsi"] = {
                "prediction": float(rsi_prediction[0]),
                "current_price": float(data["close"].iloc[-1]),
                "action": "buy" if rsi_prediction[0] > data["close"].iloc[-1] else "sell"
            }
            
            results["signals"]["macd"] = {
                "prediction": float(macd_prediction[0]),
                "current_price": float(data["close"].iloc[-1]),
                "action": "buy" if macd_prediction[0] > data["close"].iloc[-1] else "sell"
            }
            
            results["signals"]["scalping"] = scalping_signal
            
        except Exception as e:
            print(f"Error generating comparison signals: {e}")
        
        # Store the results in history
        self.signals_history.append(results)
        
        return results
    
    def get_final_signal(self, results: Dict) -> Dict:
        """Generate final trading signal by combining multiple signals.
        
        This is where you can implement your custom logic to combine signals.
        In this example, we prioritize the market structure signal with high confidence.
        
        Args:
            results: Analysis results dictionary
            
        Returns:
            Dict with final trading signal
        """
        # Start with the market structure signal as baseline
        market_signal = results["signals"]["market_structure"]
        
        # Initialize final signal
        final_signal = {
            "action": market_signal["action"],
            "confidence": market_signal["confidence"],
            "reason": f"Market Structure: {market_signal['reason']}",
            "timestamp": datetime.now()
        }
        
        # Only consider other signals if market structure confidence is below threshold
        if market_signal["confidence"] < self.confidence_threshold:
            # Count votes for each action
            votes = {"buy": 0, "sell": 0, "hold": 0}
            
            # Add vote from market structure
            votes[market_signal["action"]] += 1
            
            # Add vote from RSI
            if "rsi" in results["signals"]:
                rsi_action = results["signals"]["rsi"]["action"]
                votes[rsi_action] += 0.7  # Lower weight
            
            # Add vote from MACD
            if "macd" in results["signals"]:
                macd_action = results["signals"]["macd"]["action"]
                votes[macd_action] += 0.8  # Medium weight
            
            # Add vote from Scalping
            if "scalping" in results["signals"]:
                scalping_action = results["signals"]["scalping"]["action"]
                votes[scalping_action] += 0.6  # Lower weight
                
            # Find the action with the most votes
            max_votes = max(votes.values())
            winning_actions = [action for action, vote in votes.items() if vote == max_votes]
            
            # If there's a clear winner and it's not the original market structure signal
            if len(winning_actions) == 1 and winning_actions[0] != market_signal["action"]:
                # Override the market structure signal if the winning action has significantly more votes
                if votes[winning_actions[0]] > votes[market_signal["action"]] + 1:
                    final_signal["action"] = winning_actions[0]
                    final_signal["reason"] += f", overridden by consensus ({winning_actions[0]})"
                    final_signal["confidence"] = 0.6  # Lower confidence when overriding
            
            # Add reasoning from other signals
            if "rsi" in results["signals"]:
                final_signal["reason"] += f", RSI: {results['signals']['rsi']['action']}"
                
            if "macd" in results["signals"]:
                final_signal["reason"] += f", MACD: {results['signals']['macd']['action']}"
                
            if "scalping" in results["signals"] and "reason" in results["signals"]["scalping"]:
                final_signal["reason"] += f", Scalping: {results['signals']['scalping']['reason']}"
        
        return final_signal
    
    def save_results(self, results: Dict, filename: str = "trading_results.json"):
        """Save analysis results to a file.
        
        Args:
            results: Analysis results to save
            filename: Output filename
        """
        # Convert datetime objects to strings
        def convert_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            return obj
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(results, f, default=convert_datetime, indent=2)
        
        print(f"Results saved to {filename}")

    def execute_trade(self, signal: Dict):
        """Execute a trade based on the signal.
        
        This is where you would integrate with your actual trading platform.
        In this example, we just print the trade details.
        
        Args:
            signal: Trading signal dictionary
        """
        if signal["action"] == "hold":
            print("No trade executed (signal: HOLD)")
            return
        
        # In a real implementation, this would connect to your trading platform API
        print(f"\nEXECUTING {signal['action'].upper()} TRADE")
        print(f"Confidence: {signal['confidence']:.2f}")
        print(f"Reason: {signal['reason']}")
        print(f"Time: {signal['timestamp']}")
        
        # Calculate position size based on confidence (example)
        position_size = min(1.0, signal['confidence']) * 100
        print(f"Position Size: {position_size:.2f}% of available capital")
        
        # Define stop loss and take profit (example)
        stop_loss_pct = 0.02 * (1 - signal['confidence'])  # Tighter stop for higher confidence
        take_profit_pct = 0.05 * signal['confidence']      # Higher target for higher confidence
        
        print(f"Suggested Stop Loss: {stop_loss_pct:.2f}%")
        print(f"Suggested Take Profit: {take_profit_pct:.2f}%")


def generate_sample_data(days=100, volatility=0.02, trend=0.001):
    """Generate sample price data for testing."""
    # Create date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # Generate price data with trend and volatility
    np.random.seed(42)  # For reproducible results
    prices = 100 * (1 + np.cumsum(np.random.normal(trend, volatility, len(date_range))))
    
    # Add some patterns 
    # Uptrend in the middle
    mid_point = len(prices) // 2
    prices[mid_point:mid_point+20] *= np.linspace(1, 1.2, 20)
    
    # Downtrend near the end
    prices[-20:] *= np.linspace(1, 0.9, 20)
    
    # Create DataFrame
    data = pd.DataFrame({
        'timestamp': date_range,
        'open': prices * np.random.uniform(0.99, 1.01, len(prices)),
        'high': prices * np.random.uniform(1.01, 1.03, len(prices)),
        'low': prices * np.random.uniform(0.97, 0.99, len(prices)),
        'close': prices,
        'volume': np.random.uniform(1000, 5000, len(prices)) * (1 + 0.5 * np.sin(np.linspace(0, 8*np.pi, len(prices))))
    })
    
    return data


def demo_trading_system():
    """Run a demonstration of the trading system."""
    print("=== TRADING SYSTEM DEMONSTRATION ===\n")
    
    # Generate sample data
    print("Generating sample market data...")
    market_data = generate_sample_data(days=100)
    print(f"Generated {len(market_data)} data points\n")
    
    # Create trading system
    print("Initializing trading system...")
    system = TradingSystem(
        lookback_period=20,
        timeframes=["1d", "4h", "1h"],
        confidence_threshold=0.7,
        use_multi_timeframe=True
    )
    
    # Analyze market
    print("Analyzing market data...")
    results = system.analyze_market(market_data)
    
    # Get final trading signal
    final_signal = system.get_final_signal(results)
    
    # Display results
    print("\n=== ANALYSIS RESULTS ===")
    
    # Market structure signal
    ms_signal = results["signals"]["market_structure"]
    print(f"\nMarket Structure Signal: {ms_signal['action'].upper()}")
    print(f"Confidence: {ms_signal['confidence']:.2f}")
    print(f"Reason: {ms_signal['reason']}")
    
    # Other signals
    if "rsi" in results["signals"]:
        print(f"\nRSI Signal: {results['signals']['rsi']['action'].upper()}")
        print(f"Prediction: {results['signals']['rsi']['prediction']:.2f}")
        
    if "macd" in results["signals"]:
        print(f"\nMACD Signal: {results['signals']['macd']['action'].upper()}")
        print(f"Prediction: {results['signals']['macd']['prediction']:.2f}")
        
    if "scalping" in results["signals"]:
        print(f"\nScalping Signal: {results['signals']['scalping']['action'].upper()}")
        print(f"Confidence: {results['signals']['scalping']['confidence']:.2f}")
        print(f"Reason: {results['signals']['scalping']['reason']}")
    
    # Final signal
    print("\n=== FINAL TRADING SIGNAL ===")
    print(f"Action: {final_signal['action'].upper()}")
    print(f"Confidence: {final_signal['confidence']:.2f}")
    print(f"Reason: {final_signal['reason']}")
    
    # Execute trade
    print("\n=== TRADE EXECUTION ===")
    system.execute_trade(final_signal)
    
    # Save results
    system.save_results(results)


if __name__ == "__main__":
    demo_trading_system()