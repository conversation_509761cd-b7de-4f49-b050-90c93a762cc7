#!/bin/bash
#
# Continuous Runner Script for Luno Trading Bot
# This script ensures the trading bot stays running 24/7 by
# automatically restarting it if it crashes.
#

# Directory where logs will be stored
LOG_DIR="data/logs"
RESTART_LOG="${LOG_DIR}/restart.log"
MAX_RESTART_DELAY=300  # Maximum restart delay in seconds

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Initialize restart counter and delay
restart_count=0
restart_delay=10

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${RESTART_LOG}"
}

# Function to handle exit
cleanup() {
    log_message "Shutdown requested. Stopping continuous runner..."
    exit 0
}

# Set up trap for graceful shutdown
trap cleanup SIGINT SIGTERM

# Store all provided arguments to pass to the Python script
SCRIPT_ARGS="$@"

log_message "Starting continuous runner for Luno Trading Bot"
log_message "Arguments: ${SCRIPT_ARGS}"

# Main loop to keep the bot running
while true; do
    log_message "Starting trading bot (attempt #$((restart_count+1)))"
    
    # Run the trading bot and capture its exit code
    python3 run_continuous_traders.py ${SCRIPT_ARGS}
    exit_code=$?
    
    # Increment restart counter
    restart_count=$((restart_count+1))
    
    # Check exit code
    if [ $exit_code -eq 0 ]; then
        log_message "Bot exited normally with code 0. Restarting..."
        restart_delay=10  # Reset delay for normal exit
    else
        log_message "Bot crashed with exit code ${exit_code}. Restarting in ${restart_delay} seconds..."
        sleep ${restart_delay}
        
        # Increase delay for next restart (exponential backoff with maximum)
        restart_delay=$((restart_delay * 2))
        if [ ${restart_delay} -gt ${MAX_RESTART_DELAY} ]; then
            restart_delay=${MAX_RESTART_DELAY}
        fi
    fi
    
    # Log restart statistics
    log_message "Total restarts: ${restart_count}, Next delay: ${restart_delay}s"
done