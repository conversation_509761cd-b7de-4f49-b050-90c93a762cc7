#!/usr/bin/env python3
"""
Luno API Test Script

This script tests the Luno API client with the official SDK,
displaying raw responses to help debug any issues.
"""

import os
import sys
import json
import time
from pathlib import Path
from pprint import pprint
from dotenv import load_dotenv

# Add the project root to the path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import utilities
from core.api_client import LunoAPIClient

# Load environment variables
load_dotenv()


def print_section(title):
    """Print a section header."""
    print("\n" + "=" * 50)
    print(title)
    print("=" * 50)


def test_api_method(method_name, method, *args, **kwargs):
    """Test an API method and print the raw response.

    Args:
        method_name: Name of the method being tested
        method: The method to call
        *args: Arguments to pass to the method
        **kwargs: Keyword arguments to pass to the method
    """
    print(f"\nTesting {method_name}...")
    print("-" * 30)

    try:
        # Get the raw response from the Luno SDK
        response = method(*args, **kwargs)

        # Print the raw response
        print("Raw response type:", type(response))

        if hasattr(response, "__dict__"):
            print("\nResponse attributes:")
            for key, value in response.__dict__.items():
                if not key.startswith("_"):
                    print(f"  {key}: {type(value)}")

            # For API methods that return lists, show sample items
            for key, value in response.__dict__.items():
                if isinstance(value, list) and len(value) > 0:
                    print(f"\nSample of {key} (first item):")
                    sample = value[0]
                    if hasattr(sample, "__dict__"):
                        pprint(sample.__dict__)
                    else:
                        pprint(sample)
        else:
            print("\nRaw response:")
            pprint(response)

        return response
    except Exception as e:
        print(f"Error: {e}")
        return None


def main():
    """Main function."""
    print_section("Luno API Test Script")

    # Check for required environment variables
    if not os.getenv("LUNO_API_KEY") or not os.getenv("LUNO_API_SECRET"):
        print(
            "Error: LUNO_API_KEY and LUNO_API_SECRET environment variables must be set"
        )
        return 1

    # Create API client using the SDK directly
    try:
        import luno_python.client as luno

        print("Creating direct SDK client...")
        sdk_client = luno.Client(
            api_key_id=os.getenv("LUNO_API_KEY"),
            api_key_secret=os.getenv("LUNO_API_SECRET"),
        )

        # Test basic API methods using the SDK directly
        print_section("Testing Direct SDK Calls")

        # Get tickers
        tickers = test_api_method("get_tickers", sdk_client.get_tickers)

        # Get ticker for XBTZAR
        ticker = test_api_method("get_ticker", sdk_client.get_ticker, pair="XBTZAR")

        # Get account balances
        balances = test_api_method("get_balances", sdk_client.get_balances)

    except Exception as e:
        print(f"Error with direct SDK calls: {e}")

    # Create our API client
    print_section("Testing Our API Client Wrapper")
    api_client = LunoAPIClient()

    # Test the wrapper methods
    try:
        # Get tickers
        tickers = api_client.get_tickers()
        print("\nWrapped get_tickers() response:")
        pprint(tickers)

        # Get ticker for XBTZAR
        ticker = api_client.get_ticker("XBTZAR")
        print("\nWrapped get_ticker() response:")
        pprint(ticker)

        # Get account balances
        balances = api_client.get_balances()
        print("\nWrapped get_balances() response:")
        pprint(balances)

    except Exception as e:
        print(f"Error with API client wrapper: {e}")

    print_section("API Test Completed")
    return 0


if __name__ == "__main__":
    sys.exit(main())
