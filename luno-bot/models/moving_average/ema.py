"""Exponential Moving Average prediction model."""

import numpy as np
import pandas as pd
from typing import Optional

from ..base.predictor import PricePredictor


class EMAPredictor(PricePredictor):
    """Predictor based on Exponential Moving Average (EMA)."""

    def __init__(self, model_dir: Optional[str] = None, window_size: int = 7):
        """Initialize the EMA predictor.

        Args:
            model_dir: Directory to save/load models from
            window_size: Window size for the EMA
        """
        super().__init__(model_dir)
        self.window_size = window_size
        self.model = {"window_size": window_size}
        self.target_col = "close"  # Default target column

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """No complex training needed for EMA, just store the configuration.

        Args:
            data: DataFrame containing market data
            target_col: Column to predict
        """
        self.target_col = target_col

    def predict(self, data: pd.DataFrame) -> pd.Series:
        """Predict next price using exponential moving average.

        Args:
            data: DataFrame containing market data

        Returns:
            Series with the predicted prices
        """
        if len(data) == 0:
            return pd.Series([0.0], name="EMA")
        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")

        # Calculate EMA
        ema = (
            data[self.target_col]
            .ewm(span=min(self.window_size, len(data)), adjust=False)
            .mean()
        )

        # If we have issues with not enough data
        if np.isnan(ema.iloc[-1]) and len(data) > 0:
            mean_price = data[self.target_col].mean()
            return pd.Series([mean_price], name="EMA")

        # Return the full EMA series
        return ema
