"""Simple Moving Average prediction model."""

import numpy as np
import pandas as pd
from typing import Optional

from ..base.predictor import PricePredictor


class SimpleMovingAveragePredictor(PricePredictor):
    """Simple predictor based on moving averages."""

    def __init__(self, model_dir: Optional[str] = None, window_size: int = 7):
        """Initialize the Simple Moving Average predictor.

        Args:
            model_dir: Directory to save/load models from
            window_size: Window size for the moving average
        """
        super().__init__(model_dir)
        self.window_size = window_size
        # No actual model needed for SMA
        self.model = {"window_size": window_size}
        self.target_col = "close"  # Default target column

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """No training needed for SMA, just store the configuration.

        Args:
            data: DataFrame containing market data
            target_col: Column to predict
        """
        # Nothing to train, SMA is just a statistical measure
        self.target_col = target_col

    def predict(self, data: pd.DataFrame) -> pd.Series:
        """Predict next price using simple moving average.

        Args:
            data: DataFrame containing market data

        Returns:
            Series with the predicted prices
        """
        if len(data) == 0:
            return pd.Series([0.0], name="SMA")
            
        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")

        # Calculate SMA
        sma = (
            data[self.target_col]
            .rolling(window=min(self.window_size, len(data)))
            .mean()
        )

        # If we don't have enough data for the full window, adapt
        if np.isnan(sma.iloc[-1]) and len(data) > 0:
            # Use the mean of all available data
            mean_price = data[self.target_col].mean()
            return pd.Series([mean_price], name="SMA")

        # Return the full SMA series
        return sma
