"""
Prediction models for the Luno Trading Bot.

This package provides various price prediction models and market structure analysis.
"""

# Base predictor
from .base.predictor import PricePredictor

# Moving average models
from .moving_average.sma import SimpleMovingAveragePredictor
from .moving_average.ema import EMAPredictor

# Indicator-based models
from .indicators.rsi import RSIPredictor
from .indicators.macd import MACDPredictor
from .indicators.scalping import ScalpingPredictor

# Regression models
from .regression.linear_regression import LinearRegressionPredictor

# Market structure analysis
from .market_structure import MarketStructureAnalyzer, MarketStructurePredictor

__all__ = [
    "PricePredictor",
    "SimpleMovingAveragePredictor",
    "EMAPredictor",
    "RSIPredictor",
    "MACDPredictor",
    "ScalpingPredictor",
    "LinearRegressionPredictor",
    "MarketStructureAnalyzer",
    "MarketStructurePredictor",
]
