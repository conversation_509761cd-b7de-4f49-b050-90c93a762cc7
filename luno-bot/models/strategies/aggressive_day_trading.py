"""Aggressive day trading strategy using optimized EMA and SMA parameters."""

import pandas as pd
import numpy as np
from typing import Dict, Any, Tuple, Optional
from pathlib import Path
import pickle

from models.moving_average.ema import EMAPredictor
from models.moving_average.sma import SimpleMovingAveragePredictor


from ..base.predictor import PricePredictor


class AggressiveDayTradingStrategy(PricePredictor):
    """Trading strategy using fast EMA, medium EMA and SMA for day trading.

    This strategy uses optimized parameters for aggressive day trading:
    - Fast EMA: 5 periods (captures immediate price movements)
    - Medium EMA: 13 periods (identifies short-term trend)
    - SMA: 20 periods (provides baseline trend confirmation)

    The strategy generates more frequent signals than standard settings and
    is designed for volatile market conditions with strong intraday movements.
    """

    def __init__(
        self,
        model_dir: Optional[str] = None,
        stop_loss_pct: float = 0.015,
        take_profit_pct: float = 0.025,
    ):
        """Initialize strategy with optimized parameters for aggressive day trading.

        Args:
            model_dir: Directory to save/load models (used by parent class)
            stop_loss_pct: Stop loss percentage (default: 1.5%)
            take_profit_pct: Take profit percentage (default: 2.5%)
        """
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct

        # Initialize base class first
        super().__init__(model_dir)

        # Set default model parameters
        default_model = {
            "indicators": {
                "fast_ema": {"window": 5},
                "medium_ema": {"window": 13},
                "sma": {"window": 20},
            },
            "parameters": {
                "stop_loss": stop_loss_pct,
                "take_profit": take_profit_pct,
            },
        }

        # Initialize model with either loaded or default parameters
        if self.model is None:
            self.model = default_model

        # Get indicator parameters
        fast_window = self.model["indicators"]["fast_ema"]["window"]
        medium_window = self.model["indicators"]["medium_ema"]["window"]
        sma_window = self.model["indicators"]["sma"]["window"]

        # Create predictors with window sizes
        self.fast_ema = EMAPredictor(window_size=fast_window)
        self.medium_ema = EMAPredictor(window_size=medium_window)
        self.trend_sma = SimpleMovingAveragePredictor(window_size=sma_window)

        # Log indicator configuration
        print(
            f"Configured aggressive day trading strategy: Fast EMA({fast_window}), Medium EMA({medium_window}), SMA({sma_window}), SL={self.stop_loss_pct*100:.2f}%, TP={self.take_profit_pct*100:.2f}%"
        )

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """Train the strategy on historical data.

        Args:
            data: DataFrame containing historical price data
            target_col: Column name for the target price series
        """
        # Train individual indicators
        self.fast_ema.train(data, target_col)
        self.medium_ema.train(data, target_col)
        self.trend_sma.train(data, target_col)

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Generate price prediction based on strategy signals.

        Args:
            data: DataFrame containing current market data

        Returns:
            np.ndarray: Array with predicted price
        """
        analysis = self.analyze(data)
        current_price = data["close"].iloc[-1]

        # Generate directional price prediction based on signal
        if analysis["signal"] == "BUY":
            predicted_price = current_price * (
                1 + self.take_profit_pct * analysis["confidence"]
            )
        elif analysis["signal"] == "SELL":
            predicted_price = current_price * (
                1 - self.take_profit_pct * analysis["confidence"]
            )
        else:
            predicted_price = current_price

        return np.array([predicted_price])

    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze market data and generate trading signals.

        Args:
            data: DataFrame with OHLCV data

        Returns:
            Dictionary with trading signals and analysis results
        """
        # Ensure we have the required data
        if len(data) < 20:  # Need at least as much data as our longest window
            return {"signal": "NEUTRAL", "confidence": 0, "reason": "Insufficient data"}

        # Calculate indicators
        fast_ema_values = self._calculate_ema(data, self.fast_ema.window_size)
        medium_ema_values = self._calculate_ema(data, self.medium_ema.window_size)
        sma_values = self._calculate_sma(data, self.trend_sma.window_size)

        # Get the latest values
        current_price = data["close"].iloc[-1]
        prev_price = data["close"].iloc[-2]

        fast_ema_current = fast_ema_values.iloc[-1]
        fast_ema_prev = fast_ema_values.iloc[-2]

        medium_ema_current = medium_ema_values.iloc[-1]
        medium_ema_prev = medium_ema_values.iloc[-2]

        sma_current = sma_values.iloc[-1]

        # Determine signal based on crossovers and trend
        signal, confidence, reason = self._generate_signal(
            current_price,
            prev_price,
            fast_ema_current,
            fast_ema_prev,
            medium_ema_current,
            medium_ema_prev,
            sma_current,
        )

        # Log detailed analysis for debugging
        print(
            f"Signal Analysis: Current Price={current_price}, Fast EMA={fast_ema_current}, Medium EMA={medium_ema_current}, SMA={sma_current}, Signal={signal}, Confidence={confidence}, Reason={reason}"
        )

        return {
            "signal": signal,
            "confidence": confidence,
            "reason": reason,
            "indicators": {
                "fast_ema": fast_ema_current,
                "medium_ema": medium_ema_current,
                "sma": sma_current,
                "price": current_price,
            },
            "risk_params": {
                "stop_loss": self._calculate_stop_loss(signal, current_price),
                "take_profit": self._calculate_take_profit(signal, current_price),
            },
        }

    def _calculate_ema(self, data: pd.DataFrame, window: int) -> pd.Series:
        """Calculate EMA for the given window size."""
        return data["close"].ewm(span=window, adjust=False).mean()

    def _calculate_sma(self, data: pd.DataFrame, window: int) -> pd.Series:
        """Calculate SMA for the given window size."""
        return data["close"].rolling(window=min(window, len(data))).mean()

    def _generate_signal(
        self,
        current_price: float,
        prev_price: float,
        fast_ema_current: float,
        fast_ema_prev: float,
        medium_ema_current: float,
        medium_ema_prev: float,
        sma_current: float,
    ) -> Tuple[str, float, str]:
        """Generate trading signal based on indicator values."""
        # Adjusted conditions for bullish crossover (fast EMA crosses above medium EMA)
        bullish_crossover = (fast_ema_prev <= medium_ema_prev) and (
            fast_ema_current > medium_ema_current
        )

        # Adjusted conditions for bearish crossover (fast EMA crosses below medium EMA)
        bearish_crossover = (fast_ema_prev >= medium_ema_prev) and (
            fast_ema_current < medium_ema_current
        )

        # Trend strength based on price vs SMA
        above_sma = current_price > sma_current
        below_sma = current_price < sma_current

        # Generate trading signals
        if bullish_crossover and above_sma:
            # Strong buy signal: Fast EMA crosses above Medium EMA and price is above SMA
            confidence = min(1.0, (current_price - sma_current) / sma_current * 100)
            return "BUY", confidence, "Bullish EMA crossover with price above SMA"
        elif bearish_crossover and below_sma:
            # Strong sell signal: Fast EMA crosses below Medium EMA and price is below SMA
            confidence = min(1.0, (sma_current - current_price) / sma_current * 100)
            return "SELL", confidence, "Bearish EMA crossover with price below SMA"
        elif fast_ema_current > medium_ema_current and above_sma:
            # Continuation of uptrend: Fast EMA remains above Medium EMA and price above SMA
            return "HOLD_LONG", 0.5, "Maintaining bullish trend"
        elif fast_ema_current < medium_ema_current and below_sma:
            # Continuation of downtrend: Fast EMA remains below Medium EMA and price below SMA
            return "HOLD_SHORT", 0.5, "Maintaining bearish trend"
        else:
            # No clear signal
            return "NEUTRAL", 0, "No clear signal"

    def _calculate_stop_loss(self, signal: str, current_price: float) -> float:
        """Calculate stop loss based on signal and current price."""
        if signal in ["BUY", "HOLD_LONG"]:
            return current_price * (1 - self.stop_loss_pct)
        elif signal in ["SELL", "HOLD_SHORT"]:
            return current_price * (1 + self.stop_loss_pct)
        return 0.0

    def _calculate_take_profit(self, signal: str, current_price: float) -> float:
        """Calculate take profit based on signal and current price."""
        if signal in ["BUY", "HOLD_LONG"]:
            return current_price * (1 + self.take_profit_pct)
        elif signal in ["SELL", "HOLD_SHORT"]:
            return current_price * (1 - self.take_profit_pct)
        return 0.0
