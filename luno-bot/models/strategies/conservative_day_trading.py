"""Conservative day trading strategy optimized for XBTMYR."""

import pandas as pd
from typing import Dict, Any, Tuple, Optional
from models.moving_average.ema import EMAPredictor
from models.moving_average.sma import SimpleMovingAveragePredictor
from ..base.predictor import PricePredictor


class ConservativeDayTradingStrategy(PricePredictor):
    """Trading strategy using triple EMA and SMA for conservative day trading.

    This strategy is optimized for XBTMYR with:
    - Fast EMA: 9 periods
    - Medium EMA: 21 periods
    - Slow EMA: 34 periods
    - SMA: 50 periods
    """

    def __init__(
        self,
        model_dir: Optional[str] = None,
        stop_loss_pct: float = 0.012,
        take_profit_pct: float = 0.020,
    ):
        """Initialize strategy with conservative parameters.

        Args:
            model_dir: Directory to save/load models (used by parent class)
            stop_loss_pct: Stop loss percentage (default: 1.2%)
            take_profit_pct: Take profit percentage (default: 2.0%)
        """
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct

        # Initialize base class
        super().__init__(model_dir)

        # Set default model parameters
        default_model = {
            "indicators": {
                "fast_ema": {"window": 9},
                "medium_ema": {"window": 21},
                "slow_ema": {"window": 34},
                "sma": {"window": 50},
            },
            "parameters": {
                "stop_loss": stop_loss_pct,
                "take_profit": take_profit_pct,
            },
        }

        # Initialize model with either loaded or default parameters
        if self.model is None:
            self.model = default_model

        # Get indicator parameters
        fast_window = self.model["indicators"]["fast_ema"]["window"]
        medium_window = self.model["indicators"]["medium_ema"]["window"]
        slow_window = self.model["indicators"]["slow_ema"]["window"]
        sma_window = self.model["indicators"]["sma"]["window"]

        # Create predictors with window sizes
        self.fast_ema = EMAPredictor(window_size=fast_window)
        self.medium_ema = EMAPredictor(window_size=medium_window)
        self.slow_ema = EMAPredictor(window_size=slow_window)
        self.trend_sma = SimpleMovingAveragePredictor(window_size=sma_window)

        # Log indicator configuration
        print(
            f"Configured conservative day trading strategy: Fast EMA({fast_window}), Medium EMA({medium_window}), Slow EMA({slow_window}), SMA({sma_window}), SL={self.stop_loss_pct*100:.2f}%, TP={self.take_profit_pct*100:.2f}%"
        )

    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze market data and generate trading signals.

        Args:
            data: DataFrame with OHLCV data

        Returns:
            Dictionary with trading signals and analysis results
        """
        # Ensure we have the required data
        if len(data) < 50:  # Need at least as much data as our longest window
            return {"signal": "NEUTRAL", "confidence": 0, "reason": "Insufficient data"}

        # Calculate indicators
        fast_ema_values = self.fast_ema.predict(data)
        medium_ema_values = self.medium_ema.predict(data)
        slow_ema_values = self.slow_ema.predict(data)
        sma_values = self.trend_sma.predict(data)

        # Get the latest values
        current_price = data["close"].iloc[-1]
        
        # Handle different return types (Series, DataFrame, or numpy array)
        if isinstance(fast_ema_values, pd.Series):
            fast_ema_current = fast_ema_values.iloc[-1]
        else:
            fast_ema_current = fast_ema_values[0]
            
        if isinstance(medium_ema_values, pd.Series):
            medium_ema_current = medium_ema_values.iloc[-1]
        else:
            medium_ema_current = medium_ema_values[0]
            
        if isinstance(slow_ema_values, pd.Series):
            slow_ema_current = slow_ema_values.iloc[-1]
        else:
            slow_ema_current = slow_ema_values[0]
            
        if isinstance(sma_values, pd.Series):
            sma_current = sma_values.iloc[-1]
        else:
            sma_current = sma_values[0]

        # Determine signal based on EMA alignment and SMA confirmation
        if (
            fast_ema_current > medium_ema_current
            and medium_ema_current > slow_ema_current
            and current_price > sma_current
        ):
            signal = "BUY"
            confidence = 0.8
            reason = "Bullish EMA alignment with price above SMA"
        elif (
            fast_ema_current < medium_ema_current
            and medium_ema_current < slow_ema_current
            and current_price < sma_current
        ):
            signal = "SELL"
            confidence = 0.8
            reason = "Bearish EMA alignment with price below SMA"
        else:
            signal = "NEUTRAL"
            confidence = 0.5
            reason = "No clear trend alignment"

        # Calculate risk parameters based on current price
        stop_loss = current_price * (1 - self.stop_loss_pct) if signal == "BUY" else 0.0
        take_profit = current_price * (1 + self.take_profit_pct) if signal == "BUY" else 0.0

        return {
            "signal": signal,
            "confidence": confidence,
            "reason": reason,
            "indicators": {
                "fast_ema": fast_ema_current,
                "medium_ema": medium_ema_current,
                "slow_ema": slow_ema_current,
                "sma": sma_current,
                "price": current_price,
            },
            "risk_params": {
                "stop_loss": stop_loss,
                "take_profit": take_profit,
            },
        }
