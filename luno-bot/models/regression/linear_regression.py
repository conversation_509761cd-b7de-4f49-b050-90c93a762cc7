"""Linear Regression prediction model."""

import numpy as np
import pandas as pd
from typing import Optional
from sklearn.linear_model import LinearRegression
import sklearn.exceptions

from ..base.predictor import PricePredictor


class LinearRegressionPredictor(PricePredictor):
    """Predictor based on Linear Regression."""

    def __init__(self, model_dir: Optional[str] = None, window_size: int = 10):
        """Initialize the Linear Regression predictor.

        Args:
            model_dir: Directory to save/load models from
            window_size: Window size for feature generation
        """
        super().__init__(model_dir)
        self.window_size = window_size
        self.model = None
        self.target_col = "close"  # Default target column

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """Train the linear regression model.

        Args:
            data: DataFrame containing market data
            target_col: Column to predict
        """
        self.target_col = target_col

        # Check if we have enough data
        if len(data) < self.window_size + 1:
            raise ValueError(
                f"Not enough data to train. Need at least {self.window_size + 1} rows."
            )

        # Prepare features and target
        X, y = self.prepare_features(data, target_col, self.window_size)

        # Create and train the model
        try:
            self.model = LinearRegression()
            self.model.fit(X, y)
        except (ValueError, sklearn.exceptions.NotFittedError) as e:
            raise ValueError(f"Error training linear regression model: {e}")

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Predict next price using trained linear regression model.

        Args:
            data: DataFrame containing market data

        Returns:
            Array with the predicted price
        """
        if self.model is None:
            raise ValueError("Model not trained. Call train() first.")

        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")

        # Check if we have enough data
        if len(data) < self.window_size:
            raise ValueError(
                f"Not enough data for prediction. Need at least {self.window_size} rows."
            )

        # Get the most recent window_size values
        recent_data = data[self.target_col].values[-self.window_size :].reshape(1, -1)

        # Make prediction
        try:
            prediction = self.model.predict(recent_data)
            return prediction
        except Exception as e:
            # Fallback to simple trend extrapolation
            last_values = data[self.target_col].values[-3:]
            avg_change = np.mean(np.diff(last_values))
            prediction = last_values[-1] + avg_change
            return np.array([prediction])
