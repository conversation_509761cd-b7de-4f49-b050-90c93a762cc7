"""
Scalping indicator module for short-term trading opportunities.

This module implements a specialized scalping indicator that identifies
very short-term price movements for quick entry and exit opportunities.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, List, Optional, Tuple

from models.base.predictor import PricePredictor

# Set up logging
logger = logging.getLogger(__name__)


class ScalpingPredictor(PricePredictor):
    """Scalping predictor that identifies short-term trading opportunities.

    This predictor uses a combination of price action, volatility, and momentum
    to identify potential scalping opportunities for very short-term trades.
    """

    def __init__(
        self,
        price_deviation_threshold: float = 0.0015,  # 0.15% price deviation
        volume_spike_threshold: float = 1.5,  # 50% above average volume
        momentum_lookback: int = 5,  # 5 candles for momentum calculation
        overbought_threshold: float = 70.0,  # RSI overbought threshold
        oversold_threshold: float = 30.0,  # RSI oversold threshold
        ema_fast_period: int = 9,  # Fast EMA period
        ema_slow_period: int = 21,  # Slow EMA period
        use_bollinger: bool = True,  # Use Bollinger Bands for signal generation
        signal_threshold: float = 0.05,  # Lowered signal threshold for easier triggering
        debug: bool = True,  # Enable debug logging
    ):
        """Initialize the scalping predictor.

        Args:
            price_deviation_threshold: Minimum price deviation to consider (as percentage)
            volume_spike_threshold: Minimum volume spike multiplier
            momentum_lookback: Number of candles to use for momentum calculation
            overbought_threshold: RSI threshold for overbought condition
            oversold_threshold: RSI threshold for oversold condition
            ema_fast_period: Fast EMA period
            ema_slow_period: Slow EMA period
            use_bollinger: Whether to use Bollinger Bands for signal generation
            signal_threshold: Threshold for generating buy/sell signals
            debug: Enable debug logging
        """
        super().__init__()
        self.price_deviation_threshold = price_deviation_threshold
        self.volume_spike_threshold = volume_spike_threshold
        self.momentum_lookback = momentum_lookback
        self.overbought_threshold = overbought_threshold
        self.oversold_threshold = oversold_threshold
        self.ema_fast_period = ema_fast_period
        self.ema_slow_period = ema_slow_period
        self.use_bollinger = use_bollinger
        self.signal_threshold = signal_threshold
        self.debug = debug

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """Train the model on the given data.

        For the scalping predictor, there isn't actual training required
        as it's using a rule-based approach. This method calculates
        missing indicators if needed.

        Args:
            data: DataFrame with market data
            target_col: Target column for prediction (unused)
        """
        self.data = data.copy()

        if self.debug:
            logger.info(f"Training on data with {len(data)} rows")
            logger.info(f"Original columns: {data.columns.tolist()}")

        # Calculate additional indicators if they don't exist
        if f"ema_{self.ema_fast_period}" not in self.data.columns:
            self.data[f"ema_{self.ema_fast_period}"] = (
                self.data["close"].ewm(span=self.ema_fast_period, adjust=False).mean()
            )

        if f"ema_{self.ema_slow_period}" not in self.data.columns:
            self.data[f"ema_{self.ema_slow_period}"] = (
                self.data["close"].ewm(span=self.ema_slow_period, adjust=False).mean()
            )

        # Add Bollinger Bands if they don't exist
        if self.use_bollinger and "bb_upper" not in self.data.columns:
            # Calculate 20-period rolling standard deviation
            rolling_std = self.data["close"].rolling(window=20).std()

            # Calculate middle band (20-period SMA)
            middle_band = self.data["close"].rolling(window=20).mean()

            # Calculate upper and lower bands
            self.data["bb_upper"] = middle_band + (rolling_std * 2)
            self.data["bb_lower"] = middle_band - (rolling_std * 2)
            self.data["bb_middle"] = middle_band

        # Calculate MACD if it doesn't exist
        if "macd" not in self.data.columns:
            # Calculate MACD line
            ema_12 = self.data["close"].ewm(span=12, adjust=False).mean()
            ema_26 = self.data["close"].ewm(span=26, adjust=False).mean()
            self.data["macd"] = ema_12 - ema_26

            # Calculate signal line
            self.data["macd_signal"] = (
                self.data["macd"].ewm(span=9, adjust=False).mean()
            )

            # Calculate histogram
            self.data["macd_hist"] = self.data["macd"] - self.data["macd_signal"]

        if self.debug:
            logger.info(
                f"Added indicators, now have columns: {self.data.columns.tolist()}"
            )

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Predict price direction from the given data.

        Args:
            data: DataFrame with market data including technical indicators

        Returns:
            Array with prediction values between -1 and 1, where:
            - Positive values indicate bullish signals (buy)
            - Negative values indicate bearish signals (sell)
            - The magnitude indicates the strength of the signal
        """
        if len(data) < 2:
            if self.debug:
                logger.warning("Not enough data for prediction (need at least 2 rows)")
            return np.array([0.0])

        # Check for required columns and provide fallbacks if missing
        required_columns = ["close", "volume"]
        if not all(col in data.columns for col in required_columns):
            if self.debug:
                logger.warning(
                    f"Missing required columns. Available: {data.columns.tolist()}"
                )
            # We need at least close and volume
            return np.array([0.0])

        # Get the most recent candles
        recent = data.iloc[-1]
        prev = data.iloc[-2]

        # Previous few candles for pattern detection
        recent_candles = data.iloc[-5:]

        # Calculate missing indicators if needed
        temp_data = data.copy()
        if f"ema_{self.ema_fast_period}" not in temp_data.columns:
            if self.debug:
                logger.info(
                    f"Calculating missing EMA-{self.ema_fast_period} on the fly"
                )
            temp_data[f"ema_{self.ema_fast_period}"] = (
                temp_data["close"].ewm(span=self.ema_fast_period, adjust=False).mean()
            )

        if f"ema_{self.ema_slow_period}" not in temp_data.columns:
            if self.debug:
                logger.info(
                    f"Calculating missing EMA-{self.ema_slow_period} on the fly"
                )
            temp_data[f"ema_{self.ema_slow_period}"] = (
                temp_data["close"].ewm(span=self.ema_slow_period, adjust=False).mean()
            )

        # Get updated recent and previous with new indicators
        if temp_data is not data:
            recent = temp_data.iloc[-1]
            prev = temp_data.iloc[-2]
            recent_candles = temp_data.iloc[-5:]

        # 1. Check price deviation
        price_delta = (recent["close"] - prev["close"]) / prev["close"]

        # 2. Check volume spike
        avg_volume = data["volume"].rolling(10).mean().iloc[-1]
        volume_ratio = recent["volume"] / avg_volume if avg_volume > 0 else 1.0
        volume_spike = volume_ratio > self.volume_spike_threshold

        # 3. Check momentum (rate of change)
        lookback_idx = max(0, len(data) - self.momentum_lookback - 1)
        lookback_price = data.iloc[lookback_idx]["close"]
        momentum = (recent["close"] - lookback_price) / lookback_price

        # 4. Check RSI for overbought/oversold conditions
        rsi_signal = 0.0
        rsi = None
        if "rsi_14" in data.columns:
            rsi = recent["rsi_14"]
        elif "RSI" in data.columns:
            # Try alternative column name
            rsi = recent["RSI"]

        if rsi is not None:
            if rsi < self.oversold_threshold:
                rsi_signal = 1.0  # Oversold, potential buy
            elif rsi > self.overbought_threshold:
                rsi_signal = -1.0  # Overbought, potential sell
            # Add gradual RSI signal for values near thresholds
            elif rsi < 40:
                rsi_signal = 0.5  # Approaching oversold
            elif rsi > 60:
                rsi_signal = -0.5  # Approaching overbought

        # 5. Check EMA crossover
        ema_signal = 0.0
        fast_col = f"ema_{self.ema_fast_period}"
        slow_col = f"ema_{self.ema_slow_period}"

        if fast_col in recent and slow_col in recent:
            # Current EMA relationship
            if recent[fast_col] > recent[slow_col]:
                # Fast above slow = bullish
                ema_signal = 0.5
            else:
                # Fast below slow = bearish
                ema_signal = -0.5

            # Check for recent crossover (stronger signal)
            if recent[fast_col] > recent[slow_col] and prev[fast_col] <= prev[slow_col]:
                # Fresh bullish crossover
                ema_signal = 1.0
            elif (
                recent[fast_col] < recent[slow_col] and prev[fast_col] >= prev[slow_col]
            ):
                # Fresh bearish crossover
                ema_signal = -1.0

        # 6. Check Bollinger Bands
        bb_signal = 0.0
        if self.use_bollinger and "bb_upper" in recent and "bb_lower" in recent:
            # Price near upper band = potential reversal/sell
            if recent["close"] > recent["bb_upper"] * 0.99:
                bb_signal = -0.7
            # Price near lower band = potential reversal/buy
            elif recent["close"] < recent["bb_lower"] * 1.01:
                bb_signal = 0.7

            # Check for Bollinger Band squeeze (low volatility)
            bb_width = (recent["bb_upper"] - recent["bb_lower"]) / recent["bb_middle"]
            avg_bb_width = pd.Series(
                [
                    (r["bb_upper"] - r["bb_lower"]) / r["bb_middle"]
                    for _, r in recent_candles.iterrows()
                ]
            ).mean()

            # Expanding volatility after squeeze
            if bb_width > avg_bb_width * 1.1:
                # Strengthen existing signal direction
                bb_signal *= 1.3

        # 7. Check MACD
        macd_signal = 0.0
        if "macd" in recent and "macd_signal" in recent:
            # MACD crossing above signal line = bullish
            if (
                recent["macd"] > recent["macd_signal"]
                and prev["macd"] <= prev["macd_signal"]
            ):
                macd_signal = 0.8
            # MACD crossing below signal line = bearish
            elif (
                recent["macd"] < recent["macd_signal"]
                and prev["macd"] >= prev["macd_signal"]
            ):
                macd_signal = -0.8

            # MACD and signal both increasing = bullish momentum
            elif (
                recent["macd"] > prev["macd"]
                and recent["macd_signal"] > prev["macd_signal"]
            ):
                macd_signal = 0.4
            # MACD and signal both decreasing = bearish momentum
            elif (
                recent["macd"] < prev["macd"]
                and recent["macd_signal"] < prev["macd_signal"]
            ):
                macd_signal = -0.4

        # 8. Check for candlestick patterns
        pattern_signal = 0.0
        # Need at least OHLC data
        if all(col in data.columns for col in ["open", "high", "low", "close"]):
            # Last 3 candles
            candles = recent_candles.iloc[-3:]

            # Check for bullish engulfing
            if (
                len(candles) >= 2
                and candles.iloc[-2]["close"]
                < candles.iloc[-2]["open"]  # Prior bearish
                and candles.iloc[-1]["close"]
                > candles.iloc[-1]["open"]  # Current bullish
                and candles.iloc[-1]["open"] < candles.iloc[-2]["close"]  # Opens lower
                and candles.iloc[-1]["close"] > candles.iloc[-2]["open"]
            ):  # Closes higher
                pattern_signal = 0.9

            # Check for bearish engulfing
            elif (
                len(candles) >= 2
                and candles.iloc[-2]["close"]
                > candles.iloc[-2]["open"]  # Prior bullish
                and candles.iloc[-1]["close"]
                < candles.iloc[-1]["open"]  # Current bearish
                and candles.iloc[-1]["open"] > candles.iloc[-2]["close"]  # Opens higher
                and candles.iloc[-1]["close"] < candles.iloc[-2]["open"]
            ):  # Closes lower
                pattern_signal = -0.9

            # Check for doji (indecision)
            elif (
                abs(candles.iloc[-1]["close"] - candles.iloc[-1]["open"])
                / (candles.iloc[-1]["high"] - candles.iloc[-1]["low"] + 0.0001)
                < 0.1
            ):
                # Doji after uptrend = bearish
                if candles.iloc[-2]["close"] > candles.iloc[-2]["open"]:
                    pattern_signal = -0.3
                # Doji after downtrend = bullish
                elif candles.iloc[-2]["close"] < candles.iloc[-2]["open"]:
                    pattern_signal = 0.3

        # Combine signals with weightings
        signal = (
            np.sign(price_delta)
            * min(abs(price_delta / self.price_deviation_threshold), 1.0)
            * 0.15  # Price action
            + (0.1 if volume_spike else 0) * np.sign(price_delta)  # Volume confirmation
            + np.sign(momentum) * min(abs(momentum * 50), 1.0) * 0.15  # Momentum
            + rsi_signal * 0.15  # RSI
            + ema_signal * 0.15  # EMA
            + bb_signal * 0.1  # Bollinger Bands
            + macd_signal * 0.1  # MACD
            + pattern_signal * 0.1  # Candlestick patterns
        )

        # Cap the signal between -1 and 1
        signal = max(-1.0, min(1.0, signal))

        if self.debug:
            logger.info("Signal components:")
            logger.info(
                f"  Price deviation: {price_delta:.4f} (signal: {np.sign(price_delta) * min(abs(price_delta / self.price_deviation_threshold), 1.0) * 0.15:.2f})"
            )
            logger.info(
                f"  Volume spike: {volume_ratio:.2f}x threshold: {self.volume_spike_threshold}x (signal: {(0.1 if volume_spike else 0) * np.sign(price_delta):.2f})"
            )
            logger.info(
                f"  Momentum: {momentum:.4f} (signal: {np.sign(momentum) * min(abs(momentum * 50), 1.0) * 0.15:.2f})"
            )
            if rsi is not None:
                logger.info(f"  RSI: {rsi:.1f} (signal: {rsi_signal * 0.15:.2f})")
            logger.info(f"  EMA: (signal: {ema_signal * 0.15:.2f})")
            logger.info(f"  Bollinger Bands: (signal: {bb_signal * 0.1:.2f})")
            logger.info(f"  MACD: (signal: {macd_signal * 0.1:.2f})")
            logger.info(f"  Pattern: (signal: {pattern_signal * 0.1:.2f})")
            logger.info(f"  Final combined signal: {signal:.4f}")

        # Return prediction
        return np.array([signal])

    def generate_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Generate trading signals based on the scalping strategy.

        Args:
            data: DataFrame with market data

        Returns:
            Dict with signal information:
            - action: 'buy', 'sell', or 'hold'
            - confidence: Signal confidence between 0 and 1
            - reason: Explanation of the signal
            - price: Current price
            - target_price: Target price for take profit
            - stop_loss: Suggested stop loss price
        """
        prediction = self.predict(data)[0]

        # Get the most recent close price
        close = data["close"].iloc[-1]

        # Default signal
        signal = {
            "action": "hold",
            "confidence": 0.0,
            "reason": "No clear scalping opportunity",
            "price": close,
            "target_price": close,
            "stop_loss": close,
        }

        # Set very tight stop loss and take profit for scalping
        # Try different ATR column names or use a default value
        atr = None
        if "atr_14" in data.columns:
            atr = data["atr_14"].iloc[-1]
        elif "ATR" in data.columns:
            atr = data["ATR"].iloc[-1]
        else:
            # Calculate approximate ATR if not available
            high_low = data["high"] - data["low"]
            high_close = abs(data["high"] - data["close"].shift())
            low_close = abs(data["low"] - data["close"].shift())

            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            atr = true_range.rolling(14).mean().iloc[-1]

            # Fallback if calculation fails
            if pd.isna(atr):
                atr = close * 0.005  # 0.5% of close price as default ATR

        # Reason components
        reason_components = []

        # Check for key indicators to include in the reason
        if "rsi_14" in data.columns:
            rsi = data["rsi_14"].iloc[-1]
            if rsi < self.oversold_threshold:
                reason_components.append(f"RSI oversold ({rsi:.1f})")
            elif rsi > self.overbought_threshold:
                reason_components.append(f"RSI overbought ({rsi:.1f})")

        # Check EMA crossover
        fast_col = f"ema_{self.ema_fast_period}"
        slow_col = f"ema_{self.ema_slow_period}"
        if fast_col in data.columns and slow_col in data.columns:
            recent = data.iloc[-1]
            prev = data.iloc[-2]
            if recent[fast_col] > recent[slow_col] and prev[fast_col] <= prev[slow_col]:
                reason_components.append(
                    f"EMA{self.ema_fast_period} crossed above EMA{self.ema_slow_period}"
                )
            elif (
                recent[fast_col] < recent[slow_col] and prev[fast_col] >= prev[slow_col]
            ):
                reason_components.append(
                    f"EMA{self.ema_fast_period} crossed below EMA{self.ema_slow_period}"
                )

        # Check volume spike
        avg_volume = data["volume"].rolling(10).mean().iloc[-1]
        volume_ratio = data["volume"].iloc[-1] / avg_volume if avg_volume > 0 else 1.0
        if volume_ratio > self.volume_spike_threshold:
            reason_components.append(f"Volume spike ({volume_ratio:.1f}x)")

        # Check for price deviation
        price_change = (data["close"].iloc[-1] - data["close"].iloc[-2]) / data[
            "close"
        ].iloc[-2]
        if abs(price_change) > self.price_deviation_threshold:
            direction = "up" if price_change > 0 else "down"
            reason_components.append(
                f"Price moved {direction} {abs(price_change)*100:.2f}%"
            )

        # Strong buy signal
        if prediction > self.signal_threshold:
            # For scalping, we use tight targets
            target_price = close * (1 + atr / close * 2)  # Target is 2x ATR
            stop_loss = close * (1 - atr / close * 0.8)  # Stop is 0.8x ATR

            buy_reason = "Bullish scalping opportunity"
            if reason_components:
                buy_reason += ": " + ", ".join(reason_components)

            signal.update(
                {
                    "action": "buy",
                    "confidence": min(abs(prediction), 1.0),
                    "reason": buy_reason,
                    "target_price": target_price,
                    "stop_loss": stop_loss,
                }
            )

            if self.debug:
                logger.info(
                    f"Generated BUY signal with confidence {min(abs(prediction), 1.0):.2f}"
                )
                logger.info(f"Reason: {buy_reason}")

        # Strong sell signal
        elif prediction < -self.signal_threshold:
            # For scalping, we use tight targets
            target_price = close * (1 - atr / close * 2)  # Target is 2x ATR
            stop_loss = close * (1 + atr / close * 0.8)  # Stop is 0.8x ATR

            sell_reason = "Bearish scalping opportunity"
            if reason_components:
                sell_reason += ": " + ", ".join(reason_components)

            signal.update(
                {
                    "action": "sell",
                    "confidence": min(abs(prediction), 1.0),
                    "reason": sell_reason,
                    "target_price": target_price,
                    "stop_loss": stop_loss,
                }
            )

            if self.debug:
                logger.info(
                    f"Generated SELL signal with confidence {min(abs(prediction), 1.0):.2f}"
                )
                logger.info(f"Reason: {sell_reason}")
        else:
            if self.debug:
                logger.info(
                    f"No trade signal generated (prediction: {prediction:.4f}, threshold: ±{self.signal_threshold})"
                )

        return signal
