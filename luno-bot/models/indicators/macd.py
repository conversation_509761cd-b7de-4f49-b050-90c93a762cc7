"""MACD prediction model."""

import numpy as np
import pandas as pd
from typing import Optional

from ..base.predictor import PricePredictor


class MACDPredictor(PricePredictor):
    """Predictor based on Moving Average Convergence Divergence (MACD)."""

    def __init__(
        self,
        model_dir: Optional[str] = None,
        fast: int = 12,
        slow: int = 26,
        signal: int = 9,
    ):
        """Initialize the MACD predictor.

        Args:
            model_dir: Directory to save/load models from
            fast: Fast EMA period
            slow: Slow EMA period
            signal: Signal line period
        """
        super().__init__(model_dir)
        self.fast = fast
        self.slow = slow
        self.signal = signal
        self.model = {"fast": fast, "slow": slow, "signal": signal}
        self.target_col = "close"  # Default target column

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """No complex training needed for MACD, just store the configuration.

        Args:
            data: DataFrame containing market data
            target_col: Column to predict
        """
        self.target_col = target_col

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Predict next price using MACD.

        Args:
            data: DataFrame containing market data

        Returns:
            Array with the predicted price
        """
        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")

        # Calculate EMAs
        fast_ema = data[self.target_col].ewm(span=self.fast, adjust=False).mean()
        slow_ema = data[self.target_col].ewm(span=self.slow, adjust=False).mean()

        # Calculate MACD line and signal line
        macd_line = fast_ema - slow_ema
        signal_line = macd_line.ewm(span=self.signal, adjust=False).mean()

        # Calculate MACD histogram
        histogram = macd_line - signal_line

        # Get the most recent values
        latest_macd = macd_line.iloc[-1]
        latest_signal = signal_line.iloc[-1]
        latest_hist = histogram.iloc[-1]
        latest_price = data[self.target_col].iloc[-1]

        # Make prediction based on MACD crossovers and histogram
        if latest_macd > latest_signal and latest_hist > 0:
            # Bullish signal: MACD above signal and histogram positive
            prediction = latest_price * 1.01  # Predict a 1% increase
        elif latest_macd < latest_signal and latest_hist < 0:
            # Bearish signal: MACD below signal and histogram negative
            prediction = latest_price * 0.99  # Predict a 1% decrease
        else:
            # Neutral signal
            prediction = latest_price  # Predict same price

        return np.array([prediction])
