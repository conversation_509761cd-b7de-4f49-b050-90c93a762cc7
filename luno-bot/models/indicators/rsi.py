"""Relative Strength Index prediction model."""

import numpy as np
import pandas as pd
from typing import Optional

from ..base.predictor import PricePredictor


class RSIPredictor(PricePredictor):
    """Predictor based on Relative Strength Index (RSI)."""

    def __init__(self, model_dir: Optional[str] = None, window_size: int = 14):
        """Initialize the RSI predictor.

        Args:
            model_dir: Directory to save/load models from
            window_size: Window size for RSI calculation
        """
        super().__init__(model_dir)
        self.window_size = window_size
        self.model = {"window_size": window_size}
        self.target_col = "close"  # Default target column

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """No complex training needed for RSI, just store the configuration.

        Args:
            data: DataFrame containing market data
            target_col: Column to predict
        """
        self.target_col = target_col

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Predict next price using RSI.

        Args:
            data: DataFrame containing market data

        Returns:
            Array with the predicted price
        """
        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")

        # Calculate price differences
        delta = data[self.target_col].diff()

        # Create copies for up and down days
        gain = delta.copy()
        loss = delta.copy()

        # Set gains and losses
        gain[gain < 0] = 0
        loss[loss > 0] = 0
        loss = abs(loss)

        # Calculate average gain and loss
        avg_gain = gain.rolling(window=self.window_size, min_periods=1).mean()
        avg_loss = loss.rolling(window=self.window_size, min_periods=1).mean()

        # Calculate RS and RSI
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        # Predict price based on RSI
        # If RSI is low, price is likely to rise (oversold)
        # If RSI is high, price is likely to fall (overbought)
        last_price = data[self.target_col].iloc[-1]
        last_rsi = rsi.iloc[-1]

        # RSI above 70 is considered overbought, below 30 is oversold
        if last_rsi > 70:
            # Overbought, predict a slight decrease
            prediction = last_price * 0.99
        elif last_rsi < 30:
            # Oversold, predict a slight increase
            prediction = last_price * 1.01
        else:
            # Neutral, predict based on the trend
            avg_price = data[self.target_col].rolling(window=3).mean().iloc[-1]
            prediction = avg_price

        return np.array([prediction])
