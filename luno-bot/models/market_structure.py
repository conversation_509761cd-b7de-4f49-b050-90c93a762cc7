"""
Market structure analysis module.

This module implements market structure analysis techniques including
identification of higher highs, higher lows, lower highs, lower lows,
and key support/resistance levels.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
import matplotlib.pyplot as plt
from dataclasses import dataclass
from datetime import datetime


@dataclass
class MarketStructurePoint:
    """Represents a significant point in market structure analysis."""

    timestamp: pd.Timestamp
    price: float
    type: str  # 'swing_high', 'swing_low', 'support', 'resistance'
    strength: float  # 0.0 to 1.0 indicating the strength of this point


class MarketStructureAnalyzer:
    """Analyzes market structure to identify key levels and patterns."""

    def __init__(
        self,
        swing_window: int = 5,
        detection_threshold: float = 0.01,
        lookback_period: int = 20,
        timeframes: Optional[List[str]] = None,
    ):
        """Initialize the market structure analyzer.

        Args:
            swing_window: Number of candles to look at for swing high/low detection
            detection_threshold: Minimum relative price change to identify swing points
            lookback_period: Number of candles to analyze for trends
            timeframes: List of timeframes to analyze (e.g., ["1m", "5m", "15m"])
        """
        self.swing_window = swing_window
        self.detection_threshold = detection_threshold
        self.lookback_period = lookback_period
        self.timeframes = timeframes if timeframes is not None else ["current"]  # Default to single timeframe
        self.market_structure_points = []
        self.timeframe_analyses = {}  # Store analyses for different timeframes

    def identify_swing_points(self, data: pd.DataFrame) -> pd.DataFrame:
        """Identify swing highs and lows in price data.

        Args:
            data: DataFrame containing OHLC price data

        Returns:
            DataFrame with added swing high/low columns
        """
        df = data.copy()

        # Initialize swing columns
        df["swing_high"] = False
        df["swing_low"] = False

        # For very small datasets, use a different approach
        if len(df) < 10:
            # Simply mark the highest high and lowest low
            if len(df) >= 3:
                max_idx = df["high"].argmax()
                min_idx = df["low"].argmin()

                df.loc[df.index[max_idx], "swing_high"] = True
                df.loc[df.index[min_idx], "swing_low"] = True

                # Add to market structure points
                self.market_structure_points.append(
                    MarketStructurePoint(
                        timestamp=df.iloc[max_idx]["timestamp"],
                        price=df.iloc[max_idx]["high"],
                        type="swing_high",
                        strength=0.7,
                    )
                )

                self.market_structure_points.append(
                    MarketStructurePoint(
                        timestamp=df.iloc[min_idx]["timestamp"],
                        price=df.iloc[min_idx]["low"],
                        type="swing_low",
                        strength=0.7,
                    )
                )
            pass

        # Need at least 2*swing_window+1 candles to identify swing points
        if len(df) < 2 * self.swing_window + 1:
            pass

        # Identify swing highs
        for i in range(self.swing_window, len(df) - self.swing_window):
            # Check if this is a local high
            window = df.iloc[i - self.swing_window : i + self.swing_window + 1]
            current_high = df.iloc[i]["high"]

            is_swing_high = True
            for j in range(len(window)):
                if j != self.swing_window and window.iloc[j]["high"] > current_high:
                    is_swing_high = False
                    break

            # Verify significant enough relative to recent price
            if is_swing_high:
                # Use ATR for dynamic threshold
                atr = df["high"].rolling(window=14).max() - df["low"].rolling(window=14).min()
                window_mean = window["close"].mean()
                atr_value = atr.iloc[i] if not pd.isna(atr.iloc[i]) else window_mean * self.detection_threshold
                threshold = atr_value

                # Check if it's above our threshold for significance
                if (current_high - min(window["low"])) > threshold:
                    df.loc[df.index[i], "swing_high"] = True
                    # Save this point for later analysis
                    self.market_structure_points.append(
                        MarketStructurePoint(
                            timestamp=df.iloc[i]["timestamp"],
                            price=current_high,
                            type="swing_high",
                            strength=min(
                                1.0,
                                (current_high - min(window["low"])) / (threshold * 5),
                            ),
                        )
                    )

        # Identify swing lows
        for i in range(self.swing_window, len(df) - self.swing_window):
            # Check if this is a local low
            window = df.iloc[i - self.swing_window : i + self.swing_window + 1]
            current_low = df.iloc[i]["low"]

            is_swing_low = True
            for j in range(len(window)):
                if j != self.swing_window and window.iloc[j]["low"] < current_low:
                    is_swing_low = False
                    break

            # Verify significant enough relative to recent price
            if is_swing_low:
                # Use ATR for dynamic threshold
                atr = df["high"].rolling(window=14).max() - df["low"].rolling(window=14).min()
                window_mean = window["close"].mean()
                atr_value = atr.iloc[i] if not pd.isna(atr.iloc[i]) else window_mean * self.detection_threshold
                threshold = atr_value

                # Check if it's below our threshold for significance
                if (max(window["high"]) - current_low) > threshold:
                    df.loc[df.index[i], "swing_low"] = True
                    # Save this point for later analysis
                    self.market_structure_points.append(
                        MarketStructurePoint(
                            timestamp=df.iloc[i]["timestamp"],
                            price=current_low,
                            type="swing_low",
                            strength=min(
                                1.0,
                                (max(window["high"]) - current_low) / (threshold * 5),
                            ),
                        )
                    )

        return df

    def detect_support_resistance(
        self, df: pd.DataFrame, n_levels: int = 3, price_tolerance: float = 0.02
    ) -> List[Dict[str, Union[float, str]]]:
        """Detect support and resistance levels using clustering.

        Args:
            df: DataFrame with identified swing points
            n_levels: Number of support/resistance levels to identify
            price_tolerance: Percentage tolerance for grouping similar price levels

        Returns:
            List of support/resistance levels with their strengths
        """
        from sklearn.cluster import KMeans

        # For small datasets, just use min/max points if we don't have swing points
        if (
            "swing_high" not in df.columns
            or "swing_low" not in df.columns
            or df["swing_high"].sum() + df["swing_low"].sum() < 2
        ):
            # If we have very limited data, use simple price range
            if len(df) >= 3:
                # Create basic levels using price range
                high_price = df["high"].max()
                low_price = df["low"].min()
                mid_price = (high_price + low_price) / 2
                current_price = df["close"].iloc[-1]

                levels = []
                # Add mid point
                levels.append(
                    {
                        "price": mid_price,
                        "count": 1,
                        "type": (
                            "support" if current_price > mid_price else "resistance"
                        ),
                        "strength": 0.5,
                    }
                )

                # Add min/max as support/resistance
                levels.append(
                    {
                        "price": high_price,
                        "count": 1,
                        "type": "resistance",
                        "strength": 0.7,
                    }
                )

                levels.append(
                    {"price": low_price, "count": 1, "type": "support", "strength": 0.7}
                )

                # Add to market structure points
                timestamp = df["timestamp"].iloc[-1]
                for level in levels:
                    self.market_structure_points.append(
                        MarketStructurePoint(
                            timestamp=timestamp,
                            price=level["price"],
                            type=level["type"],
                            strength=level["strength"],
                        )
                    )

                return levels
            else:
                return []

        # Get swing points
        swing_highs = df[df["swing_high"]]["high"].values
        swing_lows = df[df["swing_low"]]["low"].values

        # Combine all price points for clustering
        all_prices = np.concatenate([swing_highs, swing_lows]).reshape(-1, 1)

        if len(all_prices) < 2:
            return []

        # Apply K-Means clustering to group similar price levels
        n_clusters = min(n_levels, len(all_prices))
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        kmeans.fit(all_prices)
        cluster_centers = kmeans.cluster_centers_.flatten()
        labels = kmeans.labels_

        # Aggregate cluster information
        levels = []
        for i in range(n_clusters):
            cluster_prices = all_prices[labels == i]
            avg_price = cluster_centers[i]
            count = len(cluster_prices)

            levels.append(
                {
                    "price": avg_price,
                    "count": count,
                    "type": "undetermined",  # Will determine later
                }
            )

        # Sort by strength (count)
        levels.sort(key=lambda x: x["count"], reverse=True)

        # Determine if each is support or resistance based on current price
        current_price = df["close"].iloc[-1]

        for level in levels:
            if level["price"] < current_price:
                level["type"] = "support"
            else:
                level["type"] = "resistance"

            # Calculate normalized strength (0-1)
            level["strength"] = min(1.0, level["count"] / (len(df) * 0.1))

            # Add this to our market structure points
            timestamp = df["timestamp"].iloc[-1]  # Use latest timestamp
            self.market_structure_points.append(
                MarketStructurePoint(
                    timestamp=timestamp,
                    price=level["price"],
                    type=level["type"],
                    strength=level["strength"],
                )
            )

        return levels


        if len(all_prices) < 2:
            return []

        # Cluster similar price levels
        levels = []
        for price in all_prices:
            # Check if we have a similar price level already
            found = False
            for level in levels:
                if abs(level["price"] - price) / level["price"] < price_tolerance:
                    # Increase the strength of this level
                    level["count"] += 1
                    # Update price to average
                    level["price"] = (
                        level["price"] * (level["count"] - 1) + price
                    ) / level["count"]
                    found = True
                    break

            if not found:
                levels.append(
                    {
                        "price": price,
                        "count": 1,
                        "type": "undetermined",  # Will determine later
                    }
                )

        # Sort by strength (count)
        levels.sort(key=lambda x: x["count"], reverse=True)

        # Take top n_levels
        top_levels = levels[: min(n_levels, len(levels))]

        # Determine if each is support or resistance based on current price
        current_price = df["close"].iloc[-1]

        for level in top_levels:
            if level["price"] < current_price:
                level["type"] = "support"
            else:
                level["type"] = "resistance"

            # Calculate normalized strength (0-1)
            level["strength"] = min(1.0, level["count"] / (len(df) * 0.1))

            # Add this to our market structure points
            timestamp = df["timestamp"].iloc[-1]  # Use latest timestamp
            self.market_structure_points.append(
                MarketStructurePoint(
                    timestamp=timestamp,
                    price=level["price"],
                    type=level["type"],
                    strength=level["strength"],
                )
            )

        return top_levels

    def calculate_adx(self, df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """Calculate Average Directional Index (ADX) for trend strength.

        Args:
            df: DataFrame with OHLC price data
            period: Period for ADX calculation

        Returns:
            DataFrame with added ADX column
        """
        # Make a copy to avoid modifying the original dataframe
        df_adx = df.copy()
        
        # Calculate True Range (TR)
        df_adx['tr1'] = abs(df_adx['high'] - df_adx['low'])
        df_adx['tr2'] = abs(df_adx['high'] - df_adx['close'].shift())
        df_adx['tr3'] = abs(df_adx['low'] - df_adx['close'].shift())
        df_adx['tr'] = df_adx[['tr1', 'tr2', 'tr3']].max(axis=1)
        
        # Calculate +DM and -DM
        df_adx['+dm'] = df_adx['high'].diff()
        df_adx['-dm'] = df_adx['low'].shift() - df_adx['low']
        df_adx['+dm'] = df_adx['+dm'].where((df_adx['+dm'] > 0) & (df_adx['+dm'] > df_adx['-dm']), 0)
        df_adx['-dm'] = df_adx['-dm'].where((df_adx['-dm'] > 0) & (df_adx['-dm'] > df_adx['+dm']), 0)
        
        # Smooth TR, +DM, and -DM using EMA
        df_adx['smoothed_tr'] = df_adx['tr'].rolling(window=period).mean()
        df_adx['smoothed_+dm'] = df_adx['+dm'].rolling(window=period).mean()
        df_adx['smoothed_-dm'] = df_adx['-dm'].rolling(window=period).mean()
        
        # Calculate +DI and -DI
        df_adx['+di'] = (df_adx['smoothed_+dm'] / df_adx['smoothed_tr']) * 100
        df_adx['-di'] = (df_adx['smoothed_-dm'] / df_adx['smoothed_tr']) * 100
        
        # Calculate DX
        df_adx['dx'] = (abs(df_adx['+di'] - df_adx['-di']) / (abs(df_adx['+di'] + df_adx['-di']) + 0.000001)) * 100
        
        # Calculate ADX
        df_adx['adx'] = df_adx['dx'].rolling(window=period).mean()
        
        return df_adx
    
    def identify_market_trend(
        self, df: pd.DataFrame, window: int = 20
    ) -> Tuple[str, float]:
        """Identify the current market trend and its strength.

        Args:
            df: DataFrame with price data
            window: Window size for trend calculation

        Returns:
            Tuple of (trend, strength) where trend is 'uptrend', 'downtrend', or 'sideways'
        """
        # Adjust window size if we don't have enough data
        if len(df) < window:
            if len(df) < 3:  # Need at least 3 points for minimal trend analysis
                return "unknown", 0.0
            # Use whatever data we have, with a minimum of 3 points
            window = max(3, len(df))

        # Get recent candles for trend analysis
        recent_df = df.tail(window)

        # Calculate ADX for trend strength
        adx_df = self.calculate_adx(recent_df)
        adx_value = adx_df['adx'].iloc[-1] if not pd.isna(adx_df['adx'].iloc[-1]) else 0
        
        # Calculate linear regression on close prices
        x = np.arange(len(recent_df))
        y = recent_df["close"].values

        # Simple linear regression
        slope, intercept = np.polyfit(x, y, 1)

        # Calculate R-squared to determine trend strength
        p = np.poly1d([slope, intercept])
        y_pred = p(x)

        # Mean of the observed data
        y_mean = np.mean(y)

        # Total sum of squares
        ss_tot = np.sum((y - y_mean) ** 2)

        # Residual sum of squares
        ss_res = np.sum((y - y_pred) ** 2)

        # R-squared
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

        # Determine trend using both linear regression and ADX
        # ADX > 25 indicates strong trend, > 20 moderate trend
        adx_strength = min(1.0, adx_value / 50.0)  # Normalize ADX to 0-1 range
        trend_strength = min(1.0, abs(slope) * len(y) / y_mean) if y_mean != 0 else 0
        
        # Combine linear regression trend strength with ADX
        combined_strength = (trend_strength + adx_strength) / 2

        # Lower the threshold for smaller datasets
        threshold = 0.005 if len(df) < 10 else 0.01

        # Use ADX to filter out weak trends
        if slope > 0 and combined_strength > threshold and adx_value > 20:
            trend = "uptrend"
        elif slope < 0 and combined_strength > threshold and adx_value > 20:
            trend = "downtrend"
        else:
            trend = "sideways"

        return trend, combined_strength

    def detect_market_structure_patterns(self, df: pd.DataFrame) -> Dict[str, bool]:
        """Analyze latest market structure patterns.

        Args:
            df: DataFrame with price data

        Returns:
            Dict with structure pattern flags (e.g., higher_highs, higher_lows)
        """
        if len(df) < 5:
            return {
                "higher_highs": False,
                "higher_lows": False,
                "lower_highs": False,
                "lower_lows": False,
            }

        # Get swing points
        if "swing_high" not in df.columns or "swing_low" not in df.columns:
            df = self.identify_swing_points(df)

        swing_highs = df[df["swing_high"]].copy()
        swing_lows = df[df["swing_low"]].copy()

        result = {
            "higher_highs": False,
            "higher_lows": False,
            "lower_highs": False,
            "lower_lows": False,
        }

        # Need at least 2 swing points to determine the pattern
        if len(swing_highs) >= 2:
            # Sort by timestamp
            swing_highs = swing_highs.sort_values("timestamp")

            # Check the last two swing highs
            last_high = swing_highs.iloc[-1]["high"]
            prev_high = swing_highs.iloc[-2]["high"]

            if last_high > prev_high:
                result["higher_highs"] = True
            elif last_high < prev_high:
                result["lower_highs"] = True

        if len(swing_lows) >= 2:
            # Sort by timestamp
            swing_lows = swing_lows.sort_values("timestamp")

            # Check the last two swing lows
            last_low = swing_lows.iloc[-1]["low"]
            prev_low = swing_lows.iloc[-2]["low"]

            if last_low > prev_low:
                result["higher_lows"] = True
            elif last_low < prev_low:
                result["lower_lows"] = True

        return result

    def detect_breakouts(self, df: pd.DataFrame, tolerance: float = 0.03) -> List[Dict]:
        """Detect potential breakouts of support/resistance levels.

        Args:
            df: DataFrame with identified market structure
            tolerance: Percentage beyond level to confirm breakout

        Returns:
            List of detected breakouts
        """
        if not self.market_structure_points:
            return []

        current_price = df["close"].iloc[-1]
        
        # Volume analysis for confirmation
        has_volume = "volume" in df.columns
        recent_volume = df["volume"].iloc[-5:].mean() if has_volume else 1.0
        avg_volume = df["volume"].mean() if has_volume else 1.0
        volume_spike = (recent_volume / avg_volume > 1.5) if has_volume else False
        
        # Volatility analysis for confirmation
        if len(df) >= 20:
            recent_volatility = df["high"].iloc[-5:].max() - df["low"].iloc[-5:].min()
            avg_volatility = (
                (df["high"] - df["low"]).rolling(window=20).mean().iloc[-1]
                if len(df) >= 20
                else (df["high"] - df["low"]).mean()
            )
            volatility_expansion = recent_volatility > (avg_volatility * 1.2)
        else:
            volatility_expansion = False

        breakouts = []

        # Check each support/resistance level for breakouts
        for point in self.market_structure_points:
            if point.type not in ["support", "resistance"]:
                continue

            # Calculate percentage difference from level
            price_diff_pct = (current_price - point.price) / point.price
            
            # Distance from the level in ATR units (if available)
            atr = df["high"].rolling(window=14).max() - df["low"].rolling(window=14).min() 
            atr_value = atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else df["close"].iloc[-1] * 0.01
            price_diff_in_atr = abs(current_price - point.price) / atr_value

            # Check for breakout conditions with enhanced criteria
            is_breakout = False
            
            if point.type == "resistance" and price_diff_pct > tolerance:
                is_breakout = True
                # Check for false breakout patterns (quick reversal)
                if len(df) >= 3 and df["close"].iloc[-1] < df["close"].iloc[-2]:
                    # Price closing lower than the previous candle suggests potential false breakout
                    is_breakout = False
            elif point.type == "support" and price_diff_pct < -tolerance:
                is_breakout = True
                # Check for false breakout patterns (quick reversal)
                if len(df) >= 3 and df["close"].iloc[-1] > df["close"].iloc[-2]:
                    # Price closing higher than the previous candle suggests potential false breakout
                    is_breakout = False
            
            if is_breakout:
                # Calculate confirmation factors
                volume_factor = recent_volume / avg_volume if avg_volume > 0 else 1.0
                volatility_factor = 1.2 if volatility_expansion else 0.9
                distance_factor = min(1.5, max(0.8, price_diff_in_atr / 2))  # Normalize based on ATR
                
                # Combined confirmation strength
                confirmation_strength = (
                    (volume_factor * 0.4) +  # 40% weight to volume
                    (volatility_factor * 0.3) +  # 30% weight to volatility
                    (distance_factor * 0.3)  # 30% weight to distance in ATR
                )
                
                breakouts.append(
                    {
                        "level": point.price,
                        "type": point.type,
                        "direction": "up" if price_diff_pct > 0 else "down",
                        "strength": point.strength * confirmation_strength,
                        "timestamp": df["timestamp"].iloc[-1],
                        "is_confirmed": volume_spike and volatility_expansion,
                        "volume_factor": volume_factor,
                        "volatility_factor": volatility_factor
                    }
                )

        return breakouts

    def analyze_market_structure(self, data: pd.DataFrame, timeframe: str = "current") -> Dict:
        """Complete market structure analysis.

        Args:
            data: DataFrame with OHLC data
            timeframe: Timeframe identifier for this analysis

        Returns:
            Dictionary with structure analysis results
        """
        # Reset points from previous analyses
        self.market_structure_points = []

        # Validate data
        if len(data) < 3 or "timestamp" not in data.columns:
            return {
                "trend": "unknown",
                "structure": "insufficient_data",
                "higher_highs": False,
                "higher_lows": False,
                "lower_highs": False,
                "lower_lows": False,
                "support_levels": [],
                "resistance_levels": [],
                "key_level": 0,
                "recent_break": None,
                "adx": 0.0,
                "timeframe": timeframe,
            }

        # Identify swing points
        df_with_swings = self.identify_swing_points(data)

        # Identify market trend
        trend, trend_strength = self.identify_market_trend(
            df_with_swings, window=min(self.lookback_period, len(data))
        )

        # Detect support and resistance levels
        sr_levels = self.detect_support_resistance(df_with_swings)

        # Organize into support and resistance lists with strengths
        support_levels = [
            (level["price"], level["strength"])
            for level in sr_levels
            if level["type"] == "support"
        ]
        resistance_levels = [
            (level["price"], level["strength"])
            for level in sr_levels
            if level["type"] == "resistance"
        ]

        # Detect structure patterns (higher highs, higher lows, etc.)
        structure_patterns = self.detect_market_structure_patterns(df_with_swings)

        # Detect breakouts
        breakouts = self.detect_breakouts(df_with_swings)

        # Determine structure type
        structure = "undefined"
        if structure_patterns["higher_highs"] and structure_patterns["higher_lows"]:
            structure = "strong_uptrend"
        elif structure_patterns["lower_highs"] and structure_patterns["lower_lows"]:
            structure = "strong_downtrend"
        elif (
            structure_patterns["higher_lows"] and not structure_patterns["lower_highs"]
        ):
            structure = "accumulation"
        elif (
            structure_patterns["lower_highs"] and not structure_patterns["higher_lows"]
        ):
            structure = "distribution"
        elif trend == "uptrend":
            structure = "uptrend"
        elif trend == "downtrend":
            structure = "downtrend"
        else:
            structure = "range"

        # Identify the key level (strongest support or resistance)
        current_price = data["close"].iloc[-1]
        key_level = 0

        # Select closest strong level above or below price
        if current_price > data["close"].iloc[-2]:  # Price is moving up
            # Find closest resistance
            if resistance_levels:
                closest_level = min(
                    resistance_levels, key=lambda x: abs(x[0] - current_price)
                )
                key_level = closest_level[0]
        else:  # Price is moving down
            # Find closest support
            if support_levels:
                closest_level = min(
                    support_levels, key=lambda x: abs(x[0] - current_price)
                )
                key_level = closest_level[0]

        # If we have a recent breakout, include it
        recent_break = None
        confirmed_breakouts = [b for b in breakouts if b.get("is_confirmed", False)]
        
        # Prefer confirmed breakouts, but fall back to any if none are confirmed
        if confirmed_breakouts:
            # Get the strongest confirmed breakout
            strongest_breakout = max(confirmed_breakouts, key=lambda x: x["strength"])
            recent_break = strongest_breakout
        elif breakouts:
            # Get the strongest breakout if no confirmed ones
            strongest_breakout = max(breakouts, key=lambda x: x["strength"])
            recent_break = strongest_breakout

        # Calculate ADX for trend strength analysis
        adx_df = self.calculate_adx(data)
        adx_value = adx_df['adx'].iloc[-1] if not pd.isna(adx_df['adx'].iloc[-1]) else 0
        
        analysis_result = {
            "trend": trend,
            "structure": structure,
            "higher_highs": structure_patterns["higher_highs"],
            "higher_lows": structure_patterns["higher_lows"],
            "lower_highs": structure_patterns["lower_highs"],
            "lower_lows": structure_patterns["lower_lows"],
            "support_levels": support_levels,
            "resistance_levels": resistance_levels,
            "key_level": key_level,
            "recent_break": recent_break,
            "adx": adx_value,
            "timeframe": timeframe,
        }
        
        # Store the analysis result for this timeframe
        self.timeframe_analyses[timeframe] = analysis_result
        
        return analysis_result
        
    def analyze_multiple_timeframes(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """Analyze multiple timeframes and aggregate the results.
        
        Args:
            data_dict: Dictionary of {timeframe: dataframe} pairs
            
        Returns:
            Dictionary of analysis results per timeframe and an aggregated result
        """
        # Clear previous analyses
        self.timeframe_analyses = {}
        
        # Analyze each timeframe
        for timeframe, df in data_dict.items():
            self.analyze_market_structure(df, timeframe)
            
        # Create aggregated analysis
        if self.timeframe_analyses:
            # Default weights by timeframe (higher weight for longer timeframes)
            timeframe_weights = {
                "1m": 0.2,
                "5m": 0.3,
                "15m": 0.5,
                "30m": 0.7,
                "1h": 0.8,
                "4h": 0.9,
                "1d": 1.0,
            }
            
            # Count trend votes weighted by timeframe
            trend_votes = {"uptrend": 0.0, "downtrend": 0.0, "sideways": 0.0}
            structure_votes = {
                "strong_uptrend": 0.0, 
                "uptrend": 0.0,
                "strong_downtrend": 0.0, 
                "downtrend": 0.0,
                "accumulation": 0.0,
                "distribution": 0.0,
                "range": 0.0,
                "undefined": 0.0
            }
            
            for tf, analysis in self.timeframe_analyses.items():
                # Get weight for this timeframe (default to 0.5 if not found)
                weight = timeframe_weights.get(tf, 0.5)
                
                # Add weighted votes
                trend = analysis["trend"]
                if trend in trend_votes:
                    trend_votes[trend] += weight
                
                structure = analysis["structure"]
                if structure in structure_votes:
                    structure_votes[structure] += weight
            
            # Find strongest trend and structure
            dominant_trend = max(trend_votes.items(), key=lambda x: x[1])[0]
            dominant_structure = max(structure_votes.items(), key=lambda x: x[1])[0]
            
            # Aggregate support and resistance levels across timeframes
            all_supports = []
            all_resistances = []
            
            for tf, analysis in self.timeframe_analyses.items():
                # Add weight based on timeframe
                weight = timeframe_weights.get(tf, 0.5)
                
                # Add weighted support and resistance levels
                for level, strength in analysis["support_levels"]:
                    all_supports.append((level, strength * weight))
                
                for level, strength in analysis["resistance_levels"]:
                    all_resistances.append((level, strength * weight))
            
            # Aggregate ADX (average across timeframes)
            avg_adx = sum(a["adx"] for a in self.timeframe_analyses.values()) / len(self.timeframe_analyses)
            
            # Create aggregated analysis
            aggregated = {
                "trend": dominant_trend,
                "structure": dominant_structure,
                "support_levels": sorted(all_supports, key=lambda x: x[1], reverse=True)[:5],  # Top 5 support levels
                "resistance_levels": sorted(all_resistances, key=lambda x: x[1], reverse=True)[:5],  # Top 5 resistance levels
                "higher_highs": any(a["higher_highs"] for a in self.timeframe_analyses.values()),
                "higher_lows": any(a["higher_lows"] for a in self.timeframe_analyses.values()),
                "lower_highs": any(a["lower_highs"] for a in self.timeframe_analyses.values()),
                "lower_lows": any(a["lower_lows"] for a in self.timeframe_analyses.values()),
                "adx": avg_adx,
                "timeframe": "aggregated",
                "analyzed_timeframes": list(self.timeframe_analyses.keys()),
            }
            
            # Add the aggregated result to the analyses
            self.timeframe_analyses["aggregated"] = aggregated
        
        return self.timeframe_analyses


class MarketStructurePredictor:
    """Generates trading signals based on market structure analysis."""

    def __init__(
        self,
        lookback_period: int = 20,
        swing_threshold: float = 0.005,
        confidence_threshold: float = 0.7,
        timeframes: Optional[List[str]] = None,
    ):
        """Initialize the market structure predictor.

        Args:
            lookback_period: Number of candles to analyze for trends
            swing_threshold: Threshold for identifying significant price swings
            confidence_threshold: Minimum confidence needed for generating signals
            timeframes: List of timeframes to analyze (e.g., ["1m", "5m", "15m"])
        """
        self.lookback_period = lookback_period
        self.swing_threshold = swing_threshold
        self.confidence_threshold = confidence_threshold
        self.timeframes = timeframes if timeframes is not None else ["current"]  # Default to single timeframe
        self.analyzer = MarketStructureAnalyzer(
            swing_window=min(5, lookback_period // 4),
            detection_threshold=swing_threshold,
            lookback_period=lookback_period,
            timeframes=self.timeframes,
        )
        # Timeframe weights for signal generation (higher weight for longer timeframes)
        self.timeframe_weights = {
            "1m": 0.3,
            "5m": 0.4,
            "15m": 0.5,
            "30m": 0.7,
            "1h": 0.8,
            "4h": 0.9,
            "1d": 1.0,
            "current": 0.6,  # Default weight
        }

    def _calculate_signal_confidence(
        self, pattern: str, strength: float, volume_factor: float = 1.0
    ) -> float:
        """Calculate confidence level for a signal based on pattern strength.

        Args:
            pattern: The market structure pattern
            strength: Raw pattern strength (0-1)
            volume_factor: Volume confirmation factor

        Returns:
            Confidence level (0-1)
        """
        # Base confidence on pattern strength
        base_confidence = strength

        # Adjust based on pattern type
        pattern_multipliers = {
            "strong_uptrend": 1.2,
            "strong_downtrend": 1.2,
            "accumulation": 0.9,
            "distribution": 0.9,
            "uptrend": 0.8,
            "downtrend": 0.8,
            "range": 0.6,
            "undefined": 0.5,
        }

        multiplier = pattern_multipliers.get(pattern, 0.7)

        # Apply volume confirmation
        confidence = base_confidence * multiplier * volume_factor

        # Cap at 1.0
        return min(1.0, confidence)

    def _detect_momentum(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Detect price momentum for signal confirmation.

        Args:
            data: DataFrame with OHLC data

        Returns:
            Tuple of (direction, strength)
        """
        if len(data) < 3:
            return "neutral", 0.0

        # Use simple momentum calculation
        short_term = data["close"].tail(3).pct_change().mean()
        medium_term = (
            data["close"].tail(7).pct_change().mean() if len(data) >= 7 else short_term
        )

        # Combined momentum
        momentum = short_term * 0.7 + medium_term * 0.3

        if momentum > 0.002:  # Positive momentum threshold
            return "bullish", min(
                1.0, momentum * 200
            )  # Scale up for easier interpretation
        elif momentum < -0.002:  # Negative momentum threshold
            return "bearish", min(1.0, abs(momentum) * 200)
        else:
            return "neutral", abs(momentum) * 100  # Weak momentum

    def generate_signal(self, data: Optional[Dict[str, pd.DataFrame]] = None, single_data: Optional[pd.DataFrame] = None) -> Dict:
        """Generate trading signal based on market structure analysis.

        Args:
            data: Dictionary of {timeframe: dataframe} pairs for multi-timeframe analysis
            single_data: Single DataFrame with OHLC data for single timeframe analysis

        Returns:
            Dictionary with signal info (action, confidence, reason)
        """
        # Default signal is hold
        signal = {"action": "hold", "confidence": 0.0, "reason": "insufficient data"}

        # Check if we have enough data
        if data is None and single_data is None:
            return signal
        
        # Multi-timeframe or single timeframe mode
        analyses = {}
        analysis = None
        analysis_data = None
        
        if data is not None and len(data) > 0:
            # Multi-timeframe analysis
            analyses = self.analyzer.analyze_multiple_timeframes(data)
            # Use the aggregated analysis
            analysis = analyses.get("aggregated", {})
            # Get the shortest timeframe data for volume and momentum
            shortest_tf = min(data.keys(), key=lambda x: self.timeframe_weights.get(x, 0.5))
            analysis_data = data[shortest_tf]
        elif single_data is not None:
            # Single timeframe analysis
            if len(single_data) < 5:
                return signal
            analysis = self.analyzer.analyze_market_structure(single_data)
            analysis_data = single_data
        else:
            return signal  # No valid data

        # Get volume factor if available
        volume_factor = 1.0
        if analysis_data is not None and "volume" in analysis_data.columns:
            recent_volume = analysis_data["volume"].tail(3).mean()
            avg_volume = analysis_data["volume"].mean()
            if avg_volume > 0:
                volume_factor = min(1.5, max(0.7, recent_volume / avg_volume))

        # Get current momentum
        momentum_direction, momentum_strength = self._detect_momentum(analysis_data) if analysis_data is not None else ("neutral", 0.0)

        # Current price
        current_price = analysis_data["close"].iloc[-1] if analysis_data is not None else 0.0
        
        # Get trend strength from ADX
        adx_value = analysis.get("adx", 0.0) if analysis is not None else 0.0
        trend_is_strong = adx_value > 25  # ADX > 25 indicates a strong trend

        # Generate signal based on structure and momentum
        structure = analysis.get("structure", "undefined") if analysis is not None else "undefined"
        
        # Multi-timeframe alignment bonus (only in multi-timeframe mode)
        alignment_bonus = 1.0
        if data is not None and len(data) > 0 and analyses:
            # Check if trends align across timeframes
            timeframe_count = len(data)
            if timeframe_count > 1:
                uptrend_count = sum(1 for tf, a in analyses.items() if tf != "aggregated" and a.get("trend") == "uptrend")
                downtrend_count = sum(1 for tf, a in analyses.items() if tf != "aggregated" and a.get("trend") == "downtrend")
                
                # Calculate alignment percentage
                uptrend_alignment = uptrend_count / (timeframe_count - 1) if timeframe_count > 1 else 0  # Exclude aggregated
                downtrend_alignment = downtrend_count / (timeframe_count - 1) if timeframe_count > 1 else 0
                
                # Apply bonus when highly aligned (at least 70%)
                if uptrend_alignment > 0.7 and analysis is not None and analysis.get("trend") == "uptrend":
                    alignment_bonus = 1.0 + (uptrend_alignment - 0.7) * 0.6  # Up to 18% bonus for perfect alignment
                elif downtrend_alignment > 0.7 and analysis is not None and analysis.get("trend") == "downtrend":
                    alignment_bonus = 1.0 + (downtrend_alignment - 0.7) * 0.6

        if analysis is not None and structure == "strong_uptrend" and momentum_direction in [
            "bullish",
            "neutral",
        ]:
            signal = {
                "action": "buy",
                "confidence": self._calculate_signal_confidence(
                    structure, 0.8, volume_factor
                ),
                "reason": "strong uptrend (higher highs and higher lows)",
            }
        elif analysis is not None and structure == "strong_downtrend" and momentum_direction in [
            "bearish",
            "neutral",
        ]:
            signal = {
                "action": "sell",
                "confidence": self._calculate_signal_confidence(
                    structure, 0.8, volume_factor
                ),
                "reason": "strong downtrend (lower highs and lower lows)",
            }
        elif analysis is not None and structure == "accumulation" and momentum_direction == "bullish":
            signal = {
                "action": "buy",
                "confidence": self._calculate_signal_confidence(
                    structure, 0.7, volume_factor
                ),
                "reason": "accumulation pattern (higher lows)",
            }
        elif analysis is not None and structure == "distribution" and momentum_direction == "bearish":
            signal = {
                "action": "sell",
                "confidence": self._calculate_signal_confidence(
                    structure, 0.7, volume_factor
                ),
                "reason": "distribution pattern (lower highs)",
            }
        if analysis is not None and analysis.get("recent_break"):
            breakout = analysis["recent_break"]
            is_confirmed = breakout.get("is_confirmed", False)
            confirmation_text = " confirmed with volume and volatility" if is_confirmed else ""
                
            if breakout["direction"] == "up" and breakout["type"] == "resistance":
                # Adjust confidence based on confirmation
                confidence_modifier = 1.2 if is_confirmed else 1.0
                signal = {
                    "action": "buy",
                    "confidence": min(0.95, breakout["strength"] * volume_factor * confidence_modifier),
                    "reason": f"breakout above resistance at {breakout['level']:.2f}{confirmation_text}",
                }
            elif breakout["direction"] == "down" and breakout["type"] == "support":
                # Adjust confidence based on confirmation
                confidence_modifier = 1.2 if is_confirmed else 1.0
                signal = {
                    "action": "sell",
                    "confidence": min(0.95, breakout["strength"] * volume_factor * confidence_modifier),
                    "reason": f"breakdown below support at {breakout['level']:.2f}{confirmation_text}",
                }
        # Support/resistance bounces
        elif analysis is not None and analysis.get("support_levels"):
            # Find closest support
            closest_support = min(
                analysis["support_levels"], key=lambda x: abs(x[0] - current_price)
            )

            # Check if price is near support
            if abs(current_price - closest_support[0]) / current_price < 0.02:
                signal = {
                    "action": "buy",
                    "confidence": min(0.7, closest_support[1] * volume_factor),
                    "reason": f"price at support level {closest_support[0]:.2f}",
                }
        elif analysis is not None and analysis.get("resistance_levels"):
            # Find closest resistance
            closest_resistance = min(
                analysis["resistance_levels"], key=lambda x: abs(x[0] - current_price)
            )

            # Check if price is near resistance
            if abs(current_price - closest_resistance[0]) / current_price < 0.02:
                signal = {
                    "action": "sell",
                    "confidence": min(0.7, closest_resistance[1] * volume_factor),
                    "reason": f"price at resistance level {closest_resistance[0]:.2f}",
                }
        else:
            # Use trend as a default signal, considering ADX strength
            if analysis is not None and analysis.get("trend") == "uptrend" and momentum_direction != "bearish":
                adx_factor = 1.0 + (0.5 * (adx_value / 50.0))  # Boost confidence for strong trends
                signal = {
                    "action": "buy",
                    "confidence": 0.6 * volume_factor * adx_factor,
                    "reason": f"price in {('strong ' if trend_is_strong else '')}uptrend (ADX: {adx_value:.1f})",
                }
            elif analysis is not None and analysis.get("trend") == "downtrend" and momentum_direction != "bullish":
                adx_factor = 1.0 + (0.5 * (adx_value / 50.0))  # Boost confidence for strong trends
                signal = {
                    "action": "sell",
                    "confidence": 0.6 * volume_factor * adx_factor,
                    "reason": f"price in {('strong ' if trend_is_strong else '')}downtrend (ADX: {adx_value:.1f})",
                }
            else:
                signal = {
                    "action": "hold",
                    "confidence": 0.7,
                    "reason": "no clear market structure signal",
                }

        # Apply confidence threshold
        if signal["confidence"] < self.confidence_threshold:
            signal = {
                "action": "hold",
                "confidence": max(0.5, signal["confidence"]),
                "reason": f"low confidence ({signal['confidence']:.2f}) for {signal['action']} signal: {signal['reason']}",
            }

        # Apply multi-timeframe alignment bonus
        if "alignment_bonus" in locals() and alignment_bonus > 1.0:
            old_confidence = signal["confidence"]
            signal["confidence"] = min(0.95, signal["confidence"] * alignment_bonus)
            
            # Add alignment info to the reason if it made a difference
            if signal["confidence"] > old_confidence and signal["action"] != "hold":
                signal["reason"] += f" (multi-timeframe aligned: +{(alignment_bonus-1)*100:.0f}%)"

        # Add timestamp and additional info
        signal["timestamp"] = datetime.now()
        signal["timeframes"] = self.timeframes if hasattr(self, 'timeframes') else ["current"]
        
        # Add which timeframes were most influential in the decision
        if data is not None and signal["action"] != "hold" and analyses:
            if signal["action"] == "buy":
                supporting_timeframes = [
                    tf for tf, a in analyses.items() 
                    if tf != "aggregated" and (a.get("trend") == "uptrend" or a.get("structure") in ["strong_uptrend", "uptrend", "accumulation"])
                ]
            else:  # sell
                supporting_timeframes = [
                    tf for tf, a in analyses.items() 
                    if tf != "aggregated" and (a.get("trend") == "downtrend" or a.get("structure") in ["strong_downtrend", "downtrend", "distribution"])
                ]
            
            if supporting_timeframes:
                signal["supporting_timeframes"] = supporting_timeframes

        return signal
