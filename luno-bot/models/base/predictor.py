"""
Base predictor class for price prediction models.

This module defines the PricePredictor base class that all
prediction models should inherit from.
"""

import os
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any, Tuple
from pathlib import Path
import pickle


class PricePredictor:
    """Base class for price prediction models."""

    def __init__(self, model_dir: Optional[str] = None):
        """Initialize the predictor.

        Args:
            model_dir: Directory to save/load models from. Defaults to 'models'.
        """
        self.model_dir = Path(model_dir or "models")
        self.model_dir.mkdir(exist_ok=True, parents=True)
        self.model = None

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data before training or prediction.

        Args:
            data: DataFrame containing market data

        Returns:
            Preprocessed DataFrame
        """
        # Basic preprocessing - override in subclasses for more complex logic
        df = data.copy()

        # Drop any NaN values
        df = df.dropna()

        # Drop non-numeric columns except timestamp
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        if "timestamp" in df.columns:
            numeric_cols.append("timestamp")
        df = df[numeric_cols]

        return df

    def prepare_features(
        self, data: pd.DataFrame, target_col: str = "close", window_size: int = 10
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features and targets for ML model.

        Args:
            data: Preprocessed DataFrame
            target_col: Column to predict
            window_size: Number of time steps to use as features

        Returns:
            Tuple of (X, y) where X is features and y is targets
        """
        df = data.copy()

        # Create sequences of window_size time steps
        X = []
        y = []

        for i in range(len(df) - window_size):
            X.append(df.iloc[i : i + window_size][target_col].values)
            y.append(df.iloc[i + window_size][target_col])

        return np.array(X), np.array(y)

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """Train the model on market data.

        This is a placeholder. Subclasses should implement this method.

        Args:
            data: DataFrame containing market data
            target_col: Column to predict
        """
        raise NotImplementedError("Subclasses must implement train method")

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Make predictions using the trained model.

        This is a placeholder. Subclasses should implement this method.

        Args:
            data: DataFrame containing market data

        Returns:
            Array of predictions
        """
        raise NotImplementedError("Subclasses must implement predict method")

    def save_model(self, filename: str) -> str:
        """Save the trained model to disk.

        Args:
            filename: Name of the file to save the model to

        Returns:
            Path to the saved model
        """
        if self.model is None:
            raise ValueError("No model to save. Train the model first.")

        filepath = self.model_dir / filename

        with open(filepath, "wb") as f:
            pickle.dump(self.model, f)

        return str(filepath)

    def load_model(self, filepath: str) -> None:
        """Load a trained model from disk.

        Args:
            filepath: Path to the saved model
        """
        with open(filepath, "rb") as f:
            self.model = pickle.load(f)
