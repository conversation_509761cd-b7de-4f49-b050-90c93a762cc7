# Conservative Live Trading Mode

This document provides detailed information about using the `run_conservative_live.py` script for executing real trades with the conservative day trading strategy on the Luno exchange.

## ⚠️ IMPORTANT WARNING ⚠️

**This script is designed to execute REAL trades with REAL money on your Luno account.**

- All dry-run settings will be ignored
- Actual funds will be used for trading
- Stop loss and take profit mechanisms are in place, but market volatility can cause losses
- Start with small amounts until you're confident in the system

## Requirements

Before using this script, ensure you have:

1. A Luno account with API access
2. Sufficient balance for the trading pair you wish to use
3. API credentials stored in a `.env` file in the project root:
   ```
   LUNO_API_KEY=your_api_key_here
   LUNO_API_SECRET=your_api_secret_here
   ```
4. Python 3.7+ with all dependencies installed (see `requirements.txt`)

## Usage

### Basic Usage

Run the script with default settings:

```bash
./run_conservative_live.py
```

This will:
- Use the XBTZAR trading pair
- Set a 1.2% stop loss
- Set a 2.0% take profit
- Trade with an amount of 0.001
- Check for trading opportunities every 15 minutes (900 seconds)

### Custom Parameters

Customize the trading behavior:

```bash
./run_conservative_live.py --pair ETHZAR --stop-loss 0.015 --take-profit 0.025 --amount 0.01 --interval 1800
```

### Skipping Confirmation

For automated deployments, you can skip the confirmation prompt:

```bash
./run_conservative_live.py --yes-i-know-this-uses-real-money
```

**USE WITH EXTREME CAUTION**: This will start trading immediately without confirmation.

## Parameters

| Parameter | Description | Default | Example |
|-----------|-------------|---------|---------|
| `--pair` | Trading pair to use | XBTZAR | `--pair ETHZAR` |
| `--stop-loss` | Stop loss percentage (decimal) | 0.012 (1.2%) | `--stop-loss 0.015` for 1.5% |
| `--take-profit` | Take profit percentage (decimal) | 0.020 (2.0%) | `--take-profit 0.025` for 2.5% |
| `--amount` | Amount to trade per transaction | 0.001 | `--amount 0.005` |
| `--interval` | Seconds between market checks | 900 | `--interval 1800` for 30 minutes |
| `--yes-i-know-this-uses-real-money` | Skip confirmation prompt | False | Use flag to enable |

## Logging

The script creates detailed logs in:
```
data/logs/conservative_day_trader_LIVE.log
```

Monitor this file for trading activity, signals, and any errors.

## How It Works

1. **Market Analysis**: The script analyzes market data using multiple technical indicators:
   - Fast EMA (9 periods)
   - Medium EMA (21 periods)
   - Slow EMA (34 periods)
   - SMA (50 periods)
   - RSI and MACD as supporting indicators

2. **Signal Generation**: Based on these indicators, the script generates buy/sell signals with confidence levels.

3. **Conservative Decision Making**: Trades are only executed when confidence exceeds the threshold (default 80%) - this higher threshold reduces false signals.

4. **Risk Management**: Conservative stop loss and take profit levels are enforced to prioritize capital preservation.

5. **Longer Intervals**: The conservative strategy uses longer intervals between checks (15 minutes by default) to filter out market noise.

## Stopping the Trader

Press `Ctrl+C` at any time to gracefully stop the trader. The script will complete the current cycle and then terminate.

## Differences from Aggressive Trading

The conservative strategy differs from the aggressive strategy in several key ways:

1. **Higher Confidence Threshold**: Requires 80% confidence (vs 50% for aggressive)
2. **Tighter Stop Loss**: Uses 1.2% stop loss by default (vs 1.5% for aggressive)
3. **Lower Take Profit**: Uses 2.0% take profit by default (vs 2.5% for aggressive)
4. **Longer Intervals**: Checks every 15 minutes by default (vs 3 minutes for aggressive)
5. **More Indicators**: Uses triple EMA + SMA (vs fast/medium EMA + SMA for aggressive)
6. **Different Default Pair**: Uses XBTZAR by default (vs XBTMYR for aggressive)

These differences make the conservative strategy more suitable for:
- Lower risk tolerance
- Longer-term positions
- More stable trading environments
- Capital preservation focus

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Ensure your API keys are correctly set in the `.env` file
   - Verify the API key has trading permissions on Luno

2. **Insufficient Balance**
   - Check that you have sufficient balance for the trading pair
   - Reduce the trading amount with `--amount`

3. **No Trades Executing**
   - Check the log file for signal confidence levels
   - The script only trades when confidence exceeds 80%
   - Try reducing the confidence threshold if appropriate
   - Market conditions may not be favorable for the strategy

### Error Checking

If you encounter errors:

1. Check the log file: `data/logs/conservative_day_trader_LIVE.log`
2. Verify your API permissions on Luno
3. Ensure you have sufficient balance
4. Check your internet connection

## Best Practices

1. **Start Small**: Begin with minimal amounts (`--amount 0.0005` or lower)
2. **Monitor Continuously**: Watch the logs during initial trading
3. **Test First**: Consider running in dry-run mode with `run_trader.py --dry-run` before using this script
4. **Set Reasonable Risk**: Adjust stop-loss and take-profit according to market volatility
5. **Backup API Keys**: Keep a secure backup of your API credentials
6. **Use Longer Intervals**: The conservative strategy works best with longer intervals to avoid overtrading

## Advanced Usage

### Running in Background

Use nohup to run the script in the background:

```bash
nohup ./run_conservative_live.py > conservative_trading.out 2>&1 &
```

### Scheduled Trading

Use cron to schedule trading during specific hours:

```
# Run trading from 9 AM to 5 PM on weekdays
0 9 * * 1-5 cd /path/to/luno-bot && ./run_conservative_live.py --yes-i-know-this-uses-real-money
0 17 * * 1-5 pkill -f run_conservative_live.py
```

### Combining with Aggressive Strategy

For a balanced approach, you might run both strategies with different allocations:

```bash
# Run conservative with 70% of your allocation
./run_conservative_live.py --amount 0.007 &

# Run aggressive with 30% of your allocation
./run_aggressive_live.py --amount 0.003 &
```

## Disclaimer

Trading cryptocurrencies involves significant risk. This tool is provided as-is with no guarantees of profit or protection from losses. Always trade with funds you can afford to lose. The conservative strategy aims to reduce risk, but cannot eliminate it entirely.