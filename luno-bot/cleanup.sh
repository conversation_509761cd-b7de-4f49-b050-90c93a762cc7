#!/bin/bash

# Cleanup script for Luno Trading Bot
# This script removes old files that were replaced in the restructured project

echo "Cleaning up old files..."

# Old main trading files
rm -f auto_trade.py
rm -f enhanced_auto_trade.py
rm -f enhanced_realtime_trader.py
rm -f enhanced_trader.py
rm -f realtime_trader.py
rm -f main.py
rm -f safe_trader.py

# Old utility files
rm -f check_balance.py
rm -f check_supported_pairs.py
rm -f execute_trade.py
rm -f updated_execute_trade.py
rm -f trade_status.py
rm -f profit_loss_tracker.py

# Old testing files
rm -f test_luno_api.py
rm -f market_analysis.py

# Old backtesting files 
# (keeping run_backtest.py since it might be useful for the refactored system)
rm -f backtest.py

echo "Creating backup directory for reference..."
mkdir -p backup/old_files

# Instead of deleting, we can move important files to a backup
# in case we need to reference them later
for file in main.py auto_trade.py realtime_trader.py safe_trader.py; do
  if [ -f "$file" ]; then
    echo "Backing up $file"
    cp "$file" backup/old_files/
  fi
done

echo "Cleanup complete!"
echo "Any essential code from the old files has been refactored into the new directory structure."
echo "Old main files were backed up to backup/old_files/ for reference." 