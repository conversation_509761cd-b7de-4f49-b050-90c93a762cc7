"""
Global settings for the Luno Trading Bot.

Provides centralized access to configuration settings and defaults.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Default settings
DEFAULT_SETTINGS = {
    # Trading settings
    "trading": {
        "pair": "XRPMYR",
        "trade_amount": 0.001,
        "threshold": 0.01,
        "stop_loss": 0.02,
        "take_profit": 0.03,
        "check_interval": 3600,  # seconds
        "min_trade_interval": 30,  # seconds
        "default_predictor": "ensemble",
        "use_market_structure": True,
    },
    # API settings
    "api": {
        "base_url": "https://api.luno.com/api/1",
        "use_websocket": True,
        "depth": 100,
    },
    # Data settings
    "data": {
        "historical_dir": "data/historical",
        "logs_dir": "data/logs",
        "backtest_dir": "data/backtests",
        "default_days_back": 90,
        "default_candle_duration": 3600,  # seconds
    },
    # Logging settings
    "logging": {
        "level": "INFO",
        "format": "[%(asctime)s] %(levelname)s: %(message)s",
        "log_to_file": True,
        "log_interval": 2,
    },
}

# Global settings singleton
_settings = None


def get_settings() -> Dict[str, Any]:
    """Get the current settings.

    Returns:
        Dict containing all settings
    """
    global _settings

    if _settings is None:
        # Initialize settings with defaults
        _settings = DEFAULT_SETTINGS.copy()

        # Try to load custom settings from file
        settings_file = Path("config/settings.json")
        if settings_file.exists():
            try:
                with open(settings_file, "r") as f:
                    custom_settings = json.load(f)

                # Update defaults with custom settings
                _update_nested_dict(_settings, custom_settings)
            except Exception as e:
                print(f"Error loading settings from file: {e}")

        # Override with environment variables
        if os.getenv("LUNO_TRADING_PAIR"):
            _settings["trading"]["pair"] = os.getenv("LUNO_TRADING_PAIR")

        if os.getenv("LUNO_TRADE_AMOUNT"):
            try:
                _settings["trading"]["trade_amount"] = float(
                    os.getenv("LUNO_TRADE_AMOUNT")
                )
            except ValueError:
                pass

    return _settings


def update_settings(new_settings: Dict[str, Any]) -> Dict[str, Any]:
    """Update the current settings.

    Args:
        new_settings: Dict containing settings to update

    Returns:
        Dict containing the updated settings
    """
    global _settings

    if _settings is None:
        _settings = get_settings()

    # Update settings
    _update_nested_dict(_settings, new_settings)

    # Save to file
    settings_file = Path("config/settings.json")
    settings_file.parent.mkdir(exist_ok=True, parents=True)

    try:
        with open(settings_file, "w") as f:
            json.dump(_settings, f, indent=4)
    except Exception as e:
        print(f"Error saving settings to file: {e}")

    return _settings


def _update_nested_dict(d: Dict[str, Any], u: Dict[str, Any]) -> Dict[str, Any]:
    """Update a nested dictionary recursively.

    Args:
        d: Dictionary to update
        u: Dictionary with updates

    Returns:
        Updated dictionary
    """
    for k, v in u.items():
        if isinstance(v, dict) and k in d and isinstance(d[k], dict):
            d[k] = _update_nested_dict(d[k], v)
        else:
            d[k] = v
    return d
