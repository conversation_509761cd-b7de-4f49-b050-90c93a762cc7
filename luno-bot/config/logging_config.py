"""
Logging configuration for the Luno Trading Bot.

Provides standardized logging setup across all components with enhanced visual formatting.
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any
import sys
try:
    from colorama import init, Fore, Back, Style
    COLORAMA_AVAILABLE = True
    init()  # Initialize colorama
except ImportError:
    COLORAMA_AVAILABLE = False
    print("Installing colorama for enhanced logging...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "colorama"])
    from colorama import init, Fore, Back, Style
    init()  # Initialize colorama


class ColoredFormatter(logging.Formatter):
    """Custom formatter for colored console output."""
    
    # Define colors for different log levels
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.WHITE + Back.RED + Style.BRIGHT,
    }
    
    # Special formatting for specific message types
    SPECIAL_FORMATS = {
        'DRY RUN': Fore.MAGENTA + Style.BRIGHT,
        'TRADE': Fore.YELLOW + Style.BRIGHT,
        'BUY': Fore.GREEN + Style.BRIGHT,
        'SELL': Fore.RED + Style.BRIGHT,
        'PROFIT': Fore.GREEN + Style.BRIGHT,
        'LOSS': Fore.RED + Style.BRIGHT,
        'WebSocket': Fore.BLUE,
    }
    
    def __init__(self, fmt=None, datefmt=None, style='%'):
        super().__init__(fmt, datefmt, style)
    
    def format(self, record):
        # First, format the record with the parent formatter
        message = super().format(record)
        
        if not COLORAMA_AVAILABLE:
            return message
            
        # Apply level-based coloring
        levelname = record.levelname
        if levelname in self.COLORS:
            color_start = self.COLORS[levelname]
            color_end = Style.RESET_ALL
            
            # Replace the level name with a colored version
            colored_levelname = f"{color_start}{levelname}{color_end}"
            message = message.replace(levelname, colored_levelname)
            
        # Apply special formatting for certain keywords
        for keyword, color in self.SPECIAL_FORMATS.items():
            if keyword in record.getMessage():
                # We need to be careful to only color the keyword, not the whole message
                message = message.replace(f"[{keyword}]", f"{color}[{keyword}]{Style.RESET_ALL}")
                message = message.replace(f" {keyword} ", f" {color}{keyword}{Style.RESET_ALL} ")
                message = message.replace(f" {keyword}:", f" {color}{keyword}{Style.RESET_ALL}:")
                
        return message


def setup_logging(
    name: str,
    log_file: Optional[str] = None,
    level: str = "INFO",
    format_str: str = "[%(asctime)s] %(levelname)s: %(message)s",
) -> logging.Logger:
    """Set up logging configuration with enhanced visual formatting.

    Args:
        name: Logger name
        log_file: Optional log file path
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_str: Log message format

    Returns:
        Configured logger with color formatting for console output
    """
    # Convert level string to logging level
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }
    log_level = level_map.get(level.upper(), logging.INFO)

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Console handler with colored output
    console_handler = logging.StreamHandler()
    colored_formatter = ColoredFormatter(format_str)
    console_handler.setFormatter(colored_formatter)
    logger.addHandler(console_handler)

    # File handler if specified (without colors)
    if log_file:
        # Ensure log directory exists
        log_path = Path(log_file)
        log_path.parent.mkdir(exist_ok=True, parents=True)

        # Regular formatter for file logs (no colors)
        file_formatter = logging.Formatter(format_str)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)

    return logger
