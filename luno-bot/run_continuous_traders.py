#!/usr/bin/env python
"""
Continuous Trading Script for Luno Bot

This script runs both aggressive day trader and conservative day trader strategies
continuously, either in parallel (separate processes) or in alternating mode.
"""

import argparse
import asyncio
import logging
import multiprocessing
import os
import signal
import sys
import time
from datetime import datetime
from pathlib import Path

# Import trading modules
from traders.aggressive_day_trader import AggressiveDayTrader
from traders.conservative_day_trader import ConservativeDayTrader
from core.api_client import LunoAPIClient
from core.data_fetcher import MarketDataFetcher
from config.settings import get_settings, update_settings
from core.utils.logging_utils import setup_logger
from core.simulation import SimulatedLunoClient, SimulatedMarket, SimulatedAccount

# Global flag for managing process termination
SHUTDOWN_REQUESTED = multiprocessing.Value('i', 0)

def setup_args():
    """Set up command line arguments for the continuous trader script."""
    parser = argparse.ArgumentParser(description="Luno Bot - Continuous Trading")
    
    # Main configuration options
    parser.add_argument(
        "--mode",
        type=str,
        choices=["parallel", "alternating"],
        default="parallel",
        help="Run mode: parallel (both strategies simultaneously) or alternating (one after another)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in dry-run mode (no actual trades)"
    )
    
    # Trading pair options
    parser.add_argument(
        "--aggressive-pair",
        type=str,
        default="XBTZAR",
        help="Trading pair for aggressive day trader (default: XBTZAR)"
    )
    parser.add_argument(
        "--conservative-pair",
        type=str,
        default="XBTZAR",
        help="Trading pair for conservative day trader (default: XBTZAR)"
    )
    
    # Time intervals
    parser.add_argument(
        "--aggressive-interval",
        type=int,
        default=180,  # 3 minutes
        help="Check interval for aggressive trader in seconds (default: 180)"
    )
    parser.add_argument(
        "--conservative-interval",
        type=int,
        default=900,  # 15 minutes
        help="Check interval for conservative trader in seconds (default: 900)"
    )
    parser.add_argument(
        "--alternating-delay",
        type=int,
        default=3600,  # 1 hour
        help="Time to run each strategy before switching in alternating mode (seconds, default: 3600)"
    )
    
    # Risk parameters
    parser.add_argument(
        "--aggressive-stop-loss",
        type=float,
        default=0.015,
        help="Stop loss percentage for aggressive trader (default: 0.015 - 1.5%)"
    )
    parser.add_argument(
        "--aggressive-take-profit",
        type=float,
        default=0.025,
        help="Take profit percentage for aggressive trader (default: 0.025 - 2.5%)"
    )
    parser.add_argument(
        "--conservative-stop-loss",
        type=float,
        default=0.012,
        help="Stop loss percentage for conservative trader (default: 0.012 - 1.2%)"
    )
    parser.add_argument(
        "--conservative-take-profit",
        type=float,
        default=0.020,
        help="Take profit percentage for conservative trader (default: 0.020 - 2.0%)"
    )
    
    # Additional options
    parser.add_argument(
        "--no-websocket",
        action="store_true",
        help="Disable WebSocket for real-time data (use polling instead)"
    )
    
    return parser.parse_args()

def config_from_args(args, strategy_type):
    """Create a configuration dictionary from command line arguments.
    
    Args:
        args: Command line arguments
        strategy_type: Either 'aggressive' or 'conservative'
        
    Returns:
        Dictionary with configuration settings
    """
    # Load base settings
    settings = get_settings()
    
    # Update with strategy-specific settings
    if strategy_type == "aggressive":
        trading_config = {
            "pair": args.aggressive_pair,
            "check_interval": args.aggressive_interval,
            "stop_loss": args.aggressive_stop_loss,
            "take_profit": args.aggressive_take_profit,
            "confidence_threshold": 0.5,  # Default for aggressive trading
        }
    else:  # conservative
        trading_config = {
            "pair": args.conservative_pair,
            "check_interval": args.conservative_interval,
            "stop_loss": args.conservative_stop_loss,
            "take_profit": args.conservative_take_profit,
            "confidence_threshold": 0.8,  # Default for conservative trading
        }
    
    # Update settings
    settings["trading"] = trading_config
    
    # Ensure websocket config exists
    if "websocket" not in settings:
        settings["websocket"] = {}
    settings["websocket"]["enabled"] = not args.no_websocket
    
    return settings

def setup_signal_handlers():
    """Set up signal handlers for graceful shutdowns."""
    def signal_handler(sig, frame):
        print(f"\n[{datetime.now()}] Shutdown requested! Gracefully stopping traders...")
        with SHUTDOWN_REQUESTED.get_lock():
            SHUTDOWN_REQUESTED.value = 1
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    return signal_handler

def run_aggressive_trader(args):
    """Run the aggressive day trading strategy.
    
    Args:
        args: Command line arguments
    """
    # Configure logger
    log_path = Path("data/logs/aggressive_day_trader.log")
    logger = setup_logger(
        "aggressive_day_trader",
        log_path,
        level=logging.INFO
    )
    
    # Initialize API client and data fetcher
    config = config_from_args(args, "aggressive")
    # Ensure it's a deep copy to avoid modifying the original settings
    config = {k: v.copy() if isinstance(v, dict) else v for k, v in config.items()}
    
    # Initialize API client (real or simulated based on dry_run)
    if args.dry_run:
        logger.info("Using simulated Luno client (dry-run mode)")
        api_client = SimulatedLunoClient(pair=args.aggressive_pair)
        api_client.dry_run = True
    else:
        logger.info("Using real Luno API client")
        api_client = LunoAPIClient(
            config.get("api", {}).get("api_key"),
            config.get("api", {}).get("api_secret")
        )
        api_client.dry_run = False
    data_fetcher = MarketDataFetcher(
        api_client=api_client
    )
    
    # Initialize trader
    trader = AggressiveDayTrader(
        api_client=api_client,
        data_fetcher=data_fetcher,
        config=config,
        pair=args.aggressive_pair,
        logger=logger
    )
    
    # Run trading loop
    logger.info(f"Starting Aggressive Day Trader for {args.aggressive_pair}")
    logger.info(f"Dry run mode: {args.dry_run}")
    
    while True:
        # Check if shutdown was requested
        if SHUTDOWN_REQUESTED.value == 1:
            logger.info("Shutdown requested. Stopping Aggressive Day Trader...")
            break
            
        try:
            # Run a single trading cycle
            cycle_result = trader.run_trading_cycle()
            
            # Log the result
            logger.info(f"Cycle result: {cycle_result['signal']['action']} (confidence: {cycle_result['signal']['confidence']:.2f})")
            if cycle_result['trade']['action'] != 'none':
                logger.info(f"Trade executed: {cycle_result['trade']['action']}")
            
            # Sleep for the check interval
            time.sleep(trader.check_interval)
            
        except Exception as e:
            logger.error(f"Error in Aggressive Day Trader cycle: {e}")
            # Sleep briefly before next attempt
            time.sleep(30)
    
    logger.info("Aggressive Day Trader stopped.")

def run_conservative_trader(args):
    """Run the conservative day trading strategy.
    
    Args:
        args: Command line arguments
    """
    # Configure logger
    log_path = Path("data/logs/conservative_day_trader.log")
    logger = setup_logger(
        "conservative_day_trader",
        log_path,
        level=logging.INFO
    )
    
    # Initialize API client and data fetcher
    config = config_from_args(args, "conservative")
    # Ensure it's a deep copy to avoid modifying the original settings
    config = {k: v.copy() if isinstance(v, dict) else v for k, v in config.items()}
    
    # Initialize API client (real or simulated based on dry_run)
    if args.dry_run:
        logger.info("Using simulated Luno client (dry-run mode)")
        api_client = SimulatedLunoClient(pair=args.conservative_pair)
        api_client.dry_run = True
    else:
        logger.info("Using real Luno API client")
        api_client = LunoAPIClient(
            config.get("api", {}).get("api_key"),
            config.get("api", {}).get("api_secret")
        )
        api_client.dry_run = False
    data_fetcher = MarketDataFetcher(
        api_client=api_client
    )
    
    # Initialize trader
    trader = ConservativeDayTrader(
        api_client=api_client,
        data_fetcher=data_fetcher,
        config=config,
        pair=args.conservative_pair,
        logger=logger
    )
    
    # Run trading loop
    logger.info(f"Starting Conservative Day Trader for {args.conservative_pair}")
    logger.info(f"Dry run mode: {args.dry_run}")
    
    while True:
        # Check if shutdown was requested
        if SHUTDOWN_REQUESTED.value == 1:
            logger.info("Shutdown requested. Stopping Conservative Day Trader...")
            break
            
        try:
            # Run a single trading cycle
            cycle_result = trader.run_trading_cycle()
            
            # Log the result
            logger.info(f"Cycle result: {cycle_result['signal']['action']} (confidence: {cycle_result['signal']['confidence']:.2f})")
            if cycle_result['trade']['action'] != 'none':
                logger.info(f"Trade executed: {cycle_result['trade']['action']}")
            
            # Sleep for the check interval
            time.sleep(trader.check_interval)
            
        except Exception as e:
            logger.error(f"Error in Conservative Day Trader cycle: {e}")
            # Sleep briefly before next attempt
            time.sleep(30)
    
    logger.info("Conservative Day Trader stopped.")

def run_alternating_mode(args):
    """Run both trading strategies in alternating mode.
    
    Args:
        args: Command line arguments
    """
    logger = setup_logger(
        "alternating_trader",
        Path("data/logs/alternating_trader.log"),
        level=logging.INFO
    )
    
    logger.info("Starting alternating trading mode")
    logger.info(f"Dry run mode: {args.dry_run}")
    logger.info(f"Alternating delay: {args.alternating_delay} seconds")
    
    # Create simulation environment for dry run if needed
    if args.dry_run:
        logger.info("Initializing simulation environment for dry-run mode")
        # We'll use the SimulatedLunoClient for each trader separately
    
    # Main alternating loop
    while True:
        # Check if shutdown was requested
        if SHUTDOWN_REQUESTED.value == 1:
            logger.info("Shutdown requested. Stopping alternating mode...")
            break
        
        # Run aggressive trader
        logger.info("Starting aggressive day trading cycle")
        run_aggressive_trader(args)
        logger.info(f"Aggressive cycle complete. Sleeping for {args.alternating_delay} seconds...")
        
        # Sleep between strategies
        for _ in range(args.alternating_delay):
            if SHUTDOWN_REQUESTED.value == 1:
                break
            time.sleep(1)
        
        if SHUTDOWN_REQUESTED.value == 1:
            logger.info("Shutdown requested. Stopping alternating mode...")
            break
        
        # Run conservative trader
        logger.info("Starting conservative day trading cycle")
        run_conservative_trader(args)
        logger.info(f"Conservative cycle complete. Sleeping for {args.alternating_delay} seconds...")
        
        # Sleep between strategies
        for _ in range(args.alternating_delay):
            if SHUTDOWN_REQUESTED.value == 1:
                break
            time.sleep(1)
    
    logger.info("Alternating trading mode stopped.")

def main():
    """Main function for the continuous trading script."""
    # Create necessary directories
    Path("data/logs").mkdir(exist_ok=True, parents=True)
    Path("data/orders").mkdir(exist_ok=True, parents=True)
    Path("data/history").mkdir(exist_ok=True, parents=True)
    Path("data/snapshots").mkdir(exist_ok=True, parents=True)
    
    # Parse arguments
    args = setup_args()
    
    # Set up signal handlers for graceful shutdown
    setup_signal_handlers()
    
    # Determine run mode and start trading
    if args.mode == "parallel":
        print(f"[{datetime.now()}] Starting parallel trading mode")
        print(f"[{datetime.now()}] Dry run mode: {args.dry_run}")
        print(f"[{datetime.now()}] Press Ctrl+C to stop all traders")
        
        if args.dry_run:
            print(f"[{datetime.now()}] Using simulation environment for trading")
        
        # Create processes
        aggressive_process = multiprocessing.Process(
            target=run_aggressive_trader,
            args=(args,),
            name="aggressive_trader"
        )
        
        conservative_process = multiprocessing.Process(
            target=run_conservative_trader,
            args=(args,),
            name="conservative_trader"
        )
        
        # Start processes
        aggressive_process.start()
        conservative_process.start()
        
        # Wait for processes to complete
        try:
            aggressive_process.join()
            conservative_process.join()
        except KeyboardInterrupt:
            print(f"\n[{datetime.now()}] Keyboard interrupt received")
        finally:
            # Make sure shutdown is requested
            with SHUTDOWN_REQUESTED.get_lock():
                SHUTDOWN_REQUESTED.value = 1
                
            # Wait for processes to terminate
            timeout = 30
            print(f"[{datetime.now()}] Waiting up to {timeout} seconds for traders to stop...")
            
            t_start = time.time()
            while (aggressive_process.is_alive() or conservative_process.is_alive()) and time.time() - t_start < timeout:
                time.sleep(1)
            
            # Force terminate if necessary
            if aggressive_process.is_alive():
                print(f"[{datetime.now()}] Force terminating aggressive trader")
                aggressive_process.terminate()
                
            if conservative_process.is_alive():
                print(f"[{datetime.now()}] Force terminating conservative trader")
                conservative_process.terminate()
    else:  # alternating mode
        try:
            run_alternating_mode(args)
        except KeyboardInterrupt:
            print(f"\n[{datetime.now()}] Keyboard interrupt received")
            with SHUTDOWN_REQUESTED.get_lock():
                SHUTDOWN_REQUESTED.value = 1
    
    print(f"[{datetime.now()}] All traders have stopped. Exiting.")

if __name__ == "__main__":
    main()