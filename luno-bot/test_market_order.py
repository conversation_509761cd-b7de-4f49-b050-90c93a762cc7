#!/usr/bin/env python3
"""
Test script for market orders in the Luno API client.

This script tests the fixed market order functionality to verify that 
the post_market_order method is called correctly.
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import our client
from core.api_client import LunoAPIClient

# Load environment variables
load_dotenv()


def print_section(title):
    """Print a section header."""
    print("\n" + "=" * 50)
    print(title)
    print("=" * 50)


def test_market_buy():
    """Test a market buy order with a small amount."""
    print_section("Testing Market Buy Order")
    
    # Create API client
    api_client = LunoAPIClient()
    
    # Get current price for reference
    try:
        ticker = api_client.get_ticker("XBTMYR")
        print(f"Current price - Ask: {ticker.get('ask')}, Bid: {ticker.get('bid')}")
    except Exception as e:
        print(f"Error getting ticker: {e}")
        return
    
    # Create a very small market buy order for testing
    test_amount = 0.0001  # Very small amount to test with
    print(f"\nAttempting to place market BUY order for {test_amount} BTC")
    
    try:
        # Place a market buy order using modified API client
        response = api_client.place_order(
            pair="XBTMYR", 
            type="BUY", 
            volume=test_amount,
            price=None  # None price indicates market order
        )
        
        print("\nOrder Response:")
        print(f"Success: {response.get('success', False)}")
        print(f"Order ID: {response.get('order_id', 'N/A')}")
        
        # If order was successful, retrieve its details
        if response.get('order_id'):
            print("\nFetching order details...")
            time.sleep(2)  # Wait a bit for order processing
            
            try:
                order_details = api_client.get_order(response['order_id'])
                print("\nOrder Details:")
                print(f"State: {order_details.get('state', 'Unknown')}")
                print(f"Type: {order_details.get('type', 'Unknown')}")
                print(f"Limit Price: {order_details.get('limit_price', 'N/A')}")
                print(f"Base: {order_details.get('base', 'Unknown')}")
                print(f"Counter: {order_details.get('counter', 'Unknown')}")
                print(f"Created At: {order_details.get('creation_timestamp', 'Unknown')}")
            except Exception as e:
                print(f"Error retrieving order details: {e}")
        
    except Exception as e:
        print(f"\nError placing market BUY order: {e}")


def test_market_sell():
    """Test a market sell order with a small amount."""
    print_section("Testing Market Sell Order")
    
    # Create API client
    api_client = LunoAPIClient()
    
    # Get current price for reference
    try:
        ticker = api_client.get_ticker("XBTMYR")
        print(f"Current price - Ask: {ticker.get('ask')}, Bid: {ticker.get('bid')}")
    except Exception as e:
        print(f"Error getting ticker: {e}")
        return
    
    # Create a very small market sell order for testing
    test_amount = 0.0001  # Very small amount to test with
    print(f"\nAttempting to place market SELL order for {test_amount} BTC")
    
    try:
        # Place a market sell order using modified API client
        response = api_client.place_order(
            pair="XBTMYR", 
            type="SELL", 
            volume=test_amount,
            price=None  # None price indicates market order
        )
        
        print("\nOrder Response:")
        print(f"Success: {response.get('success', False)}")
        print(f"Order ID: {response.get('order_id', 'N/A')}")
        
        # If order was successful, retrieve its details
        if response.get('order_id'):
            print("\nFetching order details...")
            time.sleep(2)  # Wait a bit for order processing
            
            try:
                order_details = api_client.get_order(response['order_id'])
                print("\nOrder Details:")
                print(f"State: {order_details.get('state', 'Unknown')}")
                print(f"Type: {order_details.get('type', 'Unknown')}")
                print(f"Limit Price: {order_details.get('limit_price', 'N/A')}")
                print(f"Base: {order_details.get('base', 'Unknown')}")
                print(f"Counter: {order_details.get('counter', 'Unknown')}")
                print(f"Created At: {order_details.get('creation_timestamp', 'Unknown')}")
            except Exception as e:
                print(f"Error retrieving order details: {e}")
        
    except Exception as e:
        print(f"\nError placing market SELL order: {e}")


def main():
    """Main function."""
    print_section("Market Order Test Script")
    
    # Check for required environment variables
    if not os.getenv("LUNO_API_KEY") or not os.getenv("LUNO_API_SECRET"):
        print("Error: LUNO_API_KEY and LUNO_API_SECRET environment variables must be set")
        print("Please create a .env file or set them in your environment")
        return 1

    # Ask for confirmation before placing orders
    print("\nWARNING: This test will attempt to place REAL market orders!")
    print("They will be very small amounts but will use actual funds.")
    confirm = input("\nDo you want to continue with the test? (yes/no): ").lower()
    
    if confirm != 'yes':
        print("Test canceled.")
        return 0

    # Select test type
    print("\nWhich order type do you want to test?")
    print("1. Market BUY")
    print("2. Market SELL")
    print("3. Both")
    
    test_type = input("Enter your choice (1/2/3): ")
    
    if test_type == '1':
        test_market_buy()
    elif test_type == '2':
        test_market_sell()
    elif test_type == '3':
        test_market_buy()
        test_market_sell()
    else:
        print("Invalid choice. Exiting.")
        return 1

    print_section("Market Order Tests Completed")
    return 0


if __name__ == "__main__":
    sys.exit(main())