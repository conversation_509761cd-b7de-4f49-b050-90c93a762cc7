#!/usr/bin/env python3
"""
Trade status checker utility for the Luno Trading Bot.

This script provides a simple utility to check the status of trades and orders.
"""

import argparse
import sys
import json
from pathlib import Path
from datetime import datetime
from tabulate import tabulate

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.api_client import LunoAPIClient
from execution.order_manager import OrderManager
from config.logging_config import setup_logging

# Set up logging
logger = setup_logging("trade_status", "data/logs/tools.log")


def setup_args():
    """Set up command line arguments."""
    parser = argparse.ArgumentParser(description="Luno Trade Status Checker")
    parser.add_argument("--order-id", type=str, help="Specific order ID to check")
    parser.add_argument(
        "--pair", type=str, help="Filter by trading pair (e.g., 'XBTZAR')"
    )
    parser.add_argument(
        "--format",
        choices=["table", "json"],
        default="table",
        help="Output format (default: table)",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=10,
        help="Maximum number of orders to display (default: 10)",
    )
    return parser.parse_args()


def check_trade_status(order_id=None, pair=None, format_output="table", limit=10):
    """Check trade status.

    Args:
        order_id: Specific order ID to check
        pair: Filter by trading pair
        format_output: Output format ('table' or 'json')
        limit: Maximum number of orders to display
    """
    try:
        # Initialize API client and order manager
        api_client = LunoAPIClient()
        order_manager = OrderManager(api_client=api_client)

        # Load existing orders
        order_manager.load_orders()

        # If specific order ID is provided, check just that order
        if order_id:
            result = order_manager.check_order_status(order_id)

            if not result["success"]:
                logger.error(
                    f"Failed to retrieve order {order_id}: {result.get('error', 'Unknown error')}"
                )
                return 1

            order = result["order"]

            if format_output == "table":
                # Print order details in table format
                print(f"\nOrder Details - {order_id}")
                print("=" * 80)

                # Basic details
                details = [
                    ["Order ID", order["order_id"]],
                    ["Pair", order["pair"]],
                    ["Type", order["type"]],
                    ["Amount", f"{order['amount']:.8f}"],
                    ["Price", f"{order['price']:.2f}" if order["price"] else "Market"],
                    ["Status", order["status"]],
                    [
                        "Created",
                        (
                            order["created_at"].strftime("%Y-%m-%d %H:%M:%S")
                            if isinstance(order["created_at"], datetime)
                            else order["created_at"]
                        ),
                    ],
                ]

                if order.get("completed_at"):
                    completed_at = order["completed_at"]
                    if isinstance(completed_at, datetime):
                        completed_at = completed_at.strftime("%Y-%m-%d %H:%M:%S")
                    details.append(["Completed", completed_at])

                print(tabulate(details, tablefmt="simple"))

                # Show fills if any
                if order.get("fills"):
                    print("\nFills:")
                    print("-" * 80)

                    fills_data = []
                    for fill in order["fills"]:
                        price = float(fill.get("price", 0))
                        volume = float(fill.get("volume", 0))
                        timestamp = fill.get("timestamp", "")

                        # Convert timestamp if needed
                        if isinstance(timestamp, int):
                            timestamp = datetime.fromtimestamp(
                                timestamp / 1000
                            ).strftime("%Y-%m-%d %H:%M:%S")

                        fills_data.append(
                            [
                                timestamp,
                                f"{price:.2f}",
                                f"{volume:.8f}",
                                f"{price * volume:.2f}",
                            ]
                        )

                    print(
                        tabulate(
                            fills_data,
                            headers=["Time", "Price", "Volume", "Total"],
                            tablefmt="simple",
                        )
                    )

            else:  # JSON format
                # Convert datetime objects to strings
                order_copy = order.copy()
                if isinstance(order_copy.get("created_at"), datetime):
                    order_copy["created_at"] = order_copy["created_at"].isoformat()
                if isinstance(order_copy.get("completed_at"), datetime):
                    order_copy["completed_at"] = order_copy["completed_at"].isoformat()
                if isinstance(order_copy.get("last_checked"), datetime):
                    order_copy["last_checked"] = order_copy["last_checked"].isoformat()

                print(json.dumps(order_copy, indent=2))

        else:
            # Check all pending orders
            order_manager.check_pending_orders()

            # Get order history
            orders = order_manager.get_order_history(pair=pair, limit=limit)

            # Get active orders
            active_summary = order_manager.get_active_orders_summary()
            active_orders = active_summary["orders"]

            if format_output == "table":
                # Print active orders
                if active_orders:
                    print("\nActive Orders")
                    print("=" * 100)

                    active_data = []
                    for order in active_orders:
                        created_at = order.get("created_at", "")
                        if isinstance(created_at, datetime):
                            created_at = created_at.strftime("%Y-%m-%d %H:%M:%S")

                        active_data.append(
                            [
                                order["order_id"][:8] + "...",
                                order["pair"],
                                order["type"],
                                f"{order['amount']:.8f}",
                                f"{order['price']:.2f}" if order["price"] else "Market",
                                order["status"],
                                created_at,
                            ]
                        )

                    print(
                        tabulate(
                            active_data,
                            headers=[
                                "Order ID",
                                "Pair",
                                "Type",
                                "Amount",
                                "Price",
                                "Status",
                                "Created",
                            ],
                            tablefmt="simple",
                        )
                    )

                    print(
                        f"\nActive: {active_summary['pending_count']} orders ({active_summary['buy_orders']} buy, {active_summary['sell_orders']} sell)"
                    )
                else:
                    print("\nNo active orders")

                # Print recent orders
                if orders:
                    print("\nRecent Completed Orders")
                    print("=" * 100)

                    history_data = []
                    for order in orders:
                        created_at = order.get("created_at", "")
                        if isinstance(created_at, datetime):
                            created_at = created_at.strftime("%Y-%m-%d %H:%M:%S")

                        completed_at = order.get("completed_at", "")
                        if isinstance(completed_at, datetime):
                            completed_at = completed_at.strftime("%Y-%m-%d %H:%M:%S")

                        history_data.append(
                            [
                                order["order_id"][:8] + "...",
                                order["pair"],
                                order["type"],
                                f"{order['amount']:.8f}",
                                f"{order['price']:.2f}" if order["price"] else "Market",
                                order["status"],
                                created_at,
                                completed_at,
                            ]
                        )

                    print(
                        tabulate(
                            history_data,
                            headers=[
                                "Order ID",
                                "Pair",
                                "Type",
                                "Amount",
                                "Price",
                                "Status",
                                "Created",
                                "Completed",
                            ],
                            tablefmt="simple",
                        )
                    )
                else:
                    print("\nNo completed orders")

            else:  # JSON format
                # Format orders for JSON
                formatted_active = []
                for order in active_orders:
                    order_copy = order.copy()
                    if isinstance(order_copy.get("created_at"), datetime):
                        order_copy["created_at"] = order_copy["created_at"].isoformat()
                    formatted_active.append(order_copy)

                formatted_history = []
                for order in orders:
                    order_copy = order.copy()
                    if isinstance(order_copy.get("created_at"), datetime):
                        order_copy["created_at"] = order_copy["created_at"].isoformat()
                    if isinstance(order_copy.get("completed_at"), datetime):
                        order_copy["completed_at"] = order_copy[
                            "completed_at"
                        ].isoformat()
                    if isinstance(order_copy.get("last_checked"), datetime):
                        order_copy["last_checked"] = order_copy[
                            "last_checked"
                        ].isoformat()
                    formatted_history.append(order_copy)

                result = {
                    "timestamp": datetime.now().isoformat(),
                    "active_orders": {
                        "count": active_summary["pending_count"],
                        "buy_orders": active_summary["buy_orders"],
                        "sell_orders": active_summary["sell_orders"],
                        "orders": formatted_active,
                    },
                    "completed_orders": formatted_history,
                }

                print(json.dumps(result, indent=2))

        return 0

    except Exception as e:
        logger.error(f"Error checking trade status: {e}")
        return 1


def main():
    """Main function."""
    # Create necessary directories
    Path("data/logs").mkdir(exist_ok=True, parents=True)

    # Get command line arguments
    args = setup_args()

    # Check trade status
    return check_trade_status(
        order_id=args.order_id,
        pair=args.pair,
        format_output=args.format,
        limit=args.limit,
    )


if __name__ == "__main__":
    sys.exit(main())
