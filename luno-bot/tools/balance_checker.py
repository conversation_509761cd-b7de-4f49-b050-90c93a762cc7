#!/usr/bin/env python3
"""
Balance checker utility for the Luno Trading Bot.

This script provides a simple utility to check account balances on the Luno exchange.
"""

import argparse
import sys
import json
from pathlib import Path
from datetime import datetime
from tabulate import tabulate

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.api_client import LunoAPIClient
from config.logging_config import setup_logging

# Set up logging
logger = setup_logging("balance_checker", "data/logs/tools.log")


def setup_args():
    """Set up command line arguments."""
    parser = argparse.ArgumentParser(description="Luno Balance Checker")
    parser.add_argument(
        "--save", action="store_true", help="Save balance snapshot to file"
    )
    parser.add_argument(
        "--format",
        choices=["table", "json"],
        default="table",
        help="Output format (default: table)",
    )
    return parser.parse_args()


def check_balances(format_output="table", save=False):
    """Check account balances.

    Args:
        format_output: Output format ('table' or 'json')
        save: Whether to save balance snapshot to file
    """
    try:
        # Initialize API client
        api_client = LunoAPIClient()

        # Get balances
        balances_response = api_client.get_balances()

        if "balance" not in balances_response:
            logger.error("Failed to retrieve balances")
            return 1

        balances = balances_response["balance"]

        # Calculate total value in ZAR (simplified approach)
        total_value = 0.0

        # For assets other than ZAR, try to get their value
        for balance in balances:
            asset = balance["asset"]
            amount = float(balance["balance"])

            if asset == "ZAR":
                total_value += amount
            else:
                # Try to get ticker to calculate value
                try:
                    ticker = api_client.get_ticker(f"{asset}ZAR")
                    if "bid" in ticker:
                        value = amount * float(ticker["bid"])
                        balance["value_zar"] = value
                        total_value += value
                except Exception as e:
                    logger.warning(f"Could not get value for {asset}: {e}")
                    balance["value_zar"] = 0.0

        # Display balances
        if format_output == "table":
            table_data = []
            for balance in balances:
                asset = balance["asset"]
                amount = float(balance["balance"])
                reserved = float(balance["reserved"])
                value_zar = balance.get("value_zar", 0.0) if asset != "ZAR" else amount

                table_data.append(
                    [
                        asset,
                        f"{amount:.8f}",
                        f"{reserved:.8f}",
                        f"R {value_zar:.2f}" if value_zar > 0 else "N/A",
                    ]
                )

            # Add total row
            table_data.append(["TOTAL", "", "", f"R {total_value:.2f}"])

            # Print table
            print("\nLuno Account Balances")
            print("=" * 50)
            print(
                tabulate(
                    table_data,
                    headers=["Asset", "Balance", "Reserved", "Value (ZAR)"],
                    tablefmt="simple",
                )
            )
            print("=" * 50)
            print(f"Snapshot time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        else:  # JSON format
            balance_data = {
                "timestamp": datetime.now().isoformat(),
                "balances": balances,
                "total_value_zar": total_value,
            }
            print(json.dumps(balance_data, indent=2))

        # Save balance snapshot if requested
        if save:
            snapshots_dir = Path("data/snapshots")
            snapshots_dir.mkdir(exist_ok=True, parents=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"balance_snapshot_{timestamp}.json"
            filepath = snapshots_dir / filename

            balance_data = {
                "timestamp": datetime.now().isoformat(),
                "balances": balances,
                "total_value_zar": total_value,
            }

            with open(filepath, "w") as f:
                json.dump(balance_data, f, indent=2)

            logger.info(f"Saved balance snapshot to {filepath}")
            if format_output == "table":
                print(f"\nBalance snapshot saved to: {filepath}")

        return 0

    except Exception as e:
        logger.error(f"Error checking balances: {e}")
        return 1


def main():
    """Main function."""
    # Create necessary directories
    Path("data/logs").mkdir(exist_ok=True, parents=True)

    # Get command line arguments
    args = setup_args()

    # Check balances
    return check_balances(format_output=args.format, save=args.save)


if __name__ == "__main__":
    sys.exit(main())
