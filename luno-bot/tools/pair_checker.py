#!/usr/bin/env python3
"""
Trading pair checker utility for the Luno Trading Bot.

This script provides a simple utility to check supported trading pairs
on the Luno exchange and their current market data.
"""

import argparse
import sys
import json
from pathlib import Path
from datetime import datetime
from tabulate import tabulate

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.api_client import LunoAPIClient
from config.logging_config import setup_logging

# Set up logging
logger = setup_logging("pair_checker", "data/logs/tools.log")


def setup_args():
    """Set up command line arguments."""
    parser = argparse.ArgumentParser(description="Luno Trading Pair Checker")
    parser.add_argument(
        "--pair", type=str, help="Specific pair to check (e.g., 'XBTZAR')"
    )
    parser.add_argument(
        "--format",
        choices=["table", "json"],
        default="table",
        help="Output format (default: table)",
    )
    parser.add_argument(
        "--detailed",
        action="store_true",
        help="Show detailed information including orderbook",
    )
    return parser.parse_args()


def check_pairs(pair=None, format_output="table", detailed=False):
    """Check trading pairs.

    Args:
        pair: Specific pair to check (e.g., 'XBTZAR')
        format_output: Output format ('table' or 'json')
        detailed: Whether to show detailed information
    """
    try:
        # Initialize API client
        api_client = LunoAPIClient()

        # Get tickers
        tickers_response = api_client.get_tickers()

        if "tickers" not in tickers_response:
            logger.error("Failed to retrieve tickers")
            return 1

        tickers = tickers_response["tickers"]

        # Filter by pair if specified
        if pair:
            tickers = [t for t in tickers if t["pair"] == pair]
            if not tickers:
                logger.error(f"Pair {pair} not found")
                return 1

        if format_output == "table":
            # Basic information
            table_data = []
            for ticker in tickers:
                pair = ticker["pair"]
                bid = float(ticker.get("bid", 0))
                ask = float(ticker.get("ask", 0))
                last_trade = float(ticker.get("last_trade", 0))

                # Calculate spread
                spread = ask - bid if bid > 0 and ask > 0 else 0
                spread_percent = (spread / bid * 100) if bid > 0 else 0

                # 24h change
                change = 0
                if "timestamp" in ticker and ticker.get("rolling_24_hour_change"):
                    change = float(ticker["rolling_24_hour_change"])

                # Add to table
                table_data.append(
                    [
                        pair,
                        f"{bid:.2f}",
                        f"{ask:.2f}",
                        f"{last_trade:.2f}",
                        f"{spread:.2f} ({spread_percent:.2f}%)",
                        f"{change:.2f}%",
                    ]
                )

            # Print table
            print("\nLuno Trading Pairs")
            print("=" * 75)
            print(
                tabulate(
                    table_data,
                    headers=[
                        "Pair",
                        "Bid",
                        "Ask",
                        "Last Trade",
                        "Spread",
                        "24h Change",
                    ],
                    tablefmt="simple",
                )
            )
            print("=" * 75)
            print(f"Snapshot time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # If detailed and specific pair, show orderbook
            if detailed and pair:
                try:
                    orderbook = api_client.get_orderbook(pair)

                    if "asks" in orderbook and "bids" in orderbook:
                        print(f"\nOrderbook for {pair}")
                        print("=" * 75)

                        # Show top 5 asks
                        print("\nTop 5 Asks (Sell Orders)")
                        print("-" * 40)
                        asks_data = []
                        for ask in orderbook["asks"][:5]:
                            price = float(ask["price"])
                            volume = float(ask["volume"])
                            asks_data.append(
                                [
                                    f"{price:.2f}",
                                    f"{volume:.8f}",
                                    f"{price * volume:.2f}",
                                ]
                            )

                        print(
                            tabulate(
                                asks_data,
                                headers=["Price", "Volume", "Total"],
                                tablefmt="simple",
                            )
                        )

                        # Show top 5 bids
                        print("\nTop 5 Bids (Buy Orders)")
                        print("-" * 40)
                        bids_data = []
                        for bid in orderbook["bids"][:5]:
                            price = float(bid["price"])
                            volume = float(bid["volume"])
                            bids_data.append(
                                [
                                    f"{price:.2f}",
                                    f"{volume:.8f}",
                                    f"{price * volume:.2f}",
                                ]
                            )

                        print(
                            tabulate(
                                bids_data,
                                headers=["Price", "Volume", "Total"],
                                tablefmt="simple",
                            )
                        )

                        # Show recent trades if available
                        try:
                            trades = api_client.get_trades(pair)
                            if "trades" in trades and trades["trades"]:
                                print("\nRecent Trades")
                                print("-" * 60)
                                trades_data = []
                                for trade in trades["trades"][:5]:
                                    price = float(trade["price"])
                                    volume = float(trade["volume"])
                                    timestamp = datetime.fromtimestamp(
                                        int(trade["timestamp"]) / 1000
                                    )
                                    trades_data.append(
                                        [
                                            timestamp.strftime("%H:%M:%S"),
                                            trade["type"],
                                            f"{price:.2f}",
                                            f"{volume:.8f}",
                                            f"{price * volume:.2f}",
                                        ]
                                    )

                                print(
                                    tabulate(
                                        trades_data,
                                        headers=[
                                            "Time",
                                            "Type",
                                            "Price",
                                            "Volume",
                                            "Total",
                                        ],
                                        tablefmt="simple",
                                    )
                                )
                        except Exception as e:
                            print(f"Could not fetch recent trades: {e}")

                except Exception as e:
                    print(f"Could not fetch orderbook: {e}")

        else:  # JSON format
            # Build data structure for JSON output
            pairs_data = {"timestamp": datetime.now().isoformat(), "pairs": {}}

            for ticker in tickers:
                pair_name = ticker["pair"]

                # Basic ticker data
                pair_data = {
                    "bid": ticker.get("bid"),
                    "ask": ticker.get("ask"),
                    "last_trade": ticker.get("last_trade"),
                    "rolling_24_hour_change": ticker.get("rolling_24_hour_change"),
                }

                # Add orderbook if detailed and specific pair
                if detailed and pair and pair_name == pair:
                    try:
                        orderbook = api_client.get_orderbook(pair)
                        pair_data["orderbook"] = {
                            "asks": orderbook.get("asks", [])[:5],
                            "bids": orderbook.get("bids", [])[:5],
                        }

                        # Add recent trades
                        trades = api_client.get_trades(pair)
                        pair_data["recent_trades"] = trades.get("trades", [])[:5]
                    except Exception as e:
                        logger.warning(f"Could not fetch detailed data: {e}")

                pairs_data["pairs"][pair_name] = pair_data

            # Print JSON
            print(json.dumps(pairs_data, indent=2))

        return 0

    except Exception as e:
        logger.error(f"Error checking pairs: {e}")
        return 1


def main():
    """Main function."""
    # Create necessary directories
    Path("data/logs").mkdir(exist_ok=True, parents=True)

    # Get command line arguments
    args = setup_args()

    # Check pairs
    return check_pairs(
        pair=args.pair, format_output=args.format, detailed=args.detailed
    )


if __name__ == "__main__":
    sys.exit(main())
