"""
Status display for trading information.

Provides functions for displaying trading status in a visually appealing way.
"""

import time
import os
import platform
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Tuple
from collections import deque

# ANSI color codes
class Colors:
    RESET = "\033[0m"
    # Regular colors
    BLACK = "\033[0;30m"
    RED = "\033[0;31m"
    GREEN = "\033[0;32m"
    YELLOW = "\033[0;33m"
    BLUE = "\033[0;34m"
    MAGENTA = "\033[0;35m"
    CYAN = "\033[0;36m"
    WHITE = "\033[0;37m"
    # Bold colors
    BOLD_BLACK = "\033[1;30m"
    BOLD_RED = "\033[1;31m"
    BOLD_GREEN = "\033[1;32m"
    BOLD_YELLOW = "\033[1;33m"
    BOLD_BLUE = "\033[1;34m"
    BOLD_MAGENTA = "\033[1;35m"
    BOLD_CYAN = "\033[1;36m"
    BOLD_WHITE = "\033[1;37m"
    # Background colors
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"
    # Special
    BOLD = "\033[1m"
    UNDERLINE = "\033[4m"


# Keep a history of price movements for charting
price_history = deque(maxlen=60)  # Keep last 60 price points
trade_history = []  # Keep a list of trades


def clear_screen():
    """Clear the terminal screen based on OS."""
    os_name = platform.system()
    if os_name == 'Windows':
        os.system('cls')
    else:
        os.system('clear')


def format_price(price: float) -> str:
    """Format price with appropriate color based on value."""
    if price is None:
        return f"{Colors.WHITE}N/A{Colors.RESET}"
    if price == 0:
        return f"{Colors.WHITE}{price:.2f}{Colors.RESET}"
    elif price > 500000:  # High value in ZAR
        return f"{Colors.BOLD_GREEN}{price:.2f}{Colors.RESET}"
    elif price < 500000:  # Low value in ZAR
        return f"{Colors.GREEN}{price:.2f}{Colors.RESET}"
    return f"{Colors.WHITE}{price:.2f}{Colors.RESET}"


def format_percentage(value: float) -> str:
    """Format percentage with color based on positive/negative."""
    if value > 0:
        return f"{Colors.BOLD_GREEN}+{value:.2%}{Colors.RESET}"
    elif value < 0:
        return f"{Colors.BOLD_RED}{value:.2%}{Colors.RESET}"
    return f"{Colors.WHITE}{value:.2%}{Colors.RESET}"


def format_action(action: str) -> str:
    """Format action with appropriate color."""
    if action.lower() == 'buy':
        return f"{Colors.BOLD_GREEN}BUY{Colors.RESET}"
    elif action.lower() == 'sell':
        return f"{Colors.BOLD_RED}SELL{Colors.RESET}"
    elif action.lower() == 'hold':
        return f"{Colors.BOLD_YELLOW}HOLD{Colors.RESET}"
    return f"{Colors.WHITE}{action.upper()}{Colors.RESET}"


def format_status(status: str) -> str:
    """Format status with appropriate color."""
    status_lower = status.lower()
    if 'running' in status_lower or 'active' in status_lower or 'ok' in status_lower:
        return f"{Colors.BOLD_GREEN}{status}{Colors.RESET}"
    elif 'stopped' in status_lower or 'inactive' in status_lower:
        return f"{Colors.BOLD_YELLOW}{status}{Colors.RESET}"
    elif 'error' in status_lower or 'failed' in status_lower:
        return f"{Colors.BOLD_RED}{status}{Colors.RESET}"
    return f"{Colors.WHITE}{status}{Colors.RESET}"


def create_sparkline(values: List[float], width: int = 20) -> str:
    """Create a simple sparkline chart from values.
    
    Args:
        values: List of float values
        width: Width of the sparkline
        
    Returns:
        A string containing the sparkline
    """
    if not values:
        return " " * width
    
    # Unicode characters for sparklines
    # (from low to high)
    spark_chars = "▁▂▃▄▅▆▇█"
    
    # Find min and max for scaling
    min_val = min(values)
    max_val = max(values)
    
    if min_val == max_val:
        return spark_chars[len(spark_chars)//2] * width
    
    # Generate the sparkline
    sparkline = ""
    values_to_display = values[-width:] if len(values) > width else values
    
    # Scale to fit our spark characters
    for value in values_to_display:
        # Scale to 0-1 range
        scaled = (value - min_val) / (max_val - min_val)
        # Map to character index
        char_idx = min(int(scaled * (len(spark_chars) - 1)), len(spark_chars) - 1)
        sparkline += spark_chars[char_idx]
    
    # Add padding if needed
    if len(values_to_display) < width:
        sparkline = " " * (width - len(values_to_display)) + sparkline
    
    # Add color based on trend
    if values and len(values) >= 2 and values[-1] > values[0]:
        return f"{Colors.GREEN}{sparkline}{Colors.RESET}"
    elif values and len(values) >= 2 and values[-1] < values[0]:
        return f"{Colors.RED}{sparkline}{Colors.RESET}"
    else:
        return f"{Colors.WHITE}{sparkline}{Colors.RESET}"


def display_trade_status(
    status: Dict[str, Any],
    trading_pair: str,
    last_price: float,
    previous_price: float = 0,
    position: float = 0,
    signals: Optional[Dict[str, Any]] = None,
    trades: Optional[List[Dict[str, Any]]] = None,
    dry_run: bool = False,
):
    """Display trading status in a visually appealing format.
    
    Args:
        status: Current trader status dictionary
        trading_pair: Trading pair (e.g., 'XBTZAR')
        last_price: Last known price
        previous_price: Previous price for comparison
        position: Current position size
        signals: Recent trading signals
        trades: Recent completed trades
        dry_run: Whether in dry run mode
    """
    # Record price for history
    if last_price is not None and last_price > 0:
        price_history.append(last_price)
    
    # Add trades to history
    if trades:
        for trade in trades:
            if trade not in trade_history:
                trade_history.append(trade)
    
    # Clear screen and draw new status
    clear_screen()
    
    # Current time
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Calculate price change
    price_change = 0
    if last_price is not None and previous_price is not None and previous_price != 0:
        price_change = (last_price - previous_price) / previous_price
    price_change_str = format_percentage(price_change)
    
    # Draw title bar
    title = f" LUNO TRADING BOT - {trading_pair} "
    print(f"{Colors.BG_BLUE}{Colors.BOLD_WHITE}{title.center(80)}{Colors.RESET}")
    
    # Draw status bar
    if dry_run:
        mode_str = f"{Colors.BG_MAGENTA}{Colors.BOLD_WHITE} DRY RUN MODE {Colors.RESET}"
    else:
        mode_str = f"{Colors.BG_GREEN}{Colors.BOLD_WHITE} LIVE TRADING {Colors.RESET}"
    
    status_line = f" {current_time} | {mode_str} | Status: {format_status(status.get('status', 'Unknown'))}"
    print(f"{status_line}")
    print("─" * 80)
    
    # Draw price information
    print(f"{Colors.BOLD}MARKET DATA{Colors.RESET}")
    print(f"  Price: {format_price(last_price if last_price is not None else 0)} {trading_pair} ({price_change_str})")
    
    # Price chart
    if price_history:
        print(f"  Trend: {create_sparkline(list(price_history), width=50)}")
    
    print("─" * 80)
    
    # Position information
    print(f"{Colors.BOLD}POSITION{Colors.RESET}")
    if position > 0:
        print(f"  Current: {position} {trading_pair.split('Z')[0] if 'Z' in trading_pair else trading_pair[:3]}")
        value = position * last_price if last_price is not None and last_price > 0 else 0
        print(f"  Value: {format_price(value)} {trading_pair[3:] if len(trading_pair) > 3 else 'ZAR'}")
    else:
        print(f"  No active position")
    
    print("─" * 80)
    
    # Signals information
    print(f"{Colors.BOLD}SIGNALS{Colors.RESET}")
    if signals:
        action = signals.get('action', 'unknown')
        confidence = signals.get('confidence', 0)
        reason = signals.get('reason', 'unknown')
        print(f"  Action: {format_action(action)} | Confidence: {confidence:.2f} | Reason: {reason}")
    else:
        print(f"  No recent signals")
    
    print("─" * 80)
    
    # Recent trades
    print(f"{Colors.BOLD}RECENT TRADES{Colors.RESET}")
    if trade_history:
        # Get last 3 trades
        recent_trades = trade_history[-3:]
        for trade in recent_trades:
            trade_time = trade.get('timestamp', 'Unknown')
            action = trade.get('action', 'unknown')
            price = trade.get('price', 0)
            amount = trade.get('amount', 0)
            
            if isinstance(trade_time, datetime):
                trade_time = trade_time.strftime("%H:%M:%S")
                
            print(f"  {trade_time} | {format_action(action)} | {amount} @ {format_price(price)}")
    else:
        print(f"  No recent trades")
        
    print("─" * 80)
    
    # Error/warning information
    if 'error' in status and status['error']:
        print(f"{Colors.BG_RED}{Colors.BOLD_WHITE} ERROR {Colors.RESET} {status['error']}")


def refresh_status_display(trader):
    """Refresh trading status display using trader object data.
    
    Args:
        trader: The trader object with status information
    """
    # Extract relevant information from trader
    status = trader.status
    trading_pair = trader.pair
    last_price = status.get('last_price', 0)
    previous_price = status.get('previous_price', 0)
    position = trader.current_position
    
    # Get latest signals and trades
    signals = trader.last_signal if hasattr(trader, 'last_signal') else None
    
    # Get trades from history
    trades = trader.trade_history if hasattr(trader, 'trade_history') else []
    
    # Display the status
    display_trade_status(
        status=status,
        trading_pair=trading_pair,
        last_price=last_price,
        previous_price=previous_price,
        position=position,
        signals=signals,
        trades=trades,
        dry_run=trader.dry_run
    )