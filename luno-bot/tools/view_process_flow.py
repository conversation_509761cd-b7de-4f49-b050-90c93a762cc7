#!/usr/bin/env python3
"""
Luno Trading Bot - Trading Process Flow Viewer

This utility displays the trading process flow documentation in the terminal.
"""

import os
import sys
from pathlib import Path


def display_file(file_path):
    """Display the contents of a file in the terminal."""
    if not Path(file_path).exists():
        print(f"Error: File not found: {file_path}")
        return False

    try:
        with open(file_path, "r") as f:
            content = f.read()

        print(content)
        return True
    except Exception as e:
        print(f"Error reading file: {e}")
        return False


def main():
    """Main function."""
    # Get project root directory
    script_dir = Path(__file__).resolve().parent
    project_root = script_dir.parent

    # Path to the flow documentation
    flow_doc_path = project_root / "docs" / "TRADING_FLOW.md"

    print("\n" + "=" * 80)
    print("Luno Trading Bot - Process Flow Documentation")
    print("=" * 80 + "\n")

    # Display the file
    if not display_file(flow_doc_path):
        print(f"\nYou can also view the documentation at: {flow_doc_path}\n")

    print("\n" + "=" * 80)
    print("To run the trading bot, use: python run_trader.py --dry-run")
    print("=" * 80 + "\n")

    return 0


if __name__ == "__main__":
    sys.exit(main())
