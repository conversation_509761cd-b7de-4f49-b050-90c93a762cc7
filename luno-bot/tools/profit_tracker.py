#!/usr/bin/env python3
"""
Profit tracking utility for the Luno Trading Bot.

This script provides functionality to track profit and loss from trading activities.
"""

import argparse
import sys
import json
import os
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime, timedelta
from tabulate import tabulate

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.api_client import LunoAPIClient
from execution.order_manager import OrderManager
from config.logging_config import setup_logging

# Set up logging
logger = setup_logging("profit_tracker", "data/logs/tools.log")


def setup_args():
    """Set up command line arguments."""
    parser = argparse.ArgumentParser(description="Luno Profit Tracker")
    parser.add_argument(
        "--pair",
        type=str,
        default="XBTZAR",
        help="Trading pair to analyze (default: XBTZAR)",
    )
    parser.add_argument(
        "--days", type=int, default=30, help="Number of days to analyze (default: 30)"
    )
    parser.add_argument(
        "--format",
        choices=["table", "json"],
        default="table",
        help="Output format (default: table)",
    )
    parser.add_argument(
        "--plot",
        action="store_true",
        help="Generate profit/loss chart",
    )
    parser.add_argument(
        "--save",
        action="store_true",
        help="Save results to file",
    )
    return parser.parse_args()


def load_trade_history():
    """Load trade history from files.

    Returns:
        List of trade records
    """
    trades = []

    # Check for trade history files
    history_dir = Path("data/history")
    if not history_dir.exists():
        logger.warning("History directory does not exist")
        return trades

    # Find all trade history files
    trade_files = list(history_dir.glob("*_trades_*.json"))

    # Load each file
    for file_path in trade_files:
        try:
            with open(file_path, "r") as f:
                file_trades = json.load(f)
                trades.extend(file_trades)
        except Exception as e:
            logger.error(f"Error loading trade file {file_path}: {e}")

    return trades


def calculate_profit(trades, pair, days):
    """Calculate profit and loss from trades.

    Args:
        trades: List of trade records
        pair: Trading pair to analyze
        days: Number of days to analyze

    Returns:
        Dict with profit analysis
    """
    # Filter by pair and timeframe
    cutoff_date = datetime.now() - timedelta(days=days)

    filtered_trades = []
    for trade in trades:
        # Check if this trade is for the specified pair
        if "pair" in trade and trade["pair"] != pair:
            continue

        # Parse the timestamp
        trade_date = None
        if "timestamp" in trade:
            try:
                if isinstance(trade["timestamp"], str):
                    trade_date = datetime.fromisoformat(trade["timestamp"])
                else:
                    # Assume it's a timestamp
                    trade_date = datetime.fromtimestamp(trade["timestamp"] / 1000)
            except Exception:
                # If we can't parse the timestamp, skip this trade
                continue

        # Skip trades outside the timeframe
        if trade_date and trade_date < cutoff_date:
            continue

        filtered_trades.append(trade)

    # Initialize result
    result = {
        "pair": pair,
        "period_days": days,
        "trade_count": len(filtered_trades),
        "buy_count": 0,
        "sell_count": 0,
        "total_volume": 0.0,
        "total_cost": 0.0,
        "total_proceeds": 0.0,
        "realized_profit": 0.0,
        "realized_profit_percent": 0.0,
        "trades": filtered_trades,
        "daily_profit": {},
    }

    if not filtered_trades:
        return result

    # Track buys and sells
    for trade in filtered_trades:
        action = trade.get("action", "").lower()
        amount = float(trade.get("amount", 0))
        price = float(trade.get("price", 0))

        result["total_volume"] += amount

        if action == "buy":
            result["buy_count"] += 1
            result["total_cost"] += amount * price
        elif action == "sell":
            result["sell_count"] += 1
            result["total_proceeds"] += amount * price

    # Calculate profit
    result["realized_profit"] = result["total_proceeds"] - result["total_cost"]

    if result["total_cost"] > 0:
        result["realized_profit_percent"] = (
            result["realized_profit"] / result["total_cost"]
        ) * 100

    # Calculate daily profit
    daily_profit = {}

    for trade in filtered_trades:
        if "timestamp" not in trade:
            continue

        # Parse timestamp
        trade_date = None
        try:
            if isinstance(trade["timestamp"], str):
                trade_date = datetime.fromisoformat(trade["timestamp"])
            else:
                trade_date = datetime.fromtimestamp(trade["timestamp"] / 1000)
        except Exception:
            continue

        # Get date string
        date_str = trade_date.strftime("%Y-%m-%d")

        # Initialize if needed
        if date_str not in daily_profit:
            daily_profit[date_str] = {
                "buys": 0,
                "sells": 0,
                "volume": 0.0,
                "cost": 0.0,
                "proceeds": 0.0,
            }

        # Update daily stats
        action = trade.get("action", "").lower()
        amount = float(trade.get("amount", 0))
        price = float(trade.get("price", 0))

        daily_profit[date_str]["volume"] += amount

        if action == "buy":
            daily_profit[date_str]["buys"] += 1
            daily_profit[date_str]["cost"] += amount * price
        elif action == "sell":
            daily_profit[date_str]["sells"] += 1
            daily_profit[date_str]["proceeds"] += amount * price

    # Calculate daily profit
    for date_str, day_data in daily_profit.items():
        day_data["profit"] = day_data["proceeds"] - day_data["cost"]

        if day_data["cost"] > 0:
            day_data["profit_percent"] = (day_data["profit"] / day_data["cost"]) * 100
        else:
            day_data["profit_percent"] = 0.0

    # Add to result
    result["daily_profit"] = daily_profit

    return result


def display_profit(profit_data, format_output="table", plot=False, save=False):
    """Display profit analysis.

    Args:
        profit_data: Profit analysis data
        format_output: Output format ('table' or 'json')
        plot: Whether to generate a chart
        save: Whether to save results to file
    """
    if format_output == "table":
        # Print summary
        print(
            f"\nProfit Analysis for {profit_data['pair']} (Last {profit_data['period_days']} days)"
        )
        print("=" * 80)

        summary = [
            ["Total Trades", profit_data["trade_count"]],
            ["Buy Trades", profit_data["buy_count"]],
            ["Sell Trades", profit_data["sell_count"]],
            ["Total Volume", f"{profit_data['total_volume']:.8f}"],
            ["Total Cost", f"R {profit_data['total_cost']:.2f}"],
            ["Total Proceeds", f"R {profit_data['total_proceeds']:.2f}"],
            ["Realized Profit", f"R {profit_data['realized_profit']:.2f}"],
            ["Profit %", f"{profit_data['realized_profit_percent']:.2f}%"],
        ]

        print(tabulate(summary, tablefmt="simple"))

        # Print daily profit
        if profit_data["daily_profit"]:
            print("\nDaily Profit")
            print("-" * 80)

            daily_data = []
            for date_str, day_data in sorted(profit_data["daily_profit"].items()):
                daily_data.append(
                    [
                        date_str,
                        day_data["buys"],
                        day_data["sells"],
                        f"{day_data['volume']:.8f}",
                        f"R {day_data['cost']:.2f}",
                        f"R {day_data['proceeds']:.2f}",
                        f"R {day_data['profit']:.2f}",
                        f"{day_data['profit_percent']:.2f}%",
                    ]
                )

            print(
                tabulate(
                    daily_data,
                    headers=[
                        "Date",
                        "Buys",
                        "Sells",
                        "Volume",
                        "Cost",
                        "Proceeds",
                        "Profit",
                        "Profit %",
                    ],
                    tablefmt="simple",
                )
            )

    else:  # JSON format
        # Just print the JSON data
        # Remove the trades list to keep the output manageable
        output_data = profit_data.copy()
        output_data.pop("trades", None)
        print(json.dumps(output_data, indent=2))

    # Generate plot if requested
    if plot:
        generate_profit_chart(profit_data)

    # Save results if requested
    if save:
        save_profit_data(profit_data)


def generate_profit_chart(profit_data):
    """Generate profit chart.

    Args:
        profit_data: Profit analysis data
    """
    try:
        # Create a DataFrame from daily profit
        dates = []
        profits = []
        cumulative_profit = []

        running_total = 0.0

        for date_str, day_data in sorted(profit_data["daily_profit"].items()):
            dates.append(date_str)
            profits.append(day_data["profit"])

            running_total += day_data["profit"]
            cumulative_profit.append(running_total)

        # Create plot
        plt.figure(figsize=(12, 6))

        # Plot daily profit as bars
        ax1 = plt.subplot(111)
        ax1.bar(dates, profits, alpha=0.6, label="Daily Profit")
        ax1.set_ylabel("Daily Profit (ZAR)")
        ax1.set_xlabel("Date")
        ax1.tick_params(axis="x", rotation=45)

        # Plot cumulative profit as line on secondary y-axis
        ax2 = ax1.twinx()
        ax2.plot(dates, cumulative_profit, "r-", label="Cumulative Profit")
        ax2.set_ylabel("Cumulative Profit (ZAR)")

        # Add legends
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc="upper left")

        # Add title
        plt.title(
            f"Profit Analysis for {profit_data['pair']} (Last {profit_data['period_days']} days)"
        )

        # Add grid
        ax1.grid(True, linestyle="--", alpha=0.7)

        # Adjust layout
        plt.tight_layout()

        # Save chart
        chart_path = f"data/reports/profit_chart_{profit_data['pair']}_{datetime.now().strftime('%Y%m%d')}.png"

        # Ensure directory exists
        Path("data/reports").mkdir(exist_ok=True, parents=True)

        plt.savefig(chart_path)
        logger.info(f"Profit chart saved to {chart_path}")
        print(f"\nProfit chart saved to {chart_path}")

        # Show chart
        plt.show()

    except Exception as e:
        logger.error(f"Error generating profit chart: {e}")


def save_profit_data(profit_data):
    """Save profit data to file.

    Args:
        profit_data: Profit analysis data
    """
    try:
        # Ensure directory exists
        reports_dir = Path("data/reports")
        reports_dir.mkdir(exist_ok=True, parents=True)

        # Create filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"profit_analysis_{profit_data['pair']}_{timestamp}.json"
        filepath = reports_dir / filename

        # Save to file
        with open(filepath, "w") as f:
            json.dump(profit_data, f, indent=2)

        logger.info(f"Profit data saved to {filepath}")
        print(f"\nProfit data saved to {filepath}")

    except Exception as e:
        logger.error(f"Error saving profit data: {e}")


def main():
    """Main function."""
    # Create necessary directories
    Path("data/logs").mkdir(exist_ok=True, parents=True)

    # Get command line arguments
    args = setup_args()

    # Load trade history
    trades = load_trade_history()

    if not trades:
        logger.warning("No trade history found")
        print("No trade history found. Run the trader first to generate some trades.")
        return 1

    # Calculate profit
    profit_data = calculate_profit(trades, args.pair, args.days)

    # Display results
    display_profit(
        profit_data, format_output=args.format, plot=args.plot, save=args.save
    )

    return 0


if __name__ == "__main__":
    sys.exit(main())
