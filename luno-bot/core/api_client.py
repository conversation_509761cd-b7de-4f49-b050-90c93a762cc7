"""
API client for interacting with the Luno cryptocurrency exchange.

Provides both REST API and WebSocket functionality using official Luno SDKs.
"""

import os
import json
import time
import base64
import threading
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

# Import Luno SDK
import luno_python.client as luno
from luno_streams import Updater

# Import utility functions
from .utils import setup_logging

# Load environment variables from .env file
load_dotenv()

# Setup logging
logger = setup_logging("api_client", "data/logs/api.log")


class LunoAPIClient:
    """Client for interacting with the Luno API."""

    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None):
        """Initialize the Luno API client.

        Args:
            api_key: Luno API key. If not provided, it will be read from LUNO_API_KEY env var.
            api_secret: Luno API secret. If not provided, it will be read from LUNO_API_SECRET env var.
        """
        self.api_key = api_key or os.getenv("LUNO_API_KEY")
        self.api_secret = api_secret or os.getenv("LUNO_API_SECRET")

        if not self.api_key or not self.api_secret:
            raise ValueError(
                "API key and secret must be provided or set as environment variables"
            )

        # Initialize Luno client
        self.client = luno.Client(
            api_key_id=self.api_key, api_key_secret=self.api_secret
        )

        # Ensure log directory exists
        Path("data/logs").mkdir(exist_ok=True, parents=True)

    def get_tickers(self) -> Dict[str, Any]:
        """Get the ticker data for all markets."""
        try:
            response = self.client.get_tickers()
            # Convert response object to dict
            if hasattr(response, "__dict__"):
                tickers_data = response.__dict__
                if "tickers" in tickers_data and isinstance(
                    tickers_data["tickers"], list
                ):
                    return {"tickers": tickers_data["tickers"]}
                # Handle case where tickers might be a property
                elif hasattr(response, "tickers"):
                    return {"tickers": response.tickers}

            # Fallback: return raw response if not a proper object
            return {"tickers": response}
        except Exception as e:
            logger.error(f"Error getting tickers: {e}")
            raise

    def get_ticker(self, pair: str) -> Dict[str, Any]:
        """Get the ticker data for a specific market.

        Args:
            pair: Trading pair (e.g., 'XBTZAR' for Bitcoin-ZAR)
        """
        try:
            response = self.client.get_ticker(pair=pair)
            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                ticker_dict = response.__dict__
            else:
                ticker_dict = response

            # Format the response for compatibility
            return {
                "pair": pair,
                "timestamp": ticker_dict.get("timestamp", 0),
                "bid": ticker_dict.get("bid", "0"),
                "ask": ticker_dict.get("ask", "0"),
                "last_trade": ticker_dict.get("last_trade", "0"),
                "rolling_24_hour_volume": ticker_dict.get(
                    "rolling_24_hour_volume", "0"
                ),
                "rolling_24_hour_change": ticker_dict.get(
                    "rolling_24_hour_change", "0"
                ),
            }
        except Exception as e:
            logger.error(f"Error getting ticker for {pair}: {e}")
            raise

    def get_orderbook(self, pair: str) -> Dict[str, Any]:
        """Get the order book for a specific market.

        Args:
            pair: Trading pair (e.g., 'XBTZAR' for Bitcoin-ZAR)
        """
        try:
            response = self.client.get_orderbook_top(pair=pair)
            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                orderbook_dict = response.__dict__
            else:
                orderbook_dict = response

            return {
                "asks": orderbook_dict.get("asks", []),
                "bids": orderbook_dict.get("bids", []),
                "timestamp": orderbook_dict.get("timestamp", 0),
            }
        except Exception as e:
            logger.error(f"Error getting orderbook for {pair}: {e}")
            raise

    def get_trades(self, pair: str, since: Optional[int] = None) -> Dict[str, Any]:
        """Get recent trades for a market.

        Args:
            pair: Trading pair (e.g., 'XBTZAR' for Bitcoin-ZAR)
            since: Fetch trades executed after this trade ID
        """
        try:
            # Using list_trades method instead of get_trades (which doesn't exist)
            response = self.client.list_trades(pair=pair)

            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                trades_dict = response.__dict__
            else:
                trades_dict = response

            return {"trades": trades_dict.get("trades", [])}
        except Exception as e:
            logger.error(f"Error getting trades for {pair}: {e}")
            raise

    def get_candles(
        self, pair: str, duration: int = 60, since: int = None
    ) -> Dict[str, Any]:
        """Get candlestick data for a market.

        Args:
            pair: Trading pair (e.g., 'XBTZAR' for Bitcoin-ZAR)
            duration: Candle duration in seconds (default: 60)
            since: Unix timestamp from which to get candles (required by Luno API)
        """
        try:
            # If since is not provided, use a reasonable default (24h ago)
            if since is None:
                since = int(time.time() - 86400)  # 24 hours ago

            response = self.client.get_candles(
                pair=pair, duration=duration, since=since
            )
            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                candles_dict = response.__dict__
            else:
                candles_dict = response

            return {"candles": candles_dict.get("candles", [])}
        except Exception as e:
            logger.error(f"Error getting candles for {pair}: {e}")
            raise

    def get_balances(self) -> Dict[str, Any]:
        """Get the account balances."""
        try:
            response = self.client.get_balances()
            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                balances_dict = response.__dict__
            else:
                balances_dict = response

            return {"balance": balances_dict.get("balance", [])}
        except Exception as e:
            logger.error(f"Error getting balances: {e}")
            raise

    def get_pending_orders(self, pair: Optional[str] = None) -> Dict[str, Any]:
        """Get the list of pending orders.

        Args:
            pair: Trading pair to filter by (optional)
        """
        try:
            response = self.client.list_orders(pair=pair, state="PENDING")
            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                orders_dict = response.__dict__
            else:
                orders_dict = response

            return {"orders": orders_dict.get("orders", [])}
        except Exception as e:
            logger.error(f"Error getting pending orders: {e}")
            raise

    def get_order(self, order_id: str) -> Dict[str, Any]:
        """Get details of a specific order.

        Args:
            order_id: ID of the order
        """
        try:
            response = self.client.get_order(id=order_id)
            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                order_dict = response.__dict__
            else:
                order_dict = response

            # Format the response for compatibility
            return {
                "order_id": order_dict.get("order_id", ""),
                "state": order_dict.get("state", ""),
                "pair": order_dict.get("pair", ""),
                "type": order_dict.get("type", ""),
                "limit_price": order_dict.get("limit_price", "0"),
                "limit_volume": order_dict.get("limit_volume", "0"),
                "base": order_dict.get("base", "0"),
                "counter": order_dict.get("counter", "0"),
                "fee_base": order_dict.get("fee_base", "0"),
                "fee_counter": order_dict.get("fee_counter", "0"),
                "trades": order_dict.get("trades", []),
            }
        except Exception as e:
            logger.error(f"Error getting order details for {order_id}: {e}")
            raise

    def place_order(
        self, pair: str, type: str, volume: float, price: Optional[float] = None
    ) -> Dict[str, Any]:
        """Place a new order.

        Args:
            pair: Trading pair (e.g., 'XBTZAR')
            type: Order type ('BUY' or 'SELL')
            volume: Amount to buy or sell
            price: Limit price (can be None for market orders)
        """
        try:
            # Convert volume to string for SDK
            volume_str = str(volume)

            if price is not None:
                # Limit order
                price_str = str(price)
                if type.upper() == "BUY":
                    response = self.client.post_limit_buy_order(
                        pair=pair, price=price_str, volume=volume_str
                    )
                else:  # SELL
                    response = self.client.post_limit_sell_order(
                        pair=pair, price=price_str, volume=volume_str
                    )
            else:
                # Market order
                if type.upper() == "BUY":
                    # For market buy orders, we use counter_volume (fiat amount)
                    # For example, "buy 500 ZAR worth of BTC"
                    try:
                        # First try to get current price to estimate the volume
                        ticker = self.get_ticker(pair)
                        price = float(ticker.get("ask", 0))
                        
                        # Extract currency code from pair (e.g., "ZAR" from "XBTZAR")
                        counter_currency = pair[3:] if len(pair) > 3 else "ZAR"
                        
                        # Set minimum order size based on currency
                        # These are typical minimums but may need adjustment based on exchange requirements
                        min_amounts = {
                            "ZAR": 10,     # 10 ZAR
                            "MYR": 10,     # 10 MYR
                            "USD": 1,      # 1 USD
                            "EUR": 1,      # 1 EUR
                            "GBP": 1,      # 1 GBP
                        }
                        
                        # Get minimum amount for this currency or default to 10
                        min_amount = min_amounts.get(counter_currency, 10)
                        
                        if price > 0:
                            # Calculate counter_volume (amount in fiat)
                            # Format with 2 decimal places for fiat currencies
                            counter_amount = float(volume_str) * price
                            
                            # Ensure minimum order size
                            if counter_amount < min_amount:
                                counter_amount = min_amount
                                
                            # Format with 2 decimal places
                            counter_volume = str(round(counter_amount, 2))
                            
                            logger.info(f"Placing market buy order with counter volume: {counter_volume} {counter_currency}")
                            response = self.client.post_market_order(
                                pair=pair, type="BUY", counter_volume=counter_volume
                            )
                        else:
                            # If we can't calculate the counter volume, use a fixed minimum amount
                            counter_volume = str(min_amount)
                            logger.info(f"Using minimum counter volume: {counter_volume} {counter_currency}")
                            response = self.client.post_market_order(
                                pair=pair, type="BUY", counter_volume=counter_volume
                            )
                    except Exception as e:
                        logger.warning(f"Error in market buy order processing: {e}")
                        # Try with a fixed minimum amount as last resort
                        try:
                            # Extract currency code from pair
                            counter_currency = pair[3:] if len(pair) > 3 else "ZAR"
                            min_amount = 10  # Minimum 10 units of counter currency
                            
                            logger.info(f"Attempting fallback with fixed minimum: {min_amount} {counter_currency}")
                            response = self.client.post_market_order(
                                pair=pair, type="BUY", counter_volume=str(min_amount)
                            )
                        except Exception as e2:
                            logger.error(f"Fallback order also failed: {e2}")
                            raise  # Re-raise the exception to be handled by the caller
                else:  # SELL
                    # For market sell orders, we use base_volume (crypto amount)
                    # For example, "sell 0.1 BTC for whatever ZAR I can get"
                    
                    # Extract base currency from pair (e.g., "XBT" from "XBTZAR")
                    base_currency = pair[:3] if len(pair) >= 3 else "XBT"
                    
                    # Set minimum order size based on currency
                    min_amounts = {
                        "XBT": 0.0005,  # Minimum BTC
                        "ETH": 0.01,    # Minimum ETH
                        "LTC": 0.1,     # Minimum LTC
                        "XRP": 10,      # Minimum XRP
                    }
                    
                    # Get minimum amount for this currency or default to 0.001
                    min_amount = min_amounts.get(base_currency, 0.001)
                    
                    # Check if volume meets minimum requirement
                    volume_float = float(volume_str)
                    if volume_float < min_amount:
                        logger.info(f"Adjusting sell order to minimum amount: {min_amount} {base_currency}")
                        volume_str = str(min_amount)
                    
                    logger.info(f"Placing market sell order with base volume: {volume_str} {base_currency}")
                    response = self.client.post_market_order(
                        pair=pair, type="SELL", base_volume=volume_str
                    )

            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                order_dict = response.__dict__
            else:
                order_dict = response

            return {"order_id": order_dict.get("order_id", "")}
        except Exception as e:
            logger.error(f"Error placing {type} order for {volume} {pair}: {e}")
            raise

    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order.

        Args:
            order_id: ID of the order to cancel
        """
        try:
            response = self.client.stop_order(order_id=order_id)
            # Convert response to dict for compatibility
            if hasattr(response, "__dict__"):
                result_dict = response.__dict__
            else:
                result_dict = response

            return {"success": result_dict.get("success", False)}
        except Exception as e:
            logger.error(f"Error canceling order {order_id}: {e}")
            raise


class LunoWebSocketClient:
    """Luno WebSocket client for real-time market data."""

    def __init__(self, pair, on_message=None, on_error=None, on_close=None):
        """Initialize a new WebSocket client.

        Args:
            pair: Trading pair to subscribe to (e.g., 'XBTZAR')
            on_message: Callback function for messages
            on_error: Callback function for errors
            on_close: Callback function for connection close
        """
        self.pair = pair
        self.on_message = on_message
        self.on_error = on_error
        self.on_close = on_close
        self.running = False
        self.thread = None
        self.loop = None
        self.updater = None
        self.api_key = None
        self.api_secret = None

    def add_callback(self, callback):
        """Add a message callback.

        Args:
            callback: Function to call when a message is received
        """
        # Store the original callback
        original_callback = self.on_message

        # Create a new callback that calls both functions
        if original_callback:
            self.on_message = lambda message: (
                original_callback(message),
                callback(message),
            )
        else:
            self.on_message = callback

    def _process_message(self, message):
        """Process a message from the WebSocket."""
        try:
            if self.on_message:
                # For messages from hooks, they're already processed
                if isinstance(message, dict) and any(
                    key in message
                    for key in ["trade_updates", "asks_updates", "bids_updates"]
                ):
                    self.on_message(message)
                    return

                # For other message types (direct calls)
                if hasattr(message, "__dict__"):
                    # Convert to dict if it's an object
                    self.on_message(message.__dict__)
                else:
                    # Pass through other types
                    self.on_message(message)
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
            if self.on_error:
                self.on_error(e)

    def _run(self):
        """Run WebSocket loop in a thread."""
        # Create a new event loop for this thread
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

        try:
            # Initialize the updater
            self.updater = Updater(
                self.pair,
                self.api_key,
                self.api_secret,
                hooks={
                    "trades": lambda trades: self._process_message(
                        {"trade_updates": trades}
                    ),
                    "asks": lambda asks: self._process_message({"asks_updates": asks}),
                    "bids": lambda bids: self._process_message({"bids_updates": bids}),
                },
            )

            # Start WebSocket connection using connect and run
            logger.info(f"Starting WebSocket connection for {self.pair}")

            # Define the run coroutine
            async def run_updater():
                # Connect first
                await self.updater.connect()
                # Then run the updater
                await self.updater.run()

            # Start in the loop
            asyncio.run_coroutine_threadsafe(run_updater(), self.loop)

            # Run the event loop
            self.loop.run_forever()
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            # Clean up
            try:
                if self.loop and not self.loop.is_closed():
                    # Cancel all running tasks
                    for task in asyncio.all_tasks(self.loop):
                        task.cancel()

                    # Run the event loop until all tasks are cancelled
                    self.loop.run_until_complete(asyncio.sleep(0.1))
                    self.loop.close()
            except Exception as e:
                logger.error(f"Error closing event loop: {e}")

            logger.info(f"WebSocket thread for {self.pair} exited")

    def stop(self):
        """Stop the WebSocket client."""
        if self.running:
            logger.info(f"Stopping WebSocket connection for {self.pair}")
            self.running = False

            # Close the event loop
            if self.loop and self.thread and self.thread.is_alive():
                try:
                    # Stop the loop - this will cancel all tasks including our updater
                    self.loop.call_soon_threadsafe(self.loop.stop)
                except Exception as e:
                    logger.error(f"Error stopping event loop: {e}")

            # Wait for the thread to finish
            if self.thread and self.thread.is_alive():
                self.thread.join(timeout=5)

            logger.info(f"WebSocket connection for {self.pair} stopped")

    def start(self, callback=None):
        """Start the WebSocket client in a background thread.

        Args:
            callback: Optional function to call when a message is received
        """
        if self.running:
            logger.warning("WebSocket client is already running")
            return

        # Set callback if provided
        if callback:
            self.on_message = callback

        # Initialize API credentials
        self.api_key = os.getenv("LUNO_API_KEY")
        self.api_secret = os.getenv("LUNO_API_SECRET")

        if not self.api_key or not self.api_secret:
            raise ValueError("API key and secret required for WebSocket connection")

        # Start in a new thread
        self.running = True
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()

        # Small delay to allow connection to initialize
        time.sleep(0.5)
