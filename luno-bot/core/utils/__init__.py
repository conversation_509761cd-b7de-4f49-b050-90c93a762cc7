"""
Core utility functions for the Luno Trading Bot.

This package contains various utility modules used throughout the trading bot.
"""

from pathlib import Path
from .logging_utils import setup_logger as setup_logging

def ensure_directory(path):
    """Ensure a directory exists, creating it and its parents if necessary.
    
    Args:
        path: Directory path as string or Path object
        
    Returns:
        Path object of the directory
    """
    if not isinstance(path, Path):
        path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path