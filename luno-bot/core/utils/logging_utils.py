"""
Logging utility functions for the Luno Trading Bot.

This module provides functions for setting up and configuring loggers
with appropriate formatting for both console and file output.
"""

import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional, Union, Dict, Any


def setup_logger(
    name: str,
    log_file: Union[str, Path] = None,
    level: int = logging.INFO,
    console: bool = True,
    file_mode: str = "a",
    log_format: str = None,
) -> logging.Logger:
    """Set up and configure a logger.

    Args:
        name: Name of the logger
        log_file: Path to the log file (optional)
        level: Logging level (default: INFO)
        console: Whether to log to console (default: True)
        file_mode: File opening mode (default: append)
        log_format: Custom log format (optional)

    Returns:
        Configured logger
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Clear any existing handlers
    logger.handlers = []
    
    # Create formatter
    if log_format is None:
        log_format = "[%(asctime)s] %(levelname)s [%(name)s] %(message)s"
    formatter = logging.Formatter(log_format, datefmt="%Y-%m-%d %H:%M:%S")
    
    # Add console handler if requested
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # Add file handler if a log file is specified
    if log_file:
        # Ensure parent directory exists
        if isinstance(log_file, str):
            log_file = Path(log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(str(log_file), mode=file_mode)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_trader_logger(
    trader_name: str,
    config: Optional[Dict[str, Any]] = None,
    level: int = None,
) -> logging.Logger:
    """Get a preconfigured logger for a trader.

    Args:
        trader_name: Name of the trader (used as logger name and in filename)
        config: Configuration dictionary (optional)
        level: Logging level (overrides config if provided)

    Returns:
        Configured logger for the trader
    """
    # Set default log level
    if level is None:
        if config and "logging" in config:
            level_str = config.get("logging", {}).get("level", "INFO")
            level = getattr(logging, level_str.upper(), logging.INFO)
        else:
            level = logging.INFO
    
    # Set up log file
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = Path(f"data/logs/{trader_name}_{timestamp}.log")
    
    # Get log format from config
    log_format = None
    if config and "logging" in config:
        log_format = config.get("logging", {}).get("format", None)
    
    # Create and return logger
    return setup_logger(
        name=trader_name,
        log_file=log_file,
        level=level,
        console=True,
        log_format=log_format,
    )


def rotating_log_setup(
    name: str,
    log_file: Union[str, Path],
    max_bytes: int = 5 * 1024 * 1024,  # 5 MB
    backup_count: int = 5,
    level: int = logging.INFO,
    console: bool = True,
    log_format: str = None,
) -> logging.Logger:
    """Set up a rotating file logger.

    Args:
        name: Name of the logger
        log_file: Path to the log file
        max_bytes: Maximum size in bytes before rotation (default: 5 MB)
        backup_count: Number of backup files to keep (default: 5)
        level: Logging level (default: INFO)
        console: Whether to log to console (default: True)
        log_format: Custom log format (optional)

    Returns:
        Configured logger with rotating file handler
    """
    from logging.handlers import RotatingFileHandler
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Clear any existing handlers
    logger.handlers = []
    
    # Create formatter
    if log_format is None:
        log_format = "[%(asctime)s] %(levelname)s [%(name)s] %(message)s"
    formatter = logging.Formatter(log_format, datefmt="%Y-%m-%d %H:%M:%S")
    
    # Add console handler if requested
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # Ensure parent directory exists
    if isinstance(log_file, str):
        log_file = Path(log_file)
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Add rotating file handler
    file_handler = RotatingFileHandler(
        str(log_file),
        maxBytes=max_bytes,
        backupCount=backup_count
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger