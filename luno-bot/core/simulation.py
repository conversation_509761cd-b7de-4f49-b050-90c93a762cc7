"""
Simulation module for Luno Trading Bot.

This module provides simulated market data, order execution, and account management
for dry-run testing of trading strategies without making actual API calls or
placing real orders.
"""

import time
import uuid
import random
import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

class SimulatedMarket:
    """Simulated market for testing trading strategies without using real money."""
    
    def __init__(self, pair: str = "XBTZAR", price_volatility: float = 0.002):
        """Initialize the simulated market.
        
        Args:
            pair: Trading pair to simulate
            price_volatility: Amount of random price movement (0.01 = 1%)
        """
        self.pair = pair
        self.price_volatility = price_volatility
        self.base_currency = pair[:3]  # e.g., XBT from XBTZAR
        self.quote_currency = pair[3:] # e.g., ZAR from XBTZAR
        
        # Initial price from a reasonable starting point
        self.current_price = self._get_initial_price()
        self.last_update = datetime.now()
        
        # Order book and trade history
        self.order_book = {
            "bids": [],
            "asks": []
        }
        self.trade_history = []
        
        # Simulate price movement every few seconds
        self._update_price()
    
    def _get_initial_price(self) -> float:
        """Get an initial price based on the trading pair."""
        # Realistic starting prices for common pairs
        pair_prices = {
            "XBTZAR": 800000.0,
            "ETHZAR": 35000.0,
            "XRPZAR": 8.0,
            "LTCZAR": 1200.0,
            "XBTMYR": 200000.0,
            "ETHMYR": 9000.0,
        }
        return pair_prices.get(self.pair, 10000.0)  # Default for unknown pairs
    
    def _update_price(self) -> float:
        """Update the current price with realistic volatility."""
        # Time-based update (more time = more potential movement)
        time_elapsed = (datetime.now() - self.last_update).total_seconds()
        
        # Calculate price movement (more time = more potential movement)
        volatility_factor = self.price_volatility * min(time_elapsed / 10.0, 1.0)
        movement = random.normalvariate(0, volatility_factor)
        
        # Update price with drift slightly upward on average (0.0001%)
        self.current_price *= (1 + movement + 0.000001)
        self.last_update = datetime.now()
        
        # Generate simulated order book based on new price
        self._update_order_book()
        
        return self.current_price
    
    def _update_order_book(self):
        """Update the simulated order book based on current price."""
        price = self.current_price
        
        # Clear existing order book
        self.order_book = {
            "bids": [],
            "asks": []
        }
        
        # Generate bids (buy orders) - slightly below current price
        for i in range(20):
            bid_price = price * (1 - 0.001 * (i + 1) - random.random() * 0.001)
            volume = random.uniform(0.001, 0.1) * (0.9 ** i)  # Decreasing volume
            self.order_book["bids"].append({
                "price": bid_price,
                "volume": volume
            })
        
        # Generate asks (sell orders) - slightly above current price
        for i in range(20):
            ask_price = price * (1 + 0.001 * (i + 1) + random.random() * 0.001)
            volume = random.uniform(0.001, 0.1) * (0.9 ** i)  # Decreasing volume
            self.order_book["asks"].append({
                "price": ask_price,
                "volume": volume
            })
    
    def get_ticker(self) -> Dict[str, Any]:
        """Get current ticker information.
        
        Returns:
            Dict containing ticker data
        """
        self._update_price()
        
        # Calculate 24h change (simulate 0.5-2% daily volatility)
        daily_change_pct = random.normalvariate(0.0, 0.01)  # Mean 0, std 1%
        
        return {
            "pair": self.pair,
            "timestamp": int(time.time() * 1000),
            "bid": self.current_price * 0.999,
            "ask": self.current_price * 1.001,
            "last_trade": self.current_price,
            "rolling_24_hour_volume": random.uniform(10, 100),
            "status": "ACTIVE",
            "change_24h": daily_change_pct
        }
    
    def get_order_book(self, depth: int = 100) -> Dict[str, Any]:
        """Get simulated order book.
        
        Args:
            depth: Number of orders to return
        
        Returns:
            Dict containing order book data
        """
        self._update_price()
        
        return {
            "timestamp": int(time.time() * 1000),
            "bids": self.order_book["bids"][:depth],
            "asks": self.order_book["asks"][:depth]
        }
    
    def get_trades(self, since: Optional[int] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get simulated recent trades.
        
        Args:
            since: Timestamp to get trades since (ms)
            limit: Maximum number of trades to return
        
        Returns:
            List of trade dictionaries
        """
        self._update_price()
        
        # Generate some random trades around current price
        trades = []
        now_ms = int(time.time() * 1000)
        
        for i in range(limit):
            # Random timestamp within last hour
            timestamp = now_ms - random.randint(0, 3600000)
            if since and timestamp < since:
                continue
                
            # Random price close to current price
            price = self.current_price * (1 + random.normalvariate(0, 0.005))
            
            # Random volume
            volume = random.uniform(0.001, 0.1)
            
            trades.append({
                "timestamp": timestamp,
                "price": price,
                "volume": volume,
                "is_buy": random.choice([True, False])
            })
        
        # Sort by timestamp (newest first)
        return sorted(trades, key=lambda x: x["timestamp"], reverse=True)
    
    def get_candles(self, 
                   interval: int = 300, 
                   since: Optional[int] = None, 
                   until: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get simulated historical candles.
        
        Args:
            interval: Candle interval in seconds
            since: Start timestamp in milliseconds
            until: End timestamp in milliseconds
        
        Returns:
            List of candle dictionaries
        """
        # Use current price as reference
        current_price = self.current_price
        
        # Set default time range if not provided (last 24 hours)
        now_ms = int(time.time() * 1000)
        until = until or now_ms
        since = since or (until - 24 * 60 * 60 * 1000)  # 24 hours before until
        
        # Calculate number of candles
        duration_ms = until - since
        num_candles = duration_ms // (interval * 1000)
        
        # Generate candles
        candles = []
        timestamp = since
        
        # Start price (work backwards from current)
        price = current_price / (1 + random.normalvariate(0, 0.02))  # Random start ~2% from current
        
        for i in range(int(num_candles)):
            # Random price movement for this candle
            candle_volatility = self.price_volatility * (interval / 300) ** 0.5  # Scale by sqrt of time
            price_change = random.normalvariate(0, candle_volatility)
            
            # Calculate candle price points
            open_price = price
            close_price = price * (1 + price_change)
            high_price = max(open_price, close_price) * (1 + random.uniform(0, candle_volatility))
            low_price = min(open_price, close_price) * (1 - random.uniform(0, candle_volatility))
            
            # Random volume
            volume = random.uniform(0.1, 2.0) * (interval / 300)
            
            candles.append({
                "timestamp": timestamp,
                "open": open_price,
                "high": high_price,
                "low": low_price,
                "close": close_price,
                "volume": volume
            })
            
            # Move to next candle
            timestamp += interval * 1000
            price = close_price
        
        return candles


class SimulatedAccount:
    """Simulated trading account for dry runs."""
    
    def __init__(self, pair: str = "XBTZAR", initial_balances: Optional[Dict[str, float]] = None):
        """Initialize the simulated account.
        
        Args:
            pair: Trading pair (e.g., XBTZAR)
            initial_balances: Dictionary of initial balances
        """
        self.pair = pair
        self.base_currency = pair[:3]  # e.g., XBT from XBTZAR
        self.quote_currency = pair[3:] # e.g., ZAR from XBTZAR
        
        # Set default initial balances if not provided
        if initial_balances is None:
            initial_balances = {
                self.base_currency: 0.1,       # e.g., 0.1 XBT
                self.quote_currency: 100000.0  # e.g., 100,000 ZAR
            }
        
        self.balances = initial_balances
        self.pending_orders = {}
        self.order_history = []
        self.trade_history = []
        
        # Fee structure (simulate exchange fees)
        self.maker_fee = 0.001  # 0.1%
        self.taker_fee = 0.002  # 0.2%
        
        # Logger
        self.logger = logging.getLogger("simulated_account")
    
    def get_balances(self) -> List[Dict[str, Any]]:
        """Get account balances.
        
        Returns:
            List of balance dictionaries
        """
        result = []
        for currency, amount in self.balances.items():
            result.append({
                "asset": currency,
                "balance": amount,
                "reserved": sum(order["volume"] for order in self.pending_orders.values() 
                               if order["type"] == "sell" and order["pair"].startswith(currency))
            })
        return result
    
    def place_order(self, order_type: str, volume: float, price: Optional[float] = None) -> Dict[str, Any]:
        """Place a simulated order.
        
        Args:
            order_type: Type of order ('buy' or 'sell')
            volume: Amount to buy or sell
            price: Limit price (None for market orders)
        
        Returns:
            Order details dictionary
        """
        # Generate order ID
        order_id = str(uuid.uuid4())
        
        # Record the time
        timestamp = int(time.time() * 1000)
        
        # Create the order
        order = {
            "order_id": order_id,
            "creation_timestamp": timestamp,
            "expiration_timestamp": timestamp + (7 * 24 * 60 * 60 * 1000),  # 7 days
            "type": order_type.lower(),
            "state": "PENDING",
            "pair": self.pair,
            "price": price,
            "volume": volume,
            "filled_volume": 0.0,
            "fee_base": 0.0,
            "fee_counter": 0.0,
        }
        
        # Add to pending orders
        self.pending_orders[order_id] = order
        
        # Log the order
        self.logger.info(f"Placed {order_type} order: {order_id} for {volume} {self.base_currency} at {price} {self.quote_currency}")
        
        # For simulation, we'll execute market orders immediately
        if price is None or order_type.lower() == "market":
            self._execute_market_order(order_id)
        
        return order
    
    def _execute_market_order(self, order_id: str) -> None:
        """Execute a market order immediately.
        
        Args:
            order_id: ID of the order to execute
        """
        if order_id not in self.pending_orders:
            return
        
        order = self.pending_orders[order_id]
        order_type = order["type"]
        volume = order["volume"]
        
        # Get current market price from a simulated market
        market = SimulatedMarket(self.pair)
        ticker = market.get_ticker()
        
        # Use appropriate price based on order type
        price = ticker["bid"] if order_type == "sell" else ticker["ask"]
        
        # Calculate fees
        fee_percentage = self.taker_fee  # Use taker fee for market orders
        
        if order_type == "buy":
            # Calculate total cost including fees
            total_cost = price * volume * (1 + fee_percentage)
            fee_amount = price * volume * fee_percentage
            
            # Check if enough funds
            if self.balances.get(self.quote_currency, 0) >= total_cost:
                # Update balances
                self.balances[self.quote_currency] = self.balances.get(self.quote_currency, 0) - total_cost
                self.balances[self.base_currency] = self.balances.get(self.base_currency, 0) + volume
                
                # Update order
                order["price"] = price
                order["state"] = "COMPLETE"
                order["filled_volume"] = volume
                order["fee_counter"] = fee_amount
                
                # Log success
                self.logger.info(f"Executed buy order {order_id}: {volume} {self.base_currency} at {price} {self.quote_currency}")
            else:
                # Not enough funds
                order["state"] = "FAILED"
                self.logger.warning(f"Buy order {order_id} failed: Insufficient funds")
        
        elif order_type == "sell":
            # Calculate total proceeds after fees
            total_proceeds = price * volume * (1 - fee_percentage)
            fee_amount = price * volume * fee_percentage
            
            # Check if enough base currency
            if self.balances.get(self.base_currency, 0) >= volume:
                # Update balances
                self.balances[self.base_currency] = self.balances.get(self.base_currency, 0) - volume
                self.balances[self.quote_currency] = self.balances.get(self.quote_currency, 0) + total_proceeds
                
                # Update order
                order["price"] = price
                order["state"] = "COMPLETE"
                order["filled_volume"] = volume
                order["fee_counter"] = fee_amount
                
                # Log success
                self.logger.info(f"Executed sell order {order_id}: {volume} {self.base_currency} at {price} {self.quote_currency}")
            else:
                # Not enough base currency
                order["state"] = "FAILED"
                self.logger.warning(f"Sell order {order_id} failed: Insufficient {self.base_currency}")
        
        # Move to order history
        self.order_history.append(order)
        del self.pending_orders[order_id]
        
        # Add to trade history if completed
        if order["state"] == "COMPLETE":
            trade = {
                "order_id": order_id,
                "timestamp": int(time.time() * 1000),
                "price": price,
                "volume": volume,
                "is_buy": order_type == "buy",
                "fee": fee_amount
            }
            self.trade_history.append(trade)
    
    def get_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get details of a specific order.
        
        Args:
            order_id: ID of the order to retrieve
        
        Returns:
            Order details dictionary or None if not found
        """
        # Check pending orders
        if order_id in self.pending_orders:
            return self.pending_orders[order_id]
        
        # Check order history
        for order in self.order_history:
            if order["order_id"] == order_id:
                return order
        
        return None
    
    def get_pending_orders(self) -> List[Dict[str, Any]]:
        """Get all pending orders.
        
        Returns:
            List of pending order dictionaries
        """
        return list(self.pending_orders.values())
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel a pending order.
        
        Args:
            order_id: ID of the order to cancel
        
        Returns:
            True if successfully cancelled, False otherwise
        """
        if order_id in self.pending_orders:
            order = self.pending_orders[order_id]
            order["state"] = "CANCELLED"
            
            # Move to order history
            self.order_history.append(order)
            del self.pending_orders[order_id]
            
            self.logger.info(f"Cancelled order {order_id}")
            return True
        
        return False


class SimulatedLunoClient:
    """Simulated Luno client for dry-run testing."""
    
    def __init__(self, pair: str = "XBTZAR"):
        """Initialize the simulated Luno client.
        
        Args:
            pair: Trading pair to simulate
        """
        self.pair = pair
        self.market = SimulatedMarket(pair)
        self.account = SimulatedAccount(pair)
        self.logger = logging.getLogger("simulated_luno")
        
    def get_tickers(self) -> Dict[str, Any]:
        """Get tickers for all trading pairs.
        
        Returns:
            Dict containing ticker data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.1, 0.3))
        
        ticker = self.market.get_ticker()
        return {
            "tickers": [ticker]
        }
    
    def get_ticker(self, pair: Optional[str] = None) -> Dict[str, Any]:
        """Get ticker for a specific trading pair.
        
        Args:
            pair: Trading pair (defaults to the instance pair)
        
        Returns:
            Dict containing ticker data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.1, 0.3))
        
        use_pair = pair or self.pair
        return self.market.get_ticker()
    
    def get_orderbook(self, pair: Optional[str] = None) -> Dict[str, Any]:
        """Get order book for a specific trading pair.
        
        Args:
            pair: Trading pair (defaults to the instance pair)
        
        Returns:
            Dict containing order book data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.2, 0.5))
        
        use_pair = pair or self.pair
        return self.market.get_order_book()
    
    def get_trades(self, pair: Optional[str] = None, since: Optional[int] = None) -> Dict[str, Any]:
        """Get recent trades for a specific trading pair.
        
        Args:
            pair: Trading pair (defaults to the instance pair)
            since: Timestamp to get trades since (ms)
        
        Returns:
            Dict containing trades data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.2, 0.5))
        
        use_pair = pair or self.pair
        trades = self.market.get_trades(since)
        return {
            "trades": trades
        }
    
    def get_balances(self) -> Dict[str, Any]:
        """Get account balances.
        
        Returns:
            Dict containing balance data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.3, 0.7))
        
        return {
            "balance": self.account.get_balances()
        }
    
    def get_pending_orders(self, pair: Optional[str] = None) -> Dict[str, Any]:
        """Get pending orders.
        
        Args:
            pair: Trading pair filter (defaults to the instance pair)
        
        Returns:
            Dict containing order data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.3, 0.7))
        
        use_pair = pair or self.pair
        orders = self.account.get_pending_orders()
        if pair:
            orders = [order for order in orders if order["pair"] == pair]
        
        return {
            "orders": orders
        }
    
    def get_order(self, order_id: str) -> Dict[str, Any]:
        """Get specific order details.
        
        Args:
            order_id: ID of the order to retrieve
        
        Returns:
            Dict containing order data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.2, 0.5))
        
        order = self.account.get_order(order_id)
        if order:
            return order
        else:
            raise ValueError(f"Order not found: {order_id}")
    
    def place_market_order(self, pair: str, type: str, volume: float) -> Dict[str, Any]:
        """Place a market order.
        
        Args:
            pair: Trading pair
            type: Order type ('buy' or 'sell')
            volume: Amount to buy or sell
        
        Returns:
            Dict containing order data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.5, 1.0))
        
        order = self.account.place_order(type, volume, None)
        return {
            "order_id": order["order_id"],
            "state": order["state"]
        }
    
    def place_limit_order(self, pair: str, type: str, price: float, volume: float) -> Dict[str, Any]:
        """Place a limit order.
        
        Args:
            pair: Trading pair
            type: Order type ('buy' or 'sell')
            price: Limit price
            volume: Amount to buy or sell
        
        Returns:
            Dict containing order data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.5, 1.0))
        
        order = self.account.place_order(type, volume, price)
        return {
            "order_id": order["order_id"],
            "state": order["state"]
        }
    
    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order.
        
        Args:
            order_id: ID of the order to cancel
        
        Returns:
            Dict containing success status
        """
        # Simulate response delay
        time.sleep(random.uniform(0.3, 0.7))
        
        success = self.account.cancel_order(order_id)
        return {
            "success": success
        }
    
    def get_candles(self, pair: str, duration: int, since: Optional[int] = None) -> Dict[str, List[Dict[str, Any]]]:
        """Get historical candles.
        
        Args:
            pair: Trading pair
            duration: Candle duration in seconds
            since: Start timestamp in milliseconds
        
        Returns:
            Dict containing candle data
        """
        # Simulate response delay
        time.sleep(random.uniform(0.5, 1.5))
        
        candles = self.market.get_candles(duration, since)
        return {
            "candles": candles
        }

def get_simulated_price_data(pair: str, days: int = 30, interval: int = 3600) -> pd.DataFrame:
    """Generate simulated historical price data for backtesting.
    
    Args:
        pair: Trading pair to simulate
        days: Number of days of data to generate
        interval: Candle interval in seconds
    
    Returns:
        DataFrame containing OHLCV data
    """
    market = SimulatedMarket(pair)
    
    # Calculate timestamps
    end_time = int(time.time() * 1000)
    start_time = end_time - (days * 24 * 60 * 60 * 1000)
    
    # Get candles
    candles = market.get_candles(interval, start_time, end_time)
    
    # Convert to DataFrame
    df = pd.DataFrame(candles)
    
    # Convert timestamp to datetime
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)
    
    return df

def create_simulation_environment(pair: str = "XBTZAR") -> Tuple[SimulatedMarket, SimulatedAccount]:
    """Create a complete simulation environment for dry-run testing.
    
    Args:
        pair: Trading pair to simulate
    
    Returns:
        Tuple of (SimulatedMarket, SimulatedAccount)
    """
    # Create simulated components
    market = SimulatedMarket(pair)
    account = SimulatedAccount(pair)
    
    # Log initialization
    logger = logging.getLogger("simulation")
    logger.info(f"Created simulation environment for {pair}")
    logger.info(f"Initial price: {market.current_price:.2f}")
    logger.info(f"Initial balances: {account.balances}")
    
    return market, account