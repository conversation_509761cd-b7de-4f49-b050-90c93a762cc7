"""
Core module for Luno Trading Bot.

This package contains essential components for interacting with the Luno exchange,
processing market data, and executing trading strategies.
"""

from pathlib import Path

# Ensure required directories exist
Path("data/logs").mkdir(exist_ok=True, parents=True)
Path("data/orders").mkdir(exist_ok=True, parents=True)
Path("data/history").mkdir(exist_ok=True, parents=True)
Path("data/snapshots").mkdir(exist_ok=True, parents=True)

# Package version
__version__ = "1.0.0"