# Luno Trading Bot

A cryptocurrency trading bot for the Luno exchange with a feature-based directory structure, robust error handling, and extensible architecture. Uses the official Luno SDK for API access.

## Project Structure

The project follows a feature-based organization pattern:

```
luno-bot/
├── config/             # Configuration settings
├── core/               # Core functionality
├── docs/               # Documentation
├── execution/          # Trade execution components
├── models/             # Prediction models
├── tools/              # Utility tools
├── traders/            # Trading strategies
├── run_trader.py       # Main entry point
└── run_backtest.py     # Backtesting functionality
```

## Key Components

### Traders

- `traders/base_trader.py`: Base class with core trading functionality
- `traders/realtime_trader.py`: Real-time trading implementation

### Execution

- `execution/risk_manager.py`: Risk management controls
- `execution/order_manager.py`: Order tracking and management
- `execution/trade_executor.py`: High-level trade execution

### Tools

- `tools/balance_checker.py`: Check account balances
- `tools/pair_checker.py`: Check supported trading pairs
- `tools/trade_status.py`: Monitor orders and trades
- `tools/profit_tracker.py`: Track and analyze trading performance

## Luno SDK Integration

The bot uses the official Luno API SDKs:

- `luno-python`: For REST API interactions
- `luno_streams`: For WebSocket real-time data

This provides a more reliable and maintainable connection to the Luno exchange.

### Testing SDK Integration

You can test the SDK integration using the provided test script:

```bash
# Run the SDK test script
python test_luno_sdk.py
```

This will verify that both the REST API and WebSocket connections are working correctly.

### Updating Dependencies

If you encounter any issues with the SDK integration, you can update all dependencies by running:

```bash
# Make the script executable (if needed)
chmod +x update_deps.sh

# Run the update script
./update_deps.sh
```

This will ensure you have all the required packages with the correct versions.

## Usage

### Running the Bot

The main entry point for the trading bot is `run_trader.py`, which supports a variety of command-line arguments to customize your trading strategy:

```bash
# Basic usage - run in dry-run mode (no actual trades)
python run_trader.py --dry-run

# Run with a specific trading pair
python run_trader.py --pair ETHZAR --dry-run

# Adjust trade checking frequency (in seconds)
python run_trader.py --dry-run --check-interval 60

# Choose a specific prediction model
python run_trader.py --dry-run --predictor rsi

# Set custom risk parameters
python run_trader.py --dry-run --stop-loss 2.0 --take-profit 3.0

# Complete example with multiple parameters
python run_trader.py --pair XBTZAR --amount 0.001 --check-interval 300 --predictor ensemble --dry-run
```

#### Dedicated Live Trading Scripts

For those who want to use a single trading strategy with guaranteed live trading (bypassing any dry-run settings), use the dedicated scripts:

##### Aggressive Live Trading

```bash
# Run aggressive trading strategy with default settings (XBTMYR)
./aggressive.sh
# or
./run_aggressive_live.py

# Run with custom parameters
./aggressive.sh --pair XBTMYR --stop-loss 0.02 --take-profit 0.03 --amount 0.001
```

Default settings for aggressive strategy:
- Trading Pair: XBTMYR
- Stop Loss: 1.5%
- Take Profit: 2.5%
- Trade Amount: 0.001
- Check Interval: 180 seconds (3 minutes)

##### Conservative Live Trading

```bash
# Run conservative trading strategy with default settings (XBTZAR)
./conservative.sh
# or
./run_conservative_live.py

# Run with custom parameters
./conservative.sh --pair XBTZAR --stop-loss 0.015 --take-profit 0.025 --amount 0.001
```

Default settings for conservative strategy:
- Trading Pair: XBTZAR
- Stop Loss: 1.2%
- Take Profit: 2.0%
- Trade Amount: 0.001
- Check Interval: 900 seconds (15 minutes)

**WARNING**: These scripts will execute real trades with real money on your Luno account.

See detailed documentation in `AGGRESSIVE_LIVE_TRADING.md` and `CONSERVATIVE_LIVE_TRADING.md`.

For a detailed explanation of how the trading bot processes market data and executes trades, see the [Trading Flow Process](docs/TRADING_FLOW.md) documentation.

#### Command-line Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--pair` | Trading pair (e.g., XBTZAR, ETHZAR) | XBTZAR |
| `--amount` | Amount to trade per transaction | 0.001 |
| `--check-interval` | Time between market checks (seconds) | 300 |
| `--predictor` | Prediction model (sma, ema, rsi, macd, ensemble) | ensemble |
| `--stop-loss` | Stop loss percentage | 2.0 |
| `--take-profit` | Take profit percentage | 3.0 |
| `--use-market-structure` | Enable market structure analysis | False |
| `--no-websocket` | Disable WebSocket (use polling instead) | False |
| `--dry-run` | Run in simulation mode (no real trades) | False |

#### Live Trading Script Arguments

Both the aggressive and conservative live trading scripts support similar arguments:

##### Aggressive Trader Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--pair` | Trading pair for aggressive trader | XBTMYR |
| `--interval` | Check interval in seconds | 180 |
| `--stop-loss` | Stop loss percentage | 0.015 (1.5%) |
| `--take-profit` | Take profit percentage | 0.025 (2.5%) |
| `--amount` | Amount to trade per transaction | 0.001 |
| `--yes-i-know-this-uses-real-money` | Skip confirmation prompt | False |

##### Conservative Trader Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--pair` | Trading pair for conservative trader | XBTZAR |
| `--interval` | Check interval in seconds | 900 |
| `--stop-loss` | Stop loss percentage | 0.012 (1.2%) |
| `--take-profit` | Take profit percentage | 0.020 (2.0%) |
| `--amount` | Amount to trade per transaction | 0.001 |
| `--yes-i-know-this-uses-real-money` | Skip confirmation prompt | False |

### Utility Tools

The bot includes several utility tools to help you monitor and manage your trading:

```bash
# Check account balances
python tools/balance_checker.py

# Check trading pairs
python tools/pair_checker.py --pair XBTZAR --detailed

# Monitor trade status
python tools/trade_status.py

# Track profit and loss
python tools/profit_tracker.py --days 30 --plot

# View the trading process flow documentation
python tools/view_process_flow.py

# Run continuous trading (with both strategies)
./run_continuous.sh --aggressive-pair XBTMYR

# Run only aggressive trading strategy in live mode
./aggressive.sh --pair XBTMYR

# Run only conservative trading strategy in live mode
./conservative.sh --pair XBTZAR
```

## Features

- **Multiple Trading Strategies**: Support for various technical indicators (SMA, EMA, RSI, MACD)
- **Risk Management**: Stop-loss, take-profit, and position sizing
- **Real-time Data**: WebSocket support for live market data
- **Dry-run Mode**: Test strategies without making actual trades
- **Guaranteed Live Mode**: Special scripts to ensure real trading with aggressive or conservative strategies
- **Performance Tracking**: Monitor and analyze trading performance
- **Extensible Architecture**: Easily add new strategies and features
- **Official SDK**: Uses the Luno SDK for reliable API access
- **Market Structure Analysis**: Advanced market structure analysis for identifying key price levels and patterns
- **Continuous Trading**: Run strategies 24/7 with automatic restart on crashes

## Configuration

Configuration settings are stored in `config/settings.py`. You can override settings through command-line arguments or by editing the settings file.

## API Credentials

To use the bot, you need to set up your Luno API credentials:

1. Create a `.env` file in the project root
2. Add your API credentials in the following format:
   ```
   LUNO_API_KEY=your_api_key_here
   LUNO_API_SECRET=your_api_secret_here
   ```

## Data Storage

- Logs: `data/logs/`
- Order history: `data/orders/`
- Trade history: `data/history/`
- Balance snapshots: `data/snapshots/`
- Reports: `data/reports/`

## Development

### Adding a New Strategy

1. Create a new class in the `traders/` directory that extends `BaseTrader`
2. Implement the `generate_signals()` method with your strategy logic
3. Register your strategy in the configuration

### Adding a New Model

1. Create a new class in the appropriate subdirectory of `models/`
2. Implement the required methods
3. Add your model to the appropriate trader implementation

## Troubleshooting

If you encounter issues with the Luno SDK:

1. Verify your API credentials are correct
2. Check that the required SDK versions are installed:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the test script to diagnose API connection issues:
   ```bash
   python test_luno_sdk.py
   ```
4. For WebSocket issues:
   - Ensure the `luno_streams` package (v0.1.5) is installed correctly
   - Check that the `websockets` package (v11.0.3) is installed
   - Run `./update_deps.sh` to update all dependencies
   - Check your Luno API permissions - WebSocket access might require specific permissions

5. If you see errors related to asyncio:
   ```bash
   pip install asyncio==3.4.3
   ```

## Market Structure Analysis

The bot includes an advanced market structure analysis module that can identify important price patterns and generate trading signals based on them:

### Key Features

- **Swing Point Detection**: Identifies swing highs and swing lows in price data
- **Higher Highs & Higher Lows**: Detects uptrend continuation patterns
- **Lower Highs & Lower Lows**: Detects downtrend continuation patterns
- **Support & Resistance Levels**: Automatically identifies key price levels
- **Breakout Detection**: Identifies when price breaks through key levels
- **Structure-Based Trading Signals**: Generates trading signals with confidence levels

### How to Enable

To enable market structure analysis, use the `--use-market-structure` flag:

```bash
python run_trader.py --pair XBTZAR --use-market-structure
```

### Configuration

The market structure analysis can be configured in the settings file:

```json
"market_structure": {
  "lookback_period": 20,
  "swing_threshold": 0.005,
  "confidence_threshold": 0.7
}
```

- `lookback_period`: Number of candles to analyze for trend identification
- `swing_threshold`: Minimum relative price change to identify swing points
- `confidence_threshold`: Minimum confidence needed to generate trading signals 