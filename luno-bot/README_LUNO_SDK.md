# Luno SDK Integration

This document provides details about the Luno SDK integration in the Luno Trading Bot.

## Overview

The Luno Trading Bot uses the official Luno API SDKs:

- `luno-python` (v0.0.10): For REST API interactions
- `luno_streams` (v0.1.5): For WebSocket real-time data

## API Client Implementation

The `core/api_client.py` file contains two main classes:

1. `LunoAPIClient`: A wrapper around the Luno REST API that provides methods for:
   - Getting market data (tickers, orderbooks, trades, candles)
   - Checking account balances
   - Placing orders
   - Canceling orders
   
2. `LunoWebSocketClient`: A client for the Luno WebSocket API that provides real-time market data.

## WebSocket Implementation Notes

The `luno_streams` package doesn't provide a direct WebSocket client but instead uses an `Updater` class. Our implementation:

1. Creates an `Updater` instance with the pair and API credentials
2. Uses hooks to process different message types:
   - `trades`: Trade updates
   - `asks`: Order book ask updates
   - `bids`: Order book bid updates
3. Connects and runs the updater in an asyncio event loop
4. Handles clean shutdown by stopping the event loop and canceling tasks

The constructor for `Updater` requires these parameters:
```python
Updater(pair_code, api_key, api_secret, hooks=None)
```

To start and stop the WebSocket:
```python
# Connect and run 
await updater.connect()
await updater.run()

# Stop by canceling tasks and closing the event loop
loop.stop()
```

## Response Handling

The Luno SDK returns custom objects for each API call, but our code works with dictionaries. Our implementation:

1. Extracts data from SDK response objects
2. Converts to dictionaries with standardized keys
3. Handles error cases with appropriate logging

## Testing the SDK Integration

We provide two test scripts to verify the SDK integration:

### Basic Test Script

```bash
# Run the SDK test script
python test_luno_sdk.py
```

This tests:
- The REST API client (tickers, ticker, balances)
- The WebSocket client (if chosen)

### Detailed API Test

```bash
# Run the detailed API test
python api_test.py
```

This provides more detailed debugging information:
- Shows raw SDK responses
- Compares direct SDK calls with our wrapper
- Displays response structures

## Troubleshooting

### Common Issues and Solutions

1. **Import Error for LunoStreamClient**

   Error:
   ```
   ImportError: cannot import name 'LunoStreamClient' from 'luno_streams'
   ```

   Solution:
   The `luno_streams` package uses `Updater` instead of `LunoStreamClient`.
   This is fixed in our implementation.

2. **WebSocket method errors**

   Error:
   ```
   'Updater' object has no attribute 'start'
   ```

   Solution:
   The `luno_streams.Updater` class uses `connect()` and `run()` instead of `start()`.
   There is no `disconnect()` method - to stop, you need to cancel tasks and close the event loop.

3. **Response Attribute Errors**

   Error:
   ```
   'dict' object has no attribute 'tickers'
   ```

   Solution:
   The SDK may return dictionaries or custom objects, which our implementation now handles correctly.

4. **WebSocket Connection Issues**

   Solution:
   - Ensure you have correct API credentials
   - Check that your credentials have WebSocket permissions
   - Verify you have the required dependencies installed
   - If you see "Event loop stopped before Future completed" errors, make sure to properly clean up all asyncio tasks

### Fixing Dependencies

Run the update script to install the correct dependencies:

```bash
./update_deps.sh
```

This will install:
- luno-python==0.0.10
- luno_streams==0.1.5
- websockets==11.0.3
- asyncio==3.4.3

## API Credentials

To use the Luno API, you need credentials:

1. Create an API key in your Luno account (Settings > API Keys)
2. Set up a `.env` file with:
   ```
   LUNO_API_KEY=your_api_key_here
   LUNO_API_SECRET=your_api_secret_here
   ```
3. Make sure your API key has the required permissions for:
   - Viewing account balances
   - Trading
   - WebSocket access (if using real-time data)

## SDK Documentation

For more details about the Luno SDK:

- luno-python: [GitHub](https://github.com/luno/luno-python)
- luno_streams: [GitHub](https://github.com/jacoduplessis/luno_streams) 