#!/bin/bash
#
# Continuous Trading Examples
# ===========================
# This script provides examples of different ways to run the continuous trading system.
# Do NOT execute this script directly - it's meant as a reference.
# Copy and modify the examples below to suit your needs.

# Make sure scripts are executable
chmod +x ../run_continuous.sh
chmod +x ../run_continuous_traders.py

# Create necessary directories
mkdir -p ../data/logs
mkdir -p ../data/orders
mkdir -p ../data/history

# =========================================================
# EXAMPLE 1: Basic dry-run with default settings
# This will run both strategies in parallel with dry-run mode
# =========================================================
echo "Example 1: Basic dry-run"
echo "../run_continuous.sh --dry-run"
echo ""

# =========================================================
# EXAMPLE 2: Parallel mode with different trading pairs
# Use different cryptocurrency pairs for each strategy
# =========================================================
echo "Example 2: Different trading pairs"
echo "../run_continuous.sh --aggressive-pair ETHZAR --conservative-pair XBTZAR --dry-run"
echo ""

# =========================================================
# EXAMPLE 3: Alternating mode with custom intervals
# Strategies will run one after another with custom timing
# =========================================================
echo "Example 3: Alternating mode"
echo "../run_continuous.sh --mode alternating --alternating-delay 1800 --aggressive-interval 120 --conservative-interval 600 --dry-run"
echo ""

# =========================================================
# EXAMPLE 4: Custom risk parameters
# Adjust stop-loss and take-profit settings for each strategy
# =========================================================
echo "Example 4: Custom risk parameters"
echo "../run_continuous.sh --aggressive-stop-loss 0.02 --aggressive-take-profit 0.035 --conservative-stop-loss 0.01 --conservative-take-profit 0.018 --dry-run"
echo ""

# =========================================================
# EXAMPLE 5: Live trading example (USE WITH CAUTION!)
# This will execute real trades with real money
# =========================================================
echo "Example 5: LIVE TRADING (not recommended without testing)"
echo "../run_continuous.sh --aggressive-pair ETHZAR --conservative-pair XBTZAR"
echo "⚠️ WARNING: This will execute REAL trades with REAL money!"
echo ""

# =========================================================
# EXAMPLE 6: Running in background with nohup
# Run the bot in the background and log output
# =========================================================
echo "Example 6: Background running with nohup"
echo "nohup ../run_continuous.sh --dry-run > ../data/logs/nohup.log 2>&1 &"
echo "# To check the process: ps aux | grep run_continuous"
echo "# To stop: kill <process_id>"
echo ""

# =========================================================
# EXAMPLE 7: Monitoring logs in real-time
# View the logs while the bot is running
# =========================================================
echo "Example 7: Monitoring logs"
echo "tail -f ../data/logs/aggressive_day_trader.log"
echo "tail -f ../data/logs/conservative_day_trader.log"
echo "tail -f ../data/logs/restart.log"
echo ""

# =========================================================
# EXAMPLE 8: Complete example with all parameters
# =========================================================
echo "Example 8: Complete configuration example"
echo "../run_continuous.sh \\"
echo "  --mode parallel \\"
echo "  --aggressive-pair ETHZAR \\"
echo "  --conservative-pair XBTZAR \\"
echo "  --aggressive-interval 180 \\"
echo "  --conservative-interval 900 \\"
echo "  --aggressive-stop-loss 0.015 \\"
echo "  --aggressive-take-profit 0.025 \\"
echo "  --conservative-stop-loss 0.012 \\"
echo "  --conservative-take-profit 0.020 \\"
echo "  --no-websocket \\"
echo "  --dry-run"
echo ""

echo "For more information, see ../docs/CONTINUOUS_TRADING.md"