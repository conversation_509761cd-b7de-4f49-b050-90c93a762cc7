import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
import matplotlib.pyplot as plt
from dataclasses import dataclass


@dataclass
class MarketStructurePoint:
    """Represents a significant point in market structure analysis."""
    timestamp: pd.Timestamp
    price: float
    type: str  # 'swing_high', 'swing_low', 'support', 'resistance'
    strength: float  # 0.0 to 1.0 indicating the strength of this point


class MarketStructureAnalyzer:
    """Analyzes market structure to identify key levels and patterns."""
    
    def __init__(self, swing_window: int = 5, detection_threshold: float = 0.01):
        """Initialize the market structure analyzer.
        
        Args:
            swing_window: Number of candles to look at for swing high/low detection
            detection_threshold: Minimum relative price change to identify swing points
        """
        self.swing_window = swing_window
        self.detection_threshold = detection_threshold
        self.market_structure_points = []
        
    def identify_swing_points(self, data: pd.DataFrame) -> pd.DataFrame:
        """Identify swing highs and lows in price data.
        
        Args:
            data: DataFrame containing OHLC price data
            
        Returns:
            DataFrame with added swing high/low columns
        """
        df = data.copy()
        
        # Initialize swing columns
        df['swing_high'] = False
        df['swing_low'] = False
        
        # For very small datasets, use a different approach
        if len(df) < 10:
            # Simply mark the highest high and lowest low
            if len(df) >= 3:
                max_idx = df['high'].argmax()
                min_idx = df['low'].argmin()
                
                df.loc[df.index[max_idx], 'swing_high'] = True
                df.loc[df.index[min_idx], 'swing_low'] = True
                
                # Add to market structure points
                self.market_structure_points.append(
                    MarketStructurePoint(
                        timestamp=df.iloc[max_idx]['timestamp'],
                        price=df.iloc[max_idx]['high'],
                        type='swing_high',
                        strength=0.7
                    )
                )
                
                self.market_structure_points.append(
                    MarketStructurePoint(
                        timestamp=df.iloc[min_idx]['timestamp'],
                        price=df.iloc[min_idx]['low'],
                        type='swing_low',
                        strength=0.7
                    )
                )
            return df
        
        # Need at least 2*swing_window+1 candles to identify swing points
        if len(df) < 2 * self.swing_window + 1:
            return df
        
        # Identify swing highs
        for i in range(self.swing_window, len(df) - self.swing_window):
            # Check if this is a local high
            window = df.iloc[i-self.swing_window:i+self.swing_window+1]
            current_high = df.iloc[i]['high']
            
            is_swing_high = True
            for j in range(len(window)):
                if j != self.swing_window and window.iloc[j]['high'] > current_high:
                    is_swing_high = False
                    break
                    
            # Verify significant enough relative to recent price
            if is_swing_high:
                avg_price = window['close'].mean()
                threshold = avg_price * self.detection_threshold
                
                # Check if it's above our threshold for significance
                if (current_high - min(window['low'])) > threshold:
                    df.loc[df.index[i], 'swing_high'] = True
                    # Save this point for later analysis
                    self.market_structure_points.append(
                        MarketStructurePoint(
                            timestamp=df.iloc[i]['timestamp'],
                            price=current_high,
                            type='swing_high',
                            strength=min(1.0, (current_high - min(window['low'])) / (threshold * 5))
                        )
                    )
        
        # Identify swing lows
        for i in range(self.swing_window, len(df) - self.swing_window):
            # Check if this is a local low
            window = df.iloc[i-self.swing_window:i+self.swing_window+1]
            current_low = df.iloc[i]['low']
            
            is_swing_low = True
            for j in range(len(window)):
                if j != self.swing_window and window.iloc[j]['low'] < current_low:
                    is_swing_low = False
                    break
                    
            # Verify significant enough relative to recent price
            if is_swing_low:
                avg_price = window['close'].mean()
                threshold = avg_price * self.detection_threshold
                
                # Check if it's below our threshold for significance
                if (max(window['high']) - current_low) > threshold:
                    df.loc[df.index[i], 'swing_low'] = True
                    # Save this point for later analysis
                    self.market_structure_points.append(
                        MarketStructurePoint(
                            timestamp=df.iloc[i]['timestamp'],
                            price=current_low,
                            type='swing_low',
                            strength=min(1.0, (max(window['high']) - current_low) / (threshold * 5))
                        )
                    )
        
        return df
    
    def detect_support_resistance(self, df: pd.DataFrame, n_levels: int = 3, price_tolerance: float = 0.02) -> List[Dict[str, Union[float, str]]]:
        """Detect support and resistance levels.
        
        Args:
            df: DataFrame with identified swing points
            n_levels: Number of support/resistance levels to identify
            price_tolerance: Percentage tolerance for grouping similar price levels
            
        Returns:
            List of support/resistance levels with their strengths
        """
        # For small datasets, just use min/max points if we don't have swing points
        if 'swing_high' not in df.columns or 'swing_low' not in df.columns or df['swing_high'].sum() + df['swing_low'].sum() < 2:
            # If we have very limited data, use simple price range
            if len(df) >= 3:
                # Create basic levels using price range
                high_price = df['high'].max()
                low_price = df['low'].min()
                mid_price = (high_price + low_price) / 2
                current_price = df['close'].iloc[-1]
                
                levels = []
                # Add mid point
                levels.append({
                    'price': mid_price,
                    'count': 1,
                    'type': 'support' if current_price > mid_price else 'resistance',
                    'strength': 0.5
                })
                
                # Add min/max as support/resistance
                levels.append({
                    'price': high_price,
                    'count': 1,
                    'type': 'resistance',
                    'strength': 0.7
                })
                
                levels.append({
                    'price': low_price,
                    'count': 1,
                    'type': 'support',
                    'strength': 0.7
                })
                
                # Add to market structure points
                timestamp = df['timestamp'].iloc[-1]
                for level in levels:
                    self.market_structure_points.append(
                        MarketStructurePoint(
                            timestamp=timestamp,
                            price=level['price'],
                            type=level['type'],
                            strength=level['strength']
                        )
                    )
                
                return levels
            else:
                return []
        
        # Original implementation for datasets with identified swing points
        # Get swing points
        swing_highs = df[df['swing_high']]['high'].values
        swing_lows = df[df['swing_low']]['low'].values
        
        # Combine all price points for clustering
        all_prices = np.concatenate([swing_highs, swing_lows])
        
        if len(all_prices) < 2:
            return []
            
        # Cluster similar price levels
        levels = []
        for price in all_prices:
            # Check if we have a similar price level already
            found = False
            for level in levels:
                if abs(level['price'] - price) / level['price'] < price_tolerance:
                    # Increase the strength of this level
                    level['count'] += 1
                    # Update price to average
                    level['price'] = (level['price'] * (level['count'] - 1) + price) / level['count']
                    found = True
                    break
                    
            if not found:
                levels.append({
                    'price': price,
                    'count': 1,
                    'type': 'undetermined'  # Will determine later
                })
        
        # Sort by strength (count)
        levels.sort(key=lambda x: x['count'], reverse=True)
        
        # Take top n_levels
        top_levels = levels[:min(n_levels, len(levels))]
        
        # Determine if each is support or resistance based on current price
        current_price = df['close'].iloc[-1]
        
        for level in top_levels:
            if level['price'] < current_price:
                level['type'] = 'support'
            else:
                level['type'] = 'resistance'
                
            # Calculate normalized strength (0-1)
            level['strength'] = min(1.0, level['count'] / (len(df) * 0.1))
            
            # Add this to our market structure points
            timestamp = df['timestamp'].iloc[-1]  # Use latest timestamp
            self.market_structure_points.append(
                MarketStructurePoint(
                    timestamp=timestamp,
                    price=level['price'],
                    type=level['type'],
                    strength=level['strength']
                )
            )
        
        return top_levels
    
    def identify_market_trend(self, df: pd.DataFrame, window: int = 20) -> Tuple[str, float]:
        """Identify the current market trend and its strength.
        
        Args:
            df: DataFrame with price data
            window: Window size for trend calculation
            
        Returns:
            Tuple of (trend, strength) where trend is 'uptrend', 'downtrend', or 'sideways'
        """
        # Adjust window size if we don't have enough data
        if len(df) < window:
            if len(df) < 3:  # Need at least 3 points for minimal trend analysis
                return 'unknown', 0.0
            # Use whatever data we have, with a minimum of 3 points
            window = max(3, len(df))
            
        # Get recent candles for trend analysis
        recent_df = df.tail(window)
        
        # Calculate linear regression on close prices
        x = np.arange(len(recent_df))
        y = recent_df['close'].values
        
        # Simple linear regression
        slope, intercept = np.polyfit(x, y, 1)
        
        # Calculate R-squared to determine trend strength
        p = np.poly1d([slope, intercept])
        y_pred = p(x)
        
        # Mean of the observed data
        y_mean = np.mean(y)
        
        # Total sum of squares
        ss_tot = np.sum((y - y_mean) ** 2)
        
        # Residual sum of squares
        ss_res = np.sum((y - y_pred) ** 2)
        
        # R-squared
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # Determine trend
        trend_strength = min(1.0, abs(slope) * len(y) / y_mean) if y_mean != 0 else 0
        
        # Lower the threshold for smaller datasets
        threshold = 0.005 if len(df) < 10 else 0.01
        
        if slope > 0 and trend_strength > threshold:
            trend = 'uptrend'
        elif slope < 0 and trend_strength > threshold:
            trend = 'downtrend'
        else:
            trend = 'sideways'
            
        return trend, trend_strength
    
    def detect_breakouts(self, df: pd.DataFrame, tolerance: float = 0.03) -> List[Dict]:
        """Detect potential breakouts of support/resistance levels.
        
        Args:
            df: DataFrame with identified market structure
            tolerance: Percentage beyond level to confirm breakout
            
        Returns:
            List of detected breakouts
        """
        if not self.market_structure_points:
            return []
            
        current_price = df['close'].iloc[-1]
        recent_volume = df['volume'].iloc[-5:].mean() if 'volume' in df.columns else 1.0
        avg_volume = df['volume'].mean() if 'volume' in df.columns else 1.0
        
        breakouts = []
        
        # Check each support/resistance level for breakouts
        for point in self.market_structure_points:
            if point.type not in ['support', 'resistance']:
                continue
                
            # Calculate percentage difference from level
            price_diff_pct = (current_price - point.price) / point.price
            
            # Check for breakout conditions
            if (point.type == 'resistance' and price_diff_pct > tolerance) or \
               (point.type == 'support' and price_diff_pct < -tolerance):
                
                # Increased volume confirms breakout
                volume_factor = recent_volume / avg_volume if avg_volume > 0 else 1.0
                
                breakouts.append({
                    'price_level': point.price,
                    'type': f"{point.type}_breakout",
                    'strength': point.strength * volume_factor,
                    'direction': 'up' if price_diff_pct > 0 else 'down'
                })
        
        return breakouts
    
    def identify_price_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Identify common price patterns in the data.
        
        Args:
            df: DataFrame with swing points identified
            
        Returns:
            List of identified patterns
        """
        patterns = []
        
        # Need enough data for pattern detection
        if len(df) < 30 or not self.market_structure_points:
            return patterns
            
        # Get recent swing points
        swing_points = sorted(
            [p for p in self.market_structure_points if p.type in ['swing_high', 'swing_low']],
            key=lambda x: x.timestamp
        )
        
        if len(swing_points) < 5:
            return patterns
            
        # Check for double top pattern
        for i in range(len(swing_points) - 3):
            if swing_points[i].type == 'swing_high' and swing_points[i+2].type == 'swing_high':
                # Check if they're at similar price levels
                if abs(swing_points[i].price - swing_points[i+2].price) / swing_points[i].price < 0.03:
                    # Check if there's a swing low in between
                    if swing_points[i+1].type == 'swing_low':
                        patterns.append({
                            'pattern': 'double_top',
                            'strength': (swing_points[i].strength + swing_points[i+2].strength) / 2,
                            'start_time': swing_points[i].timestamp,
                            'end_time': swing_points[i+2].timestamp,
                            'signal': 'bearish'
                        })
        
        # Check for double bottom pattern
        for i in range(len(swing_points) - 3):
            if swing_points[i].type == 'swing_low' and swing_points[i+2].type == 'swing_low':
                # Check if they're at similar price levels
                if abs(swing_points[i].price - swing_points[i+2].price) / swing_points[i].price < 0.03:
                    # Check if there's a swing high in between
                    if swing_points[i+1].type == 'swing_high':
                        patterns.append({
                            'pattern': 'double_bottom',
                            'strength': (swing_points[i].strength + swing_points[i+2].strength) / 2,
                            'start_time': swing_points[i].timestamp,
                            'end_time': swing_points[i+2].timestamp,
                            'signal': 'bullish'
                        })
        
        # Check for head and shoulders pattern (simplified)
        for i in range(len(swing_points) - 5):
            # Need alternating swing high and low, with a higher middle peak
            if (swing_points[i].type == 'swing_high' and 
                swing_points[i+2].type == 'swing_high' and 
                swing_points[i+4].type == 'swing_high' and
                swing_points[i+1].type == 'swing_low' and
                swing_points[i+3].type == 'swing_low'):
                
                # Middle peak should be higher
                if (swing_points[i+2].price > swing_points[i].price and 
                    swing_points[i+2].price > swing_points[i+4].price):
                    
                    # Shoulders should be at similar levels
                    if abs(swing_points[i].price - swing_points[i+4].price) / swing_points[i].price < 0.05:
                        patterns.append({
                            'pattern': 'head_and_shoulders',
                            'strength': (swing_points[i].strength + swing_points[i+2].strength + swing_points[i+4].strength) / 3,
                            'start_time': swing_points[i].timestamp,
                            'end_time': swing_points[i+4].timestamp,
                            'signal': 'bearish'
                        })
        
        # Check for inverse head and shoulders pattern
        for i in range(len(swing_points) - 5):
            # Need alternating swing low and high, with a lower middle trough
            if (swing_points[i].type == 'swing_low' and 
                swing_points[i+2].type == 'swing_low' and 
                swing_points[i+4].type == 'swing_low' and
                swing_points[i+1].type == 'swing_high' and
                swing_points[i+3].type == 'swing_high'):
                
                # Middle trough should be lower
                if (swing_points[i+2].price < swing_points[i].price and 
                    swing_points[i+2].price < swing_points[i+4].price):
                    
                    # Shoulders should be at similar levels
                    if abs(swing_points[i].price - swing_points[i+4].price) / swing_points[i].price < 0.05:
                        patterns.append({
                            'pattern': 'inverse_head_and_shoulders',
                            'strength': (swing_points[i].strength + swing_points[i+2].strength + swing_points[i+4].strength) / 3,
                            'start_time': swing_points[i].timestamp,
                            'end_time': swing_points[i+4].timestamp,
                            'signal': 'bullish'
                        })
        
        return patterns
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """Perform full market structure analysis.
        
        Args:
            data: DataFrame containing OHLC price data
            
        Returns:
            Dictionary with analysis results
        """
        # Reset points from previous analyses
        self.market_structure_points = []
        
        # Ensure we have the required columns
        required_columns = ['timestamp', 'open', 'high', 'low', 'close']
        for col in required_columns:
            if col not in data.columns:
                raise ValueError(f"Required column '{col}' not found in data")
        
        # For very small datasets, provide minimal analysis
        if len(data) < 5:
            # Very basic analysis for tiny datasets
            current_price = data['close'].iloc[-1]
            first_price = data['close'].iloc[0]
            percent_change = (current_price - first_price) / first_price if first_price != 0 else 0
            
            # Simple trend based on first vs last price
            if percent_change > 0.01:
                trend = 'uptrend'
                trend_strength = min(1.0, abs(percent_change) * 5)  # Scale it up a bit
            elif percent_change < -0.01:
                trend = 'downtrend'
                trend_strength = min(1.0, abs(percent_change) * 5)  # Scale it up a bit
            else:
                trend = 'sideways'
                trend_strength = 0.2
                
            # Basic support and resistance
            high_price = data['high'].max()
            low_price = data['low'].min()
            
            support_resistance = [
                {
                    'price': low_price,
                    'type': 'support',
                    'strength': 0.6,
                    'count': 1
                },
                {
                    'price': high_price,
                    'type': 'resistance',
                    'strength': 0.6,
                    'count': 1
                }
            ]
            
            # Add to market structure points for visualization
            timestamp = data['timestamp'].iloc[-1]
            self.market_structure_points.append(
                MarketStructurePoint(
                    timestamp=timestamp,
                    price=high_price,
                    type='resistance',
                    strength=0.6
                )
            )
            
            self.market_structure_points.append(
                MarketStructurePoint(
                    timestamp=timestamp,
                    price=low_price,
                    type='support',
                    strength=0.6
                )
            )
            
            return {
                'trend': trend,
                'trend_strength': trend_strength,
                'support_resistance_levels': support_resistance,
                'breakouts': [],
                'patterns': [],
                'current_price': current_price,
                'timestamp': data['timestamp'].iloc[-1],
                'market_structure_points': self.market_structure_points
            }
        
        # Normal analysis flow for sufficient data
        # Identify swing points
        df_with_swings = self.identify_swing_points(data)
        
        # Detect support and resistance levels
        support_resistance = self.detect_support_resistance(df_with_swings)
        
        # Identify market trend
        trend, trend_strength = self.identify_market_trend(df_with_swings)
        
        # Detect breakouts
        breakouts = self.detect_breakouts(df_with_swings)
        
        # Identify price patterns
        patterns = self.identify_price_patterns(df_with_swings)
        
        # Compile analysis results
        analysis = {
            'trend': trend,
            'trend_strength': trend_strength,
            'support_resistance_levels': support_resistance,
            'breakouts': breakouts,
            'patterns': patterns,
            'current_price': data['close'].iloc[-1],
            'timestamp': data['timestamp'].iloc[-1],
            'market_structure_points': self.market_structure_points
        }
        
        return analysis
        
    def plot_analysis(self, data: pd.DataFrame, analysis: Dict = None, save_path: Optional[str] = None):
        """Plot the market structure analysis results.
        
        Args:
            data: DataFrame containing OHLC price data
            analysis: Analysis results from analyze(). If None, analysis will be performed.
            save_path: If provided, save the chart to this path
        """
        if analysis is None:
            analysis = self.analyze(data)
            
        # Create figure
        plt.figure(figsize=(14, 10))
        
        # Price chart
        plt.subplot(2, 1, 1)
        
        # For small datasets, plot a more detailed view
        if len(data) < 20:
            # Plot candlesticks as vertical lines from high to low
            for i, row in data.iterrows():
                # Line from low to high
                plt.plot([row['timestamp'], row['timestamp']], [row['low'], row['high']], 
                        color='black', linewidth=1)
                # Markers for open and close
                color = 'green' if row['close'] >= row['open'] else 'red'
                plt.plot(row['timestamp'], row['open'], 'o', color=color, markersize=4)
                plt.plot(row['timestamp'], row['close'], 's', color=color, markersize=4)
            
            # Connect close prices with a line
            plt.plot(data['timestamp'], data['close'], '-', color='blue', alpha=0.6, 
                    linewidth=1, label='Close Price')
            
            plt.title(f'Price Chart ({len(data)} candles available)')
        else:
            # Standard close price plot for larger datasets
            plt.plot(data['timestamp'], data['close'], label='Close Price')
        
        # Plot swing points
        df_with_swings = data.copy()
        if 'swing_high' not in df_with_swings.columns:
            df_with_swings = self.identify_swing_points(data)
            
        swing_highs = df_with_swings[df_with_swings['swing_high']]
        swing_lows = df_with_swings[df_with_swings['swing_low']]
        
        plt.scatter(swing_highs['timestamp'], swing_highs['high'], color='red', marker='^', s=100, label='Swing High')
        plt.scatter(swing_lows['timestamp'], swing_lows['low'], color='green', marker='v', s=100, label='Swing Low')
        
        # Plot support/resistance levels
        for level in analysis['support_resistance_levels']:
            color = 'green' if level['type'] == 'support' else 'red'
            plt.axhline(y=level['price'], color=color, linestyle='--', 
                        alpha=min(1.0, 0.3 + level['strength'] * 0.7),
                        linewidth=1 + level['strength'] * 2,
                        label=f"{level['type'].capitalize()} ({level['price']:.2f})")
        
        # Add trend line
        if analysis['trend'] != 'unknown':
            # Get a simple regression line
            x = np.arange(len(data))
            y = data['close'].values
            slope, intercept = np.polyfit(x, y, 1)
            
            trend_line = intercept + slope * x
            plt.plot(data['timestamp'], trend_line, 'b--', 
                     alpha=min(0.8, 0.3 + analysis['trend_strength'] * 0.5),
                     linewidth=1 + analysis['trend_strength'] * 2,
                     label=f"{analysis['trend'].capitalize()} (strength: {analysis['trend_strength']:.2f})")
        
        # Add breakout markers
        for breakout in analysis['breakouts']:
            marker = '^' if breakout['direction'] == 'up' else 'v'
            color = 'purple' if breakout['direction'] == 'up' else 'orange'
            plt.axhline(y=breakout['price_level'], color=color, linestyle='-.',
                        alpha=min(1.0, 0.4 + breakout['strength'] * 0.6),
                        linewidth=1 + breakout['strength'] * 2,
                        label=f"{breakout['type'].replace('_', ' ').title()} ({breakout['price_level']:.2f})")
        
        # Add patterns
        for pattern in analysis['patterns']:
            start_idx = data[data['timestamp'] == pattern['start_time']].index[0] if pattern['start_time'] in data['timestamp'].values else 0
            end_idx = data[data['timestamp'] == pattern['end_time']].index[0] if pattern['end_time'] in data['timestamp'].values else len(data) - 1
            
            # Mark the pattern area
            plt.axvspan(data['timestamp'].iloc[start_idx], data['timestamp'].iloc[end_idx], 
                        alpha=0.2, color='yellow')
            
            # Add pattern label
            mid_idx = (start_idx + end_idx) // 2
            y_pos = data['high'].iloc[mid_idx] * 1.02  # Slightly above the price
            plt.text(data['timestamp'].iloc[mid_idx], y_pos, 
                     f"{pattern['pattern'].replace('_', ' ').title()}\n({pattern['signal']})",
                     ha='center', fontsize=9, bbox=dict(facecolor='white', alpha=0.7))
        
        # Add current price marker and label
        current_price = data['close'].iloc[-1]
        plt.axhline(y=current_price, color='blue', linestyle='-', alpha=0.5,
                   linewidth=1, label=f"Current Price: {current_price:.2f}")
        
        # Add text summary for small datasets
        if len(data) < 20:
            summary_text = f"Current: {current_price:.2f}\n"
            summary_text += f"Trend: {analysis['trend']} ({analysis['trend_strength']:.2f})\n"
            summary_text += f"Change: {((data['close'].iloc[-1] / data['close'].iloc[0]) - 1) * 100:.2f}%\n"
            
            # Add text box with summary
            plt.text(0.02, 0.05, summary_text, transform=plt.gca().transAxes,
                    fontsize=9, verticalalignment='bottom',
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.title('Market Structure Analysis')
        plt.ylabel('Price')
        plt.grid(True)
        plt.legend(loc='upper left')
        
        # For small datasets, use more of the plot for the price chart and a simplified indicator section
        if len(data) < 10:
            # Simple volume chart instead of RSI for very small datasets
            plt.subplot(2, 1, 2)
            plt.bar(data['timestamp'], data['volume'], color='blue', alpha=0.6, label='Volume')
            plt.title('Volume')
            plt.ylabel('Volume')
            plt.grid(True)
            plt.legend()
        else:
            # RSI subplot as usual
            plt.subplot(2, 1, 2)
            
            # Calculate RSI if not already in data
            if 'rsi_14' not in data.columns:
                delta = data['close'].diff()
                gain = delta.where(delta > 0, 0).rolling(window=min(14, len(data)-1)).mean()
                loss = -delta.where(delta < 0, 0).rolling(window=min(14, len(data)-1)).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = data['rsi_14']
            
            plt.plot(data['timestamp'], rsi, label='RSI(14)')
            plt.axhline(y=70, color='r', linestyle='--', alpha=0.5)
            plt.axhline(y=30, color='g', linestyle='--', alpha=0.5)
            plt.title('Relative Strength Index')
            plt.ylabel('RSI')
            plt.grid(True)
            plt.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            
        plt.close() 