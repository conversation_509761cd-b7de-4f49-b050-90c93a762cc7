import os
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import pickle

class PricePredictor:
    """Base class for price prediction models."""
    
    def __init__(self, model_dir: Optional[str] = None):
        """Initialize the predictor.
        
        Args:
            model_dir: Directory to save/load models from. Defaults to 'models'.
        """
        self.model_dir = Path(model_dir or "models")
        self.model_dir.mkdir(exist_ok=True)
        self.model = None
    
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data before training or prediction.
        
        Args:
            data: DataFrame containing market data
            
        Returns:
            Preprocessed DataFrame
        """
        # Basic preprocessing - override in subclasses for more complex logic
        df = data.copy()
        
        # Drop any NaN values
        df = df.dropna()
        
        # Drop non-numeric columns except timestamp
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        if "timestamp" in df.columns:
            numeric_cols.append("timestamp")
        df = df[numeric_cols]
        
        return df
    
    def prepare_features(
        self, 
        data: pd.DataFrame, 
        target_col: str = "close", 
        window_size: int = 10
    ) -> tuple:
        """Prepare features and targets for ML model.
        
        Args:
            data: Preprocessed DataFrame
            target_col: Column to predict
            window_size: Number of time steps to use as features
            
        Returns:
            Tuple of (X, y) where X is features and y is targets
        """
        df = data.copy()
        
        # Create sequences of window_size time steps
        X = []
        y = []
        
        for i in range(len(df) - window_size):
            X.append(df.iloc[i:i+window_size][target_col].values)
            y.append(df.iloc[i+window_size][target_col])
        
        return np.array(X), np.array(y)
    
    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """Train the model on market data.
        
        This is a placeholder. Subclasses should implement this method.
        
        Args:
            data: DataFrame containing market data
            target_col: Column to predict
        """
        raise NotImplementedError("Subclasses must implement train method")
    
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Make predictions using the trained model.
        
        This is a placeholder. Subclasses should implement this method.
        
        Args:
            data: DataFrame containing market data
            
        Returns:
            Array of predictions
        """
        raise NotImplementedError("Subclasses must implement predict method")
    
    def save_model(self, filename: str) -> str:
        """Save the trained model to disk.
        
        Args:
            filename: Name of the file to save the model to
            
        Returns:
            Path to the saved model
        """
        if self.model is None:
            raise ValueError("No model to save. Train the model first.")
        
        filepath = self.model_dir / filename
        
        with open(filepath, "wb") as f:
            pickle.dump(self.model, f)
        
        return str(filepath)
    
    def load_model(self, filepath: str) -> None:
        """Load a trained model from disk.
        
        Args:
            filepath: Path to the saved model
        """
        with open(filepath, "rb") as f:
            self.model = pickle.load(f)


class SimpleMovingAveragePredictor(PricePredictor):
    """Simple predictor based on moving averages."""
    
    def __init__(self, model_dir: Optional[str] = None, window_size: int = 7):
        """Initialize the Simple Moving Average predictor.
        
        Args:
            model_dir: Directory to save/load models from
            window_size: Window size for the moving average
        """
        super().__init__(model_dir)
        self.window_size = window_size
        # No actual model needed for SMA
        self.model = {"window_size": window_size}
    
    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        """No training needed for SMA, just store the configuration.
        
        Args:
            data: DataFrame containing market data
            target_col: Column to predict
        """
        # Nothing to train, SMA is just a statistical measure
        self.target_col = target_col
    
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """Predict next price using simple moving average.
        
        Args:
            data: DataFrame containing market data
            
        Returns:
            Array with the predicted price
        """
        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")
        
        # Calculate SMA
        sma = data[self.target_col].rolling(window=min(self.window_size, len(data))).mean()
        
        # If we don't have enough data for the full window, adapt
        if np.isnan(sma.iloc[-1]) and len(data) > 0:
            # Use the mean of all available data
            mean_price = data[self.target_col].mean()
            return np.array([mean_price])
        
        # Last SMA value is our prediction for the next time step
        return np.array([sma.iloc[-1]])


class EMAPredictor(PricePredictor):
    """Predictor based on Exponential Moving Average (EMA)."""
    def __init__(self, model_dir: Optional[str] = None, window_size: int = 7):
        super().__init__(model_dir)
        self.window_size = window_size
        self.model = {"window_size": window_size}

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        self.target_col = target_col

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")
        # Calculate EMA
        ema = data[self.target_col].ewm(span=min(self.window_size, len(data)), adjust=False).mean()
        if np.isnan(ema.iloc[-1]) and len(data) > 0:
            mean_price = data[self.target_col].mean()
            return np.array([mean_price])
        return np.array([ema.iloc[-1]])


class RSIPredictor(PricePredictor):
    """Predictor based on Relative Strength Index (RSI)."""
    def __init__(self, model_dir: Optional[str] = None, window_size: int = 14):
        super().__init__(model_dir)
        self.window_size = window_size
        self.model = {"window_size": window_size}

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        self.target_col = target_col

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")
        delta = data[self.target_col].diff()
        gain = delta.where(delta > 0, 0).rolling(window=self.window_size).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=self.window_size).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        last_rsi = rsi.iloc[-1]
        current_price = data[self.target_col].iloc[-1]
        # Simple rule: if RSI < 30, predict up; if RSI > 70, predict down; else no change
        if last_rsi < 30:
            return np.array([current_price * 1.01])  # Predict up
        elif last_rsi > 70:
            return np.array([current_price * 0.99])  # Predict down
        else:
            return np.array([current_price])  # No change


class MACDPredictor(PricePredictor):
    """Predictor based on MACD indicator."""
    def __init__(self, model_dir: Optional[str] = None, fast: int = 12, slow: int = 26, signal: int = 9):
        super().__init__(model_dir)
        self.fast = fast
        self.slow = slow
        self.signal = signal
        self.model = {"fast": fast, "slow": slow, "signal": signal}

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        self.target_col = target_col

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        if self.target_col not in data.columns:
            raise ValueError(f"Target column {self.target_col} not found in data")
        close = data[self.target_col]
        ema_fast = close.ewm(span=self.fast, adjust=False).mean()
        ema_slow = close.ewm(span=self.slow, adjust=False).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=self.signal, adjust=False).mean()
        current_price = close.iloc[-1]
        # Simple rule: if MACD crosses above signal, predict up; if below, predict down
        if macd.iloc[-2] < signal_line.iloc[-2] and macd.iloc[-1] > signal_line.iloc[-1]:
            return np.array([current_price * 1.01])  # Predict up
        elif macd.iloc[-2] > signal_line.iloc[-2] and macd.iloc[-1] < signal_line.iloc[-1]:
            return np.array([current_price * 0.99])  # Predict down
        else:
            return np.array([current_price])  # No change


try:
    from sklearn.linear_model import LinearRegression
except ImportError:
    LinearRegression = None

class LinearRegressionPredictor(PricePredictor):
    """Predictor using linear regression on recent close prices."""
    def __init__(self, model_dir: Optional[str] = None, window_size: int = 10):
        super().__init__(model_dir)
        self.window_size = window_size
        self.model = {"window_size": window_size}
        self.reg = None

    def train(self, data: pd.DataFrame, target_col: str = "close") -> None:
        if LinearRegression is None:
            raise ImportError("scikit-learn is required for LinearRegressionPredictor")
        self.target_col = target_col
        closes = data[self.target_col].tail(self.window_size).values.reshape(-1, 1)
        X = np.arange(len(closes)).reshape(-1, 1)
        self.reg = LinearRegression().fit(X, closes)

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        if LinearRegression is None:
            raise ImportError("scikit-learn is required for LinearRegressionPredictor")
        closes = data[self.target_col].tail(self.window_size).values.reshape(-1, 1)
        X = np.arange(len(closes)).reshape(-1, 1)
        next_X = np.array([[len(closes)]])
        pred = self.reg.predict(next_X)
        return np.array([pred[0, 0]])


# In a real implementation, you would add more sophisticated models:
# - LinearRegressionPredictor
# - RandomForestPredictor
# - LSTMPredictor
# etc. 