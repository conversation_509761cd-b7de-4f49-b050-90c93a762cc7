import os
import hmac
import hashlib
import time
import json
import requests
from typing import Dict, Any, Optional
from dotenv import load_dotenv
import base64
import threading
import websocket

load_dotenv()


class LunoAPIClient:
    """Client for interacting with the Luno API."""

    BASE_URL = "https://api.luno.com/api/1"

    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None):
        """Initialize the Luno API client.

        Args:
            api_key: Luno API key. If not provided, it will be read from LUNO_API_KEY env var.
            api_secret: Luno API secret. If not provided, it will be read from LUNO_API_SECRET env var.
        """
        self.api_key = api_key or os.getenv("LUNO_API_KEY")
        self.api_secret = api_secret or os.getenv("LUNO_API_SECRET")

        if not self.api_key or not self.api_secret:
            raise ValueError(
                "API key and secret must be provided or set as environment variables"
            )

    def _get_auth_header(self) -> Dict[str, str]:
        """Return the authentication header for API requests."""
        if not self.api_key or not self.api_secret:
            # If credentials are not available, don't include auth header
            return {}

        auth_string = f"{self.api_key}:{self.api_secret}"
        encoded = base64.b64encode(auth_string.encode()).decode()
        return {"Authorization": f"Basic {encoded}"}

    def get_tickers(self) -> Dict[str, Any]:
        """Get the ticker data for all markets."""
        response = requests.get(
            f"{self.BASE_URL}/tickers", headers=self._get_auth_header()
        )
        response.raise_for_status()
        return response.json()

    def get_ticker(self, pair: str) -> Dict[str, Any]:
        """Get the ticker data for a specific market.

        Args:
            pair: Trading pair (e.g., 'XBTZAR' for Bitcoin-ZAR)
        """
        response = requests.get(
            f"{self.BASE_URL}/ticker",
            params={"pair": pair},
            headers=self._get_auth_header(),
        )
        response.raise_for_status()
        return response.json()

    def get_orderbook(self, pair: str) -> Dict[str, Any]:
        """Get the order book for a specific market.

        Args:
            pair: Trading pair (e.g., 'XBTZAR' for Bitcoin-ZAR)
        """
        response = requests.get(
            f"{self.BASE_URL}/orderbook_top",
            params={"pair": pair},
            headers=self._get_auth_header(),
        )
        response.raise_for_status()
        return response.json()

    def get_trades(self, pair: str, since: Optional[int] = None) -> Dict[str, Any]:
        """Get recent trades for a market.

        Args:
            pair: Trading pair (e.g., 'XBTZAR' for Bitcoin-ZAR)
            since: Fetch trades executed after this trade ID
        """
        params = {"pair": pair}
        if since is not None:
            params["since"] = since

        response = requests.get(
            f"{self.BASE_URL}/trades", params=params, headers=self._get_auth_header()
        )
        response.raise_for_status()
        return response.json()

    def get_candles(self, pair: str, duration: int = 60) -> Dict[str, Any]:
        """Get candlestick data for a market.

        Args:
            pair: Trading pair (e.g., 'XBTZAR' for Bitcoin-ZAR)
            duration: Candle duration in seconds (default: 60)
        """
        response = requests.get(
            f"{self.BASE_URL}/candles",
            params={"pair": pair, "duration": duration},
            headers=self._get_auth_header(),
        )
        response.raise_for_status()
        return response.json()

    def get_balances(self) -> Dict[str, Any]:
        """Get the account balances."""
        response = requests.get(
            f"{self.BASE_URL}/balance", headers=self._get_auth_header()
        )
        response.raise_for_status()
        return response.json()

    def get_pending_orders(self, pair: Optional[str] = None) -> Dict[str, Any]:
        """Get the list of pending orders.

        Args:
            pair: Trading pair to filter by (optional)
        """
        params = {}
        if pair:
            params["pair"] = pair

        response = requests.get(
            f"{self.BASE_URL}/listorders",
            params=params,
            headers=self._get_auth_header(),
        )
        response.raise_for_status()
        return response.json()

    def place_order(
        self, pair: str, type: str, price: Optional[float] = None, volume: float = 0.0
    ) -> Dict[str, Any]:
        """Place a new order.

        Args:
            pair: Trading pair (e.g., 'XBTZAR')
            type: Order type ('BUY' or 'SELL')
            price: Limit price (can be None for market orders)
            volume: Amount to buy or sell
        """
        data = {"pair": pair, "type": type, "volume": str(volume)}

        if price is not None:
            data["price"] = str(price)

        response = requests.post(
            f"{self.BASE_URL}/postorder", data=data, headers=self._get_auth_header()
        )
        response.raise_for_status()
        return response.json()

    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order.

        Args:
            order_id: ID of the order to cancel
        """
        response = requests.post(
            f"{self.BASE_URL}/stoporder",
            data={"order_id": order_id},
            headers=self._get_auth_header(),
        )
        response.raise_for_status()
        return response.json()


class LunoWebSocketClient:
    """WebSocket client for real-time Luno market data."""

    WS_URL = "wss://ws.luno.com/api/1/stream/{pair}"

    def __init__(self, pair, on_message=None, on_error=None, on_close=None):
        """
        Args:
            pair: Trading pair (e.g., 'XBTZAR')
            on_message: Optional callback for incoming messages
            on_error: Optional callback for errors
            on_close: Optional callback for close events
        """
        self.pair = pair
        self.ws = None
        self.thread = None
        self.running = False
        self.callbacks = []
        if on_message:
            self.callbacks.append(on_message)
        self.on_error = on_error
        self.on_close = on_close

    def add_callback(self, callback):
        self.callbacks.append(callback)

    def _on_message(self, ws, message):
        data = json.loads(message)
        for cb in self.callbacks:
            cb(data)

    def _on_error(self, ws, error):
        if self.on_error:
            self.on_error(error)
        else:
            print(f"WebSocket error: {error}")

    def _on_close(self, ws, close_status_code, close_msg):
        self.running = False
        if self.on_close:
            self.on_close(close_status_code, close_msg)
        else:
            print(f"WebSocket closed: {close_status_code} {close_msg}")

    def _on_open(self, ws):
        print(f"WebSocket connection opened for {self.pair}")

    def run_forever(self, reconnect=True, retry_delay=5):
        """Start the WebSocket client in a background thread."""

        def _run():
            while self.running:
                try:
                    ws_url = self.WS_URL.format(pair=self.pair)
                    self.ws = websocket.WebSocketApp(
                        ws_url,
                        on_message=self._on_message,
                        on_error=self._on_error,
                        on_close=self._on_close,
                        on_open=self._on_open,
                    )
                    self.ws.run_forever()
                except Exception as e:
                    print(f"WebSocket connection error: {e}")
                if not reconnect or not self.running:
                    break
                print(f"Reconnecting in {retry_delay} seconds...")
                time.sleep(retry_delay)

        self.running = True
        self.thread = threading.Thread(target=_run, daemon=True)
        self.thread.start()

    def stop(self):
        """Stop the WebSocket client and close the connection."""
        self.running = False
        if self.ws:
            self.ws.close()
        if self.thread:
            self.thread.join(timeout=2)
