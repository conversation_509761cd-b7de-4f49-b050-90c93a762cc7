# Technical Context

## Technologies Used

### Programming Language
- **Python 3**: Primary development language

### External APIs and Services
- **Luno API**: Cryptocurrency exchange API for accessing market data and executing trades
- **Luno SDK**: Official Python SDK for easier integration with Luno API
- **Luno Streams**: WebSocket implementation for real-time market data

### Key Libraries
- **pandas**: Data analysis and manipulation
- **numpy**: Mathematical operations and array processing
- **matplotlib**: Data visualization
- **asyncio**: Asynchronous I/O, event loop, and concurrency
- **websockets**: WebSocket client and server implementation
- **dotenv**: Environment variable management

## Development Setup
- Configuration files located in `config/` directory
- Settings stored in `settings.json` with defaults in `settings.py`
- Environment variables loaded from `.env` file (API keys)

## Technical Constraints
1. **API Rate Limits**: Luno API has rate limiting that must be respected
2. **Data Availability**: Historical data availability varies by trading pair
3. **Candle Duration Constraints**: Only specific candle durations are supported by the API:
   - Valid durations: 60, 300, 900, 1800, 3600, 86400 seconds (1min, 5min, 15min, 30min, 1hr, 1day)
   - Invalid durations like 90 seconds will cause API errors

## Dependencies
- Luno API credentials are required for full functionality
- Python 3.7+ is required for asyncio features

## Architecture

### Core Components
1. **API Client** (`core/api_client.py`): Handles communication with Luno API
2. **Data Fetcher** (`core/data_fetcher.py`): Retrieves and processes market data
3. **Traders** (`traders/`): Implements different trading strategies
4. **Models** (`models/`): Contains prediction models and technical indicators
5. **Execution** (`execution/`): Manages order execution and risk management

### Data Flow
```
API Client → Data Fetcher → Trader → Models → Execution → API Client
```

### Configuration System
- Layered configuration with defaults, file-based settings, and CLI overrides
- Runtime updates to settings via `update_settings()` function
