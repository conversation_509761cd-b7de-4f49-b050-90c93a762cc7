# System Patterns

## System Architecture

### Modular Design
The Luno Trading Bot follows a modular architecture with clear separation of concerns:

```
┌────────────┐    ┌────────────┐    ┌────────────┐    ┌────────────┐
│  API Layer │───▶│ Data Layer │───▶│ Strategy   │───▶│ Execution  │
│            │◀───│            │◀───│ Layer      │◀───│ Layer      │
└────────────┘    └────────────┘    └────────────┘    └────────────┘
```

1. **API Layer**: Handles all communication with the Luno exchange
2. **Data Layer**: Processes and transforms market data
3. **Strategy Layer**: Implements trading algorithms and decision logic
4. **Execution Layer**: Manages trade execution and risk controls

### Component Relationships

- **Trader Classes**: Central coordinators that use other components
- **Models**: Encapsulated prediction logic that can be swapped out
- **Data Fetcher**: Provides standardized data access across the system
- **Order Manager**: Abstracts order creation and management
- **Risk Manager**: Enforces trading limits and safety controls

## Key Technical Decisions

### Async Processing
- Asynchronous design using `asyncio` for real-time processing
- Event-driven architecture for handling websocket data streams
- Concurrent task management for simultaneous operations

### Configuration Management
- Multi-layered configuration system (defaults → file → environment → command line)
- Runtime configuration updates without restart
- Strategy-specific configuration sections

### Data Handling
- Pandas DataFrames as the primary data structure for time-series data
- Vectorized operations for performance-critical calculations
- Flexible indicator calculations that operate on standard data formats

### Testing and Safety
- Dry-run mode for testing without actual trading
- Explicit error handling and logging at all levels
- Graceful shutdown procedures to prevent dangling orders

## Design Patterns

### Factory Pattern
- Strategy factories that create appropriate predictor instances
- Indicator factories for standardized technical analysis creation

### Strategy Pattern
- Interchangeable trading strategies with common interfaces
- Pluggable prediction models that follow consistent APIs

### Observer Pattern
- WebSocket client uses callbacks to notify components of market updates
- Event-driven updates for real-time data processing

### Repository Pattern
- Data access abstracted behind repository interfaces
- Historical data management with standardized storage and retrieval

## Critical Implementation Paths

### Trade Execution Flow
1. Data fetching and processing
2. Signal generation via strategy/model
3. Risk assessment and position sizing
4. Order creation and submission
5. Order tracking and management
6. Position monitoring and adjustment

### Configuration Loading Sequence
1. Load default settings
2. Apply file-based settings from settings.json
3. Override with environment variables
4. Apply command-line arguments at runtime

### Market Data Processing
1. Fetch raw data from API or websocket
2. Transform into standardized data structures
3. Calculate technical indicators
4. Apply market structure analysis
5. Generate trading signals

### Error Recovery Process
1. Detect error conditions
2. Log detailed error information
3. Attempt recovery when appropriate
4. Safely resume operation or gracefully degrade
