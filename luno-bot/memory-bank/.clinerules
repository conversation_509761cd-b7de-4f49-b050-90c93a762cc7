# Luno Trading Bot - Project Rules and Patterns

## API Constraints

1. **Valid Candle Durations**: The Luno API only accepts specific candle durations:
   - 60 seconds (1 minute)
   - 300 seconds (5 minutes)
   - 900 seconds (15 minutes)
   - 1800 seconds (30 minutes)
   - 3600 seconds (1 hour)
   - 86400 seconds (1 day)
   - Using any other duration will result in an 'ErrInvalidArguments' error

2. **Configuration Validation**: Always validate configuration parameters before using them in API calls, especially:
   - Candle durations
   - Trading pairs (use pair_checker.py to verify)
   - Time-related parameters

3. **Error Handling**: When encountering API errors:
   - Check configuration parameters first
   - Verify API credentials
   - Ensure trading pair exists and is active
   - Test with diagnostic tools (check_pairs.py, test_candles.py)

## Code Organization

1. **Diagnostic Tools**: Place diagnostic scripts in the tools/ directory or the project root
2. **Configuration Flow**: Follow the configuration hierarchy:
   - Default settings in settings.py
   - User settings in settings.json
   - Environment variables
   - Command-line arguments

3. **Error Messages**: Make error messages specific and actionable:
   - Include parameter values that caused errors
   - Suggest possible fixes
   - Reference documentation where applicable

## Testing Practices

1. **API Testing**: Before modifying core functionality:
   - Test with minimal scripts to isolate issues
   - Verify API responses directly
   - Check different parameter combinations

2. **Fallback Mechanisms**: The data_fetcher has fallback mechanisms:
   - Candle data fallback to constructing from trades
   - Trade data fallback to synthetic data generation
   - Always ensure these work with valid inputs

## Market Data Handling

1. **Data Validation**: Always validate:
   - Timeframes are appropriate for the strategy
   - Data completeness before analysis
   - Indicators have sufficient data points

2. **Processing Order**:
   - Fetch raw data first
   - Apply transformations
   - Calculate indicators
   - Generate signals

## Development Workflow

1. **Issue Investigation**:
   - Check logs first
   - Isolate the problem with minimal test cases
   - Verify configuration before blaming code
   - Document findings in memory-bank

2. **Implementation**:
   - Make targeted changes
   - Test changes thoroughly
   - Update documentation
   - Update memory-bank with new learnings
