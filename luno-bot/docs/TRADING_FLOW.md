# Luno Trading Bot - Trading Flow

This document explains the trading flow of the Luno Trading Bot, focusing on how market structure analysis is integrated with technical indicators to generate trading signals.

```
                                        ┌─────────────────────┐
                                        │   Market Data API   │
                                        └─────────────────────┘
                                                  │
                                                  ▼
┌─────────────────┐                     ┌─────────────────────┐
│  Configuration  │───────────────────▶ │    Data Fetcher     │
└─────────────────┘                     └─────────────────────┘
                                                  │
                                                  ▼
                                        ┌─────────────────────┐
                                        │  Indicators (OHLCV, │
                                        │  RSI, MACD, etc.)   │
                                        └─────────────────────┘
                                                  │
                  ┌─────────────────────┬────────┴─────────┬─────────────────────┐
                  │                     │                  │                     │
                  ▼                     ▼                  ▼                     ▼
        ┌───────────────────┐ ┌──────────────────┐ ┌──────────────┐  ┌────────────────────┐
        │   Price Predictors │ │ Market Structure │ │ Risk Manager │  │ Technical Indicators│
        │   (ML Models)      │ │    Analysis      │ │              │  │ (RSI, MACD, etc.)  │
        └───────────────────┘ └──────────────────┘ └──────────────┘  └────────────────────┘
                  │                     │                  │                     │
                  ▼                     ▼                  ▼                     ▼
        ┌───────────────────┐ ┌──────────────────┐ ┌──────────────┐  ┌────────────────────┐
        │ Price Predictions │ │Structure Patterns │ │ Risk Signals │  │Indicator Signals   │
        │                   │ │(HH, HL, S/R, etc.)│ │              │  │                    │
        └───────────────────┘ └──────────────────┘ └──────────────┘  └────────────────────┘
                  │                     │                  │                     │
                  └─────────────────────┬──────────────────┴─────────────────────┘
                                        │
                                        ▼
                              ┌─────────────────────┐
                              │  Signal Generation  │
                              │  & Conflict Resolution  │
                              └─────────────────────┘
                                        │
                                        ▼
                              ┌─────────────────────┐
                              │   Trade Execution   │
                              └─────────────────────┘
                                        │
                                        ▼
                              ┌─────────────────────┐
                              │  Position Tracking  │
                              └─────────────────────┘

```

## Trading Flow Steps

1. **Market Data Collection**
   - Historical candles are fetched from the Luno API
   - Data is processed to calculate technical indicators (RSI, MACD, SMA, etc.)

2. **Parallel Analysis Streams**
   - **Price Prediction**: Machine learning models predict future price movement
   - **Market Structure Analysis**: Identifies key structural patterns:
     - Higher Highs (HH) & Higher Lows (HL) for uptrends
     - Lower Highs (LH) & Lower Lows (LL) for downtrends
     - Support & Resistance levels
     - Breakout detection
   - **Technical Indicators**: Standard indicators generate signals
   - **Risk Management**: Monitors positions for stop loss/take profit

3. **Signal Generation**
   - Each analysis stream generates signals with confidence levels
   - Market structure signals include swing pattern analysis, breakouts, and key level tests
   - Technical indicator signals based on crossovers, divergences, and overbought/oversold conditions
   - Prediction signals based on forecast price movement beyond threshold

4. **Signal Integration & Conflict Resolution**
   - Signals are weighted by confidence level
   - Conflicting signals are resolved using a priority system:
     - High confidence market structure signals often override indicator signals
     - When signals conflict with similar confidence, default to "hold"
   - Final signal includes action, confidence level, and reasoning

5. **Trade Execution**
   - Final signal is translated into a buy/sell/hold action
   - Orders are placed with the Luno API (except in dry-run mode)
   - Trade details are logged

6. **Position Tracking**
   - Current positions are monitored
   - Performance metrics are updated

## Market Structure Analysis Details

The market structure analysis examines price action to identify patterns that traditional indicators might miss:

1. **Swing Point Detection**
   - Identifies local highs and lows that are significant relative to surrounding price action
   - Uses adaptive thresholds based on recent volatility

2. **Pattern Recognition**
   - HH+HL patterns indicate strong uptrends
   - LH+LL patterns indicate strong downtrends
   - HL without LH suggests accumulation (bullish)
   - LH without LL suggests distribution (bearish)

3. **Support/Resistance Identification**
   - Clusters similar price levels where multiple swing points occur
   - Assigns strength based on frequency of tests and volume

4. **Breakout Detection**
   - Identifies when price breaks through key levels
   - Confirms breakouts with volume analysis when available

5. **Signal Generation**
   - Generates high-confidence signals at key structural points
   - Prioritizes clean breakouts and tests of major support/resistance

## Conclusion

The integration of market structure analysis with traditional technical indicators provides a more complete view of market conditions. By identifying key structural patterns and incorporating them into the decision-making process, the Luno Trading Bot can generate more informed trading signals with higher confidence. 