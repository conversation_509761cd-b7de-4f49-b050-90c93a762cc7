# Continuous Trading with <PERSON><PERSON>

This guide explains how to run <PERSON><PERSON> in continuous trading mode, allowing you to run both aggressive and conservative day trading strategies either simultaneously or in an alternating fashion.

## Overview

The continuous trading functionality enables you to:

- Run two different trading strategies (Aggressive Day Trading and Conservative Day Trading)
- Choose between parallel mode (both strategies run simultaneously) or alternating mode
- Configure each strategy independently with different trading pairs and risk parameters
- Keep the bot running continuously with automatic restart on crashes

## Strategies Overview

### Aggressive Day Trading Strategy

The aggressive day trading strategy is designed for more frequent trading with higher risk/reward:

- Uses Fast EMA (5), Medium EMA (13), and SMA (20) indicators
- Lower confidence threshold (50%) to catch more trading opportunities
- Default check interval of 3 minutes
- Default stop-loss of 1.5% and take-profit of 2.5%
- Best suited for volatile market conditions

### Conservative Day Trading Strategy

The conservative day trading strategy is designed for more cautious trading:

- Uses triple EMA system (9, 21, 34) and SMA (50) indicators
- Higher confidence threshold (80%) to reduce false signals
- Default check interval of 15 minutes
- Default stop-loss of 1.2% and take-profit of 2.0%
- Best suited for stable or trending market conditions

## Getting Started

### Running the Scripts

Two scripts are provided for continuous trading:

1. `run_continuous_traders.py` - The main Python script that implements the trading logic
2. `run_continuous.sh` - A shell script that ensures the trading bot keeps running 24/7

#### Basic Usage

To start the continuous trader with default settings in dry-run mode:

```bash
./run_continuous.sh --dry-run
```

This will run both strategies in parallel mode with dry-run enabled (no real trades).

#### Run Modes

There are two run modes available:

**Parallel Mode (Default):**
- Both strategies run simultaneously in separate processes
- Each strategy operates independently

```bash
./run_continuous.sh --mode parallel --dry-run
```

**Alternating Mode:**
- Strategies run one after the other with a configurable delay between them
- Useful when you want to apply different strategies at different times

```bash
./run_continuous.sh --mode alternating --alternating-delay 3600 --dry-run
```

#### Live Trading

To enable live trading (real trades), simply omit the `--dry-run` flag:

```bash
./run_continuous.sh --mode parallel
```

**⚠️ WARNING: This will execute real trades with real money. Make sure your configuration is correct before enabling live trading.**

## Command-Line Options

### General Options

| Option | Description | Default |
|--------|-------------|---------|
| `--mode` | Run mode: `parallel` or `alternating` | `parallel` |
| `--dry-run` | Run in simulation mode (no real trades) | Not enabled |
| `--no-websocket` | Disable WebSocket for real-time data (use polling instead) | WebSocket enabled |

### Strategy-Specific Options

| Option | Description | Default |
|--------|-------------|---------|
| `--aggressive-pair` | Trading pair for aggressive strategy | `XBTZAR` |
| `--conservative-pair` | Trading pair for conservative strategy | `XBTZAR` |
| `--aggressive-interval` | Check interval for aggressive trader (seconds) | 180 |
| `--conservative-interval` | Check interval for conservative trader (seconds) | 900 |
| `--aggressive-stop-loss` | Stop loss for aggressive trader | 0.015 (1.5%) |
| `--aggressive-take-profit` | Take profit for aggressive trader | 0.025 (2.5%) |
| `--conservative-stop-loss` | Stop loss for conservative trader | 0.012 (1.2%) |
| `--conservative-take-profit` | Take profit for conservative trader | 0.020 (2.0%) |
| `--alternating-delay` | Time to run each strategy before switching (seconds) | 3600 (1 hour) |

### Examples

**Running with different trading pairs:**

```bash
./run_continuous.sh --aggressive-pair ETHZAR --conservative-pair XBTZAR --dry-run
```

**Customizing risk parameters:**

```bash
./run_continuous.sh --aggressive-stop-loss 0.02 --aggressive-take-profit 0.03 --conservative-stop-loss 0.01 --conservative-take-profit 0.018 --dry-run
```

**Adjusting check intervals:**

```bash
./run_continuous.sh --aggressive-interval 120 --conservative-interval 600 --dry-run
```

**Running in alternating mode with custom delay:**

```bash
./run_continuous.sh --mode alternating --alternating-delay 1800 --dry-run
```

## Monitoring and Logging

The continuous trading system creates several log files to help you monitor performance:

- `data/logs/aggressive_day_trader.log` - Logs for the aggressive day trading strategy
- `data/logs/conservative_day_trader.log` - Logs for the conservative day trading strategy
- `data/logs/alternating_trader.log` - Logs for the alternating mode (when used)
- `data/logs/restart.log` - Logs of script restarts when using `run_continuous.sh`

You can monitor these logs in real-time using:

```bash
tail -f data/logs/aggressive_day_trader.log
```

## Performance Analysis

To analyze trading performance, use the built-in profit tracker:

```bash
python tools/profit_tracker.py --days 30 --plot
```

This will show performance statistics and generate a visualization of your trading results.

## Troubleshooting

### Bot Crashes Repeatedly

If the bot crashes repeatedly:

1. Check the log files for error messages
2. Ensure your API credentials are correct
3. Verify network connectivity to the Luno exchange
4. Make sure you have sufficient balance for trading

### High Restart Frequency

If you notice frequent restarts in the `restart.log`:

1. Try running with `--no-websocket` to use polling instead of WebSocket
2. Increase the check intervals for both strategies
3. Update your dependencies using `./update_deps.sh`

### No Trading Signals

If you're not seeing any trading signals:

1. The confidence threshold might be too high - try lowering it
2. Market conditions might not be favorable for your strategy
3. Check that your trading pair is active and has sufficient volume

## Best Practices

1. **Always start with dry-run mode** until you're confident in your configuration
2. **Use different trading pairs** for each strategy to avoid conflicts
3. **Monitor logs regularly** to ensure proper operation
4. **Start with conservative risk parameters** and adjust based on performance
5. **Back up your configuration** before making significant changes
6. **Regularly check your account balance** using `python tools/balance_checker.py`
7. **Set up alerts** for critical errors in the logs

## Advanced Configuration

For more advanced configuration options, you can modify the settings in:

- `config/settings.py` - Main configuration file
- `models/strategies/aggressive_day_trading.py` - Aggressive strategy implementation
- `models/strategies/conservative_day_trading.py` - Conservative strategy implementation

## Safety Features

The continuous trading system includes several safety features:

1. **Graceful shutdown** - Properly closes positions when stopped
2. **Signal handling** - Responds to SIGINT and SIGTERM for clean shutdowns
3. **Exponential backoff** - Increases delay between restarts after crashes
4. **Independent logging** - Separate logs for each strategy for easier debugging

## Maintenance

To ensure optimal performance:

1. Regularly update the bot dependencies with `./update_deps.sh`
2. Clean up old log files occasionally to save disk space
3. Monitor system resource usage when running multiple strategies in parallel
4. Periodically review and optimize your strategy parameters based on performance analytics