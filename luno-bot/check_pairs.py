#!/usr/bin/env python3
"""
Simple script to check available trading pairs on Luno.
"""

from core.api_client import LunoAPIClient
import json


def main():
    try:
        # Initialize API client
        api_client = LunoAPIClient()

        # Get tickers
        print("Fetching available pairs from Luno API...")
        tickers_response = api_client.get_tickers()

        print("Raw response:")
        print(json.dumps(tickers_response, indent=2))

        # Check if 'tickers' in response
        if "tickers" in tickers_response:
            print("\nAvailable pairs:")
            for ticker in tickers_response["tickers"]:
                if isinstance(ticker, dict) and "pair" in ticker:
                    print(f"- {ticker['pair']}")
                else:
                    print(f"- {ticker}")
        else:
            print("No 'tickers' found in the response")

    except Exception as e:
        print(f"Error checking pairs: {e}")


if __name__ == "__main__":
    main()
