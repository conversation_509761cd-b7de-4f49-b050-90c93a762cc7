"""
Base trader module defining the core trading functionality.

This module provides the BaseTrader class that serves as the foundation
for all trading strategies.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import pandas as pd
import numpy as np

from core.api_client import LunoAPIClient
from core.data_fetcher import MarketDataFetcher
from models.base.predictor import PricePredictor
from models.market_structure import MarketStructureAnalyzer, MarketStructurePredictor
from config.logging_config import setup_logging
from config.settings import get_settings


class BaseTrader:
    """Base class for all trading strategies."""

    def __init__(
        self,
        api_client: Optional[LunoAPIClient] = None,
        data_fetcher: Optional[MarketDataFetcher] = None,
        config: Optional[Dict[str, Any]] = None,
        pair: str = "XBTZAR",
        logger: Optional[logging.Logger] = None,
    ):
        """Initialize the base trader.

        Args:
            api_client: LunoAPIClient instance. If None, a new one will be created.
            data_fetcher: MarketDataFetcher instance. If None, a new one will be created.
            config: Configuration dictionary.
            pair: Trading pair (e.g., 'XBTZ<PERSON>').
            logger: Logger instance. If None, a new one will be created.
        """
        self.api_client = api_client or LunoAPIClient()
        self.data_fetcher = data_fetcher or MarketDataFetcher(
            api_client=self.api_client
        )
        self.config = config or {}
        self.pair = pair
        self.logger = logger or setup_logging("trader", "data/logs/trader.log")

        # Trading state
        self.current_position = 0.0
        
        # Status display settings
        display_settings = config.get("display", {}) if config else {}
        self.display_enabled = display_settings.get("enabled", True)
        self.display_refresh_interval = display_settings.get("refresh_interval", 5.0)  # seconds
        self.last_display_refresh = None
        
        # Initialize status dictionary
        self.status = {
            "running": False,
            "status": "Initializing",
            "last_price": 0,
            "previous_price": 0,
            "error": None
        }
        
        # Tracking for signals and trades
        self.last_signal = None
        self.trade_history = []
        self.entry_price = 0.0
        self.last_trade_time = None

        # Market data
        self.market_data = None
        self.indicators = None

        # Predictors
        self.predictors = {}

        # Market structure analysis
        self.use_market_structure = self.config.get("trading", {}).get(
            "use_market_structure", True
        )
        self.market_structure_analyzer = MarketStructureAnalyzer(
            lookback_period=self.config.get("market_structure", {}).get(
                "lookback_period", 20
            ),
            detection_threshold=self.config.get("market_structure", {}).get(
                "swing_threshold", 0.005
            ),
        )
        self.market_structure_predictor = MarketStructurePredictor(
            lookback_period=self.config.get("market_structure", {}).get(
                "lookback_period", 20
            ),
            swing_threshold=self.config.get("market_structure", {}).get(
                "swing_threshold", 0.005
            ),
            confidence_threshold=self.config.get("market_structure", {}).get(
                "confidence_threshold", 0.7
            ),
        )

    def initialize(self) -> bool:
        """Initialize the trader.

        Returns:
            True if initialization was successful, False otherwise.
        """
        try:
            self.logger.info(f"Initializing trader for {self.pair}")

            # Get current account balances
            balances = self.api_client.get_balances()
            if "balance" in balances:
                self.logger.info("Current Account Balances:")
                for asset in balances["balance"]:
                    self.logger.info(
                        f"{asset['asset']}: {asset['balance']} (reserved: {asset['reserved']})"
                    )

            # Get current position
            self.update_position()

            # Fetch initial market data
            self.update_market_data()

            return True
        except Exception as e:
            self.logger.error(f"Error initializing trader: {e}")
            return False

    def update_position(self) -> None:
        """Update the current position based on account balances."""
        try:
            # Get base currency from pair (e.g., 'XBT' from 'XBTZAR')
            base_currency = self.pair[:3]

            # Get balances
            balances = self.api_client.get_balances()

            # Find the balance for the base currency
            for asset in balances.get("balance", []):
                if asset["asset"] == base_currency:
                    self.current_position = float(asset["balance"])
                    self.logger.info(
                        f"Current position: {self.current_position} {base_currency}"
                    )
                    break
        except Exception as e:
            self.logger.error(f"Error updating position: {e}")

    def update_market_data(
        self, days_back: int = 7, interval: int = 3600
    ) -> pd.DataFrame:
        """Update market data.

        Args:
            days_back: Number of days of historical data to fetch.
            interval: Candle interval in seconds.

        Returns:
            DataFrame with market data.
        """
        try:
            self.logger.info(f"Fetching market data for {self.pair}...")

            # Get candle data
            self.market_data = self.data_fetcher.get_candle_data(
                self.pair, duration=interval, days_back=days_back
            )

            # Calculate indicators
            self.indicators = self.data_fetcher.calculate_indicators(self.market_data)

            self.logger.info(f"Fetched {len(self.market_data)} candles")

            return self.indicators
        except Exception as e:
            self.logger.error(f"Error updating market data: {e}")
            return pd.DataFrame()

    def add_predictor(self, name: str, predictor: PricePredictor) -> None:
        """Add a price predictor.

        Args:
            name: Name of the predictor.
            predictor: PricePredictor instance.
        """
        self.predictors[name] = predictor

    def get_predictions(self) -> Dict[str, float]:
        """Get predictions from all predictors.

        Returns:
            Dict mapping predictor names to predicted prices.
        """
        predictions = {}

        if self.indicators is None or len(self.indicators) == 0:
            self.logger.warning("No market data available for prediction")
            return predictions

        for name, predictor in self.predictors.items():
            try:
                # Ensure the predictor is trained
                if not hasattr(predictor, "model") or predictor.model is None:
                    predictor.train(self.indicators, target_col="close")

                # Make prediction
                prediction = predictor.predict(self.indicators)[0]
                predictions[name] = prediction

                self.logger.debug(f"Prediction from {name}: {prediction:.2f}")
            except Exception as e:
                self.logger.warning(f"Error getting prediction from {name}: {e}")

        return predictions

    def analyze_market(self) -> Dict[str, Any]:
        """Analyze market conditions.

        Performs technical analysis of market data including indicators and market structure.

        Returns:
            Dict with market analysis results.
        """
        if self.indicators is None or len(self.indicators) == 0:
            return {"status": "no_data"}

        # Get the latest candle
        latest = self.indicators.iloc[-1]

        # Basic analysis
        analysis = {
            "timestamp": latest["timestamp"],
            "price": latest["close"],
            "volume": latest["volume"],
            "trend": "neutral",  # Default
        }

        # Determine trend based on SMAs
        if "SMA_20" in latest and "SMA_50" in latest:
            if latest["SMA_20"] > latest["SMA_50"]:
                analysis["trend"] = "bullish"
            elif latest["SMA_20"] < latest["SMA_50"]:
                analysis["trend"] = "bearish"

        # Add RSI if available
        if "RSI" in latest:
            analysis["rsi"] = latest["RSI"]
            if latest["RSI"] > 70:
                analysis["rsi_signal"] = "overbought"
            elif latest["RSI"] < 30:
                analysis["rsi_signal"] = "oversold"
            else:
                analysis["rsi_signal"] = "neutral"

        # Market structure analysis
        if self.use_market_structure:
            try:
                # Analyze market structure
                structure_analysis = (
                    self.market_structure_analyzer.analyze_market_structure(
                        self.indicators
                    )
                )

                # Add market structure information to analysis
                analysis["market_structure"] = {
                    "trend": structure_analysis["trend"],
                    "structure": structure_analysis["structure"],
                    "higher_highs": structure_analysis["higher_highs"],
                    "higher_lows": structure_analysis["higher_lows"],
                    "lower_highs": structure_analysis["lower_highs"],
                    "lower_lows": structure_analysis["lower_lows"],
                }

                # Add support/resistance levels
                if structure_analysis["support_levels"]:
                    analysis["support_levels"] = [
                        level for level, _ in structure_analysis["support_levels"]
                    ]
                if structure_analysis["resistance_levels"]:
                    analysis["resistance_levels"] = [
                        level for level, _ in structure_analysis["resistance_levels"]
                    ]

                # Key level
                if structure_analysis["key_level"] > 0:
                    analysis["key_level"] = structure_analysis["key_level"]

                # Recent breakout
                if structure_analysis["recent_break"]:
                    analysis["breakout"] = structure_analysis["recent_break"]

                # Log market structure insights
                self.logger.info(
                    f"Market structure: {analysis['market_structure']['structure']} "
                    f"({analysis['market_structure']['trend']})"
                )

                if (
                    analysis["market_structure"]["higher_highs"]
                    and analysis["market_structure"]["higher_lows"]
                ):
                    self.logger.info(
                        "Found higher highs and higher lows - strong uptrend"
                    )
                elif (
                    analysis["market_structure"]["lower_highs"]
                    and analysis["market_structure"]["lower_lows"]
                ):
                    self.logger.info(
                        "Found lower highs and lower lows - strong downtrend"
                    )
                elif analysis["market_structure"]["higher_lows"]:
                    self.logger.info("Found higher lows - potential accumulation")
                elif analysis["market_structure"]["lower_highs"]:
                    self.logger.info("Found lower highs - potential distribution")

                if "breakout" in analysis:
                    self.logger.info(
                        f"Recent breakout: {analysis['breakout']['direction']} through "
                        f"{analysis['breakout']['type']} at {analysis['breakout']['level']:.2f}"
                    )
            except Exception as e:
                self.logger.error(f"Error in market structure analysis: {e}")

        return analysis

    def generate_signals(self) -> Dict[str, Any]:
        """Generate trading signals.

        Combines predictions from technical indicators and market structure
        to generate buy/sell/hold signals.

        Returns:
            Dict with trading signals.
        """
        # Get predictions
        predictions = self.get_predictions()

        # Get market analysis
        analysis = self.analyze_market()

        # If no predictions or analysis, return neutral signal
        if not predictions or analysis.get("status") == "no_data":
            return {"action": "hold", "confidence": 0.0}

        # Get current price
        current_price = analysis.get("price", 0.0)
        if current_price == 0.0:
            return {"action": "hold", "confidence": 0.0}

        # Initialize signals
        indicator_signal = {"action": "hold", "confidence": 0.0, "reason": ""}
        structure_signal = {"action": "hold", "confidence": 0.0, "reason": ""}

        # Generate signal based on technical indicators
        # Calculate average prediction
        avg_prediction = sum(predictions.values()) / len(predictions)
        price_change = (avg_prediction - current_price) / current_price

        # Threshold for generating a signal based on predictions
        threshold = self.config.get("trading", {}).get(
            "threshold", 0.01
        )  # 1% by default

        # Generate signal based on predicted price change
        if price_change > threshold:
            indicator_signal = {
                "action": "buy",
                "confidence": min(abs(price_change) * 10, 1.0),  # Scale confidence
                "reason": f"predicted price increase of {price_change:.2%}",
            }
        elif price_change < -threshold:
            indicator_signal = {
                "action": "sell",
                "confidence": min(abs(price_change) * 10, 1.0),  # Scale confidence
                "reason": f"predicted price decrease of {price_change:.2%}",
            }
        else:
            indicator_signal = {
                "action": "hold",
                "confidence": 0.5,
                "reason": "price expected to remain stable",
            }

        # Add RSI signal if available
        if "rsi_signal" in analysis:
            if analysis["rsi_signal"] == "overbought":
                # Strengthen sell signal or weaken buy signal
                if indicator_signal["action"] == "sell":
                    indicator_signal["confidence"] = min(
                        indicator_signal["confidence"] + 0.2, 1.0
                    )
                    indicator_signal["reason"] += ", RSI overbought"
                elif indicator_signal["action"] == "buy":
                    indicator_signal["confidence"] = max(
                        indicator_signal["confidence"] - 0.3, 0.0
                    )
                    if indicator_signal["confidence"] < 0.5:
                        indicator_signal = {
                            "action": "hold",
                            "confidence": 0.6,
                            "reason": "RSI overbought despite bullish indicators",
                        }
            elif analysis["rsi_signal"] == "oversold":
                # Strengthen buy signal or weaken sell signal
                if indicator_signal["action"] == "buy":
                    indicator_signal["confidence"] = min(
                        indicator_signal["confidence"] + 0.2, 1.0
                    )
                    indicator_signal["reason"] += ", RSI oversold"
                elif indicator_signal["action"] == "sell":
                    indicator_signal["confidence"] = max(
                        indicator_signal["confidence"] - 0.3, 0.0
                    )
                    if indicator_signal["confidence"] < 0.5:
                        indicator_signal = {
                            "action": "hold",
                            "confidence": 0.6,
                            "reason": "RSI oversold despite bearish indicators",
                        }

        # Generate signal based on market structure if enabled
        if self.use_market_structure and "market_structure" in analysis:
            # Use the market structure predictor to generate a signal
            try:
                structure_signal = self.market_structure_predictor.generate_signal(
                    self.indicators
                )
                self.logger.info(
                    f"Market structure signal: {structure_signal['action']} "
                    f"(confidence: {structure_signal['confidence']:.2f}, "
                    f"reason: {structure_signal['reason']})"
                )
            except Exception as e:
                self.logger.error(f"Error generating market structure signal: {e}")

        # Combine signals
        # Default to the higher confidence signal
        if structure_signal["confidence"] > indicator_signal["confidence"]:
            final_signal = structure_signal
            signal_source = "market structure"
        else:
            final_signal = indicator_signal
            signal_source = "indicators"

        # Handle conflicting signals - if high confidence in both but contradictory actions
        if (
            structure_signal["confidence"] > 0.7
            and indicator_signal["confidence"] > 0.7
            and structure_signal["action"] != indicator_signal["action"]
            and structure_signal["action"] != "hold"
            and indicator_signal["action"] != "hold"
        ):

            # When signals conflict with high confidence, default to hold
            final_signal = {
                "action": "hold",
                "confidence": 0.8,
                "reason": f"conflicting signals: {indicator_signal['action']} from indicators vs {structure_signal['action']} from market structure",
            }
            signal_source = "conflict resolution"

        # Add source and timestamp
        final_signal["source"] = signal_source
        final_signal["timestamp"] = datetime.now()

        # Log the final signal
        self.logger.info(
            f"Generated signal: {final_signal['action']} (confidence: {final_signal['confidence']:.2f}, "
            f"source: {final_signal['source']})"
        )

        return final_signal

    def execute_trade(
        self, signal: Dict[str, Any], amount: float = 0.0
    ) -> Dict[str, Any]:
        """Execute a trade based on the signal.

        Args:
            signal: Trading signal.
            amount: Amount to trade. If 0, use the amount from config.

        Returns:
            Dict with trade execution results.
        """
        # Default result is no trade
        result = {
            "success": False,
            "action": "none",
            "timestamp": datetime.now(),
            "reason": "no_action",
        }

        # Check if we should trade
        action = signal.get("action", "hold")
        if action == "hold":
            result["reason"] = "signal_hold"
            return result

        # Determine amount to trade
        if amount <= 0:
            amount = self.config.get("trade_amount", 0.001)

        # Execute the trade
        try:
            if action == "buy":
                # Get current price
                ticker = self.api_client.get_ticker(self.pair)
                price = float(ticker["ask"])

                # Place order
                order = self.api_client.place_order(
                    pair=self.pair, type="BUY", volume=amount
                )

                # Update result
                result["success"] = True
                result["action"] = "buy"
                result["amount"] = amount
                result["price"] = price
                result["order_id"] = order.get("order_id", "unknown")

                # Update position and entry price
                self.current_position += amount
                self.entry_price = price
                self.last_trade_time = datetime.now()

                # Log the trade
                self.trade_history.append(
                    {
                        "timestamp": datetime.now(),
                        "action": "buy",
                        "price": price,
                        "amount": amount,
                        "order_id": order.get("order_id", "unknown"),
                    }
                )

                self.logger.info(f"BUY order placed: {amount} @ {price}")

            elif action == "sell":
                # Don't sell if we don't have a position
                if self.current_position <= 0:
                    result["reason"] = "no_position"
                    return result

                # Limit amount to current position
                amount = min(amount, self.current_position)

                # Get current price
                ticker = self.api_client.get_ticker(self.pair)
                price = float(ticker["bid"])

                # Place order
                order = self.api_client.place_order(
                    pair=self.pair, type="SELL", volume=amount
                )

                # Update result
                result["success"] = True
                result["action"] = "sell"
                result["amount"] = amount
                result["price"] = price
                result["order_id"] = order.get("order_id", "unknown")

                # Update position
                self.current_position -= amount
                self.last_trade_time = datetime.now()

                # Log the trade
                self.trade_history.append(
                    {
                        "timestamp": datetime.now(),
                        "action": "sell",
                        "price": price,
                        "amount": amount,
                        "order_id": order.get("order_id", "unknown"),
                    }
                )

                self.logger.info(f"SELL order placed: {amount} @ {price}")

        except Exception as e:
            self.logger.error(f"Error executing trade: {e}")
            result["reason"] = f"error: {e}"

        return result

    def run_trading_cycle(self) -> Dict[str, Any]:
        """Run a single trading cycle.

        Returns:
            Dict with cycle results.
        """
        # Update market data
        self.update_market_data()

        # Generate signals
        signal = self.generate_signals()

        # Execute trade if signal is strong enough
        confidence_threshold = self.config.get("confidence_threshold", 0.01)

        if signal.get("confidence", 0) >= confidence_threshold:
            # Execute trade
            trade_result = self.execute_trade(signal)
        else:
            trade_result = {
                "success": False,
                "action": "none",
                "reason": "low_confidence",
                "confidence": signal.get("confidence", 0),
                "threshold": confidence_threshold,
            }

        # Return combined results
        return {
            "timestamp": datetime.now(),
            "market_data": {
                "price": (
                    self.market_data.iloc[-1]["close"]
                    if self.market_data is not None and len(self.market_data) > 0
                    else None
                )
            },
            "signal": signal,
            "trade": trade_result,
        }
