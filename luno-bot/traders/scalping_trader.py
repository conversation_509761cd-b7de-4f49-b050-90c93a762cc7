"""
Scalping trader for very short-term trading opportunities.

This module implements a scalping trading strategy focused on making
small profits from short-term price movements, with typically very short
holding periods.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import asyncio

from core.api_client import LunoAPIClient
from core.data_fetcher import MarketDataFetcher
from models.indicators.scalping import ScalpingPredictor
from execution.trade_executor import TradeExecutor
from execution.risk_manager import RiskManager
from execution.order_manager import OrderManager
from .base_trader import BaseTrader


class ScalpingTrader(BaseTrader):
    """Trader implementing a scalping strategy for very short-term trades."""

    def __init__(
        self,
        api_client: Optional[LunoAPIClient] = None,
        data_fetcher: Optional[MarketDataFetcher] = None,
        config: Optional[Dict[str, Any]] = None,
        pair: str = "XBTZAR",
        logger: Optional[logging.Logger] = None,
    ):
        """Initialize the scalping trader.

        Args:
            api_client: LunoAPIClient instance
            data_fetcher: MarketDataFetcher instance
            config: Configuration dictionary
            pair: Trading pair (e.g., 'XBTZAR')
            logger: Logger instance
        """
        super().__init__(
            api_client=api_client,
            data_fetcher=data_fetcher,
            config=config,
            pair=pair,
            logger=logger,
        )

        # Specialized config for scalping
        self.scalping_config = self.config.get("scalping", {})

        # Set more frequent data update interval for scalping (1 minute candles)
        self.candle_interval = self.scalping_config.get(
            "candle_interval", 60
        )  # 60 seconds = 1 minute

        # Set shorter lookback period for scalping (default: 2 hours)
        self.lookback_hours = self.scalping_config.get("lookback_hours", 2)

        # Set tight take profit and stop loss percentages for scalping
        self.take_profit = self.scalping_config.get(
            "take_profit", 0.5
        )  # 0.5% take profit
        self.stop_loss = self.scalping_config.get("stop_loss", 0.3)  # 0.3% stop loss

        # Maximum trade duration for scalping (close positions that exceed this time)
        self.max_trade_duration_minutes = self.scalping_config.get(
            "max_trade_duration_minutes", 30
        )

        # Add the scalping predictor
        self.scalping_predictor = ScalpingPredictor(
            price_deviation_threshold=self.scalping_config.get(
                "price_deviation_threshold", 0.0015
            ),
            volume_spike_threshold=self.scalping_config.get(
                "volume_spike_threshold", 1.5
            ),
            momentum_lookback=self.scalping_config.get("momentum_lookback", 5),
            overbought_threshold=self.scalping_config.get("overbought_threshold", 70.0),
            oversold_threshold=self.scalping_config.get("oversold_threshold", 30.0),
        )

        # Add the scalping predictor to our available predictors
        self.predictors["scalping"] = self.scalping_predictor

        # Create specialized trade executor with tighter risk management
        risk_manager = RiskManager(
            config={
                "max_position_size": self.scalping_config.get(
                    "max_position_size", 0.01
                ),
                "max_risk_per_trade": self.scalping_config.get(
                    "max_risk_per_trade", 1.0
                ),  # 1% max risk per trade
                "max_daily_risk": self.scalping_config.get(
                    "max_daily_risk", 5.0
                ),  # 5% max daily risk
            }
        )

        order_manager = OrderManager(api_client=self.api_client)

        # Create a trade executor with specialized risk settings
        self.trade_executor = TradeExecutor(
            api_client=self.api_client,
            risk_manager=risk_manager,
            order_manager=order_manager,
            config=self.config,
        )

        # Track active scalping trades
        self.active_trades = []
        self.trade_history = []

    def update_market_data(self) -> pd.DataFrame:
        """Update market data, optimized for scalping with shorter timeframes.

        Returns:
            DataFrame with market data.
        """
        try:
            self.logger.info(f"► Fetching short-term market data for {self.pair}...")

            # Convert lookback_hours to days for the API call
            days_back = max(1, self.lookback_hours / 24)

            # Get candle data with shorter interval for scalping
            self.market_data = self.data_fetcher.get_candle_data(
                self.pair, duration=self.candle_interval, days_back=days_back
            )

            # Calculate indicators
            self.indicators = self.data_fetcher.calculate_indicators(self.market_data)

            self.logger.info(f"✓ Fetched {len(self.market_data)} candles")

            return self.indicators
        except Exception as e:
            self.logger.error(f"✗ Error updating market data: {e}")
            return pd.DataFrame()

    def generate_signals(self) -> Dict[str, Any]:
        """Generate trading signals based on the scalping strategy.

        Returns:
            Dictionary with signal information.
        """
        try:
            # Ensure we have market data
            if self.indicators is None or len(self.indicators) < 10:
                self.logger.warning("⚠ Insufficient market data for signal generation")
                return {"action": "hold", "confidence": 0.0}

            # Generate signals using the scalping predictor
            signals = self.scalping_predictor.generate_signals(self.indicators)

            # Add timestamp
            signals["timestamp"] = datetime.now()

            # Log the generated signals
            self.logger.info(
                f"► Scalping signals generated: [{signals['action'].upper()}] "
                f"(confidence: {signals['confidence']:.2f}, "
                f"reason: {signals['reason']})"
            )

            return signals
        except Exception as e:
            self.logger.error(f"✗ Error generating signals: {e}")
            return {"action": "hold", "confidence": 0.0}

    def check_active_trades(self) -> None:
        """Check active trades for take profit, stop loss, or time exit conditions."""
        try:
            if not self.active_trades:
                return

            # Get current price
            ticker = self.api_client.get_ticker(self.pair)
            current_price = float(ticker["last_trade"])

            # Current time
            now = datetime.now()

            for trade in self.active_trades[
                :
            ]:  # Create a copy of the list for safe iteration
                # Skip if no entry price or time
                if "entry_price" not in trade or "entry_time" not in trade:
                    continue

                entry_price = trade["entry_price"]
                entry_time = trade["entry_time"]
                trade_type = trade["action"]  # 'buy' or 'sell'

                # Calculate price change percentage
                if trade_type == "buy":
                    price_change_pct = (current_price - entry_price) / entry_price * 100
                else:  # sell
                    price_change_pct = (entry_price - current_price) / entry_price * 100

                # Calculate trade duration
                trade_duration = now - entry_time
                trade_duration_minutes = trade_duration.total_seconds() / 60

                # Check take profit condition
                if price_change_pct >= self.take_profit:
                    self.logger.info(
                        f"[PROFIT] Take profit reached for {trade_type} trade: {price_change_pct:.2f}% profit"
                    )
                    self._close_trade(
                        trade, "take_profit", current_price, price_change_pct
                    )

                # Check stop loss condition
                elif price_change_pct <= -self.stop_loss:
                    self.logger.info(
                        f"[LOSS] Stop loss triggered for {trade_type} trade: {-price_change_pct:.2f}% loss"
                    )
                    self._close_trade(
                        trade, "stop_loss", current_price, price_change_pct
                    )

                # Check time-based exit condition
                elif trade_duration_minutes >= self.max_trade_duration_minutes:
                    self.logger.info(
                        f"[TRADE] Time-based exit for {trade_type} trade after {trade_duration_minutes:.1f} minutes"
                        f" with {price_change_pct:.2f}% P/L"
                    )
                    self._close_trade(
                        trade, "time_exit", current_price, price_change_pct
                    )

        except Exception as e:
            self.logger.error(f"✗ Error checking active trades: {e}")

    def _close_trade(self, trade, exit_reason, exit_price, pnl_pct):
        """Close an active trade and record the results."""
        try:
            # Determine closing action (opposite of entry)
            action = "sell" if trade["action"] == "buy" else "buy"

            # Close the position
            result = self.trade_executor.execute_trade(
                action=action,
                pair=self.pair,
                amount=trade.get("amount", 0.0),
                price=None,  # Use market price
            )

            if result["success"]:
                # Record trade outcome
                trade_record = {
                    **trade,
                    "exit_price": exit_price,
                    "exit_time": datetime.now(),
                    "exit_reason": exit_reason,
                    "pnl_pct": pnl_pct,
                    "success": True,
                }

                # Add to trade history
                self.trade_history.append(trade_record)

                # Remove from active trades
                self.active_trades.remove(trade)

                self.logger.info(f"✓ Successfully closed trade: {trade_record}")
            else:
                self.logger.error(f"✗ Failed to close trade: {result}")

        except Exception as e:
            self.logger.error(f"✗ Error closing trade: {e}")

    def execute_trade(
        self, signal: Dict[str, Any], amount: float = 0.0
    ) -> Dict[str, Any]:
        """Execute a trade based on the given signal.

        Args:
            signal: Signal dictionary from generate_signals
            amount: Amount to trade (if 0, will use config value)

        Returns:
            Dictionary with trade execution results.
        """
        try:
            # Check if we have a valid signal with sufficient confidence
            if signal["action"] not in ["buy", "sell"] or signal[
                "confidence"
            ] < self.scalping_config.get("min_confidence", 0.5):
                return {"success": False, "error": "insufficient_confidence"}

            # Use default amount if not specified
            if amount <= 0:
                amount = self.scalping_config.get("trade_amount", 0.001)

            # Execute the trade using our specialized executor
            result = self.trade_executor.execute_trade(
                action=signal["action"],
                pair=self.pair,
                amount=amount,
                price=None,  # Use market price for faster execution
            )

            if result["success"]:
                # Record the trade
                trade_record = {
                    **signal,
                    "amount": amount,
                    "entry_price": result["price"],
                    "entry_time": datetime.now(),
                    "order_id": result["order_id"],
                }

                # Add to active trades
                self.active_trades.append(trade_record)

                self.logger.info(
                    f"[{signal['action'].upper()}] Executed trade for {amount} {self.pair} "
                    f"@ {float(result['price']):.2f} (ID: {result['order_id']})"
                )

            return result

        except Exception as e:
            self.logger.error(f"✗ Error executing trade: {e}")
            return {"success": False, "error": str(e)}

    def run_trading_cycle(self) -> Dict[str, Any]:
        """Run a complete trading cycle.

        Returns:
            Dictionary with cycle results.
        """
        try:
            # Update market data (with shorter timeframe for scalping)
            self.update_market_data()

            # Check active trades for take profit or stop loss conditions
            self.check_active_trades()

            # Generate trading signals
            signals = self.generate_signals()

            # Execute trades based on signals
            if signals["action"] in ["buy", "sell"]:
                execution_result = self.execute_trade(signals)
                signals["execution"] = execution_result

            return signals

        except Exception as e:
            self.logger.error(f"✗ Error running trading cycle: {e}")
            return {"error": str(e)}

    async def run_trading_loop(self) -> None:
        """Run the trading loop.

        This asynchronous method runs the trading cycle at the specified interval.
        """
        try:
            # Initialize trader
            if not self.initialize():
                self.logger.error("✗ Failed to initialize scalping trader")
                return

            self.logger.info(f"▶ Starting scalping trading loop for {self.pair}")

            # Keep running while not stopped
            self.running = True

            # Get check interval from config (default 60 seconds for scalping)
            check_interval = self.scalping_config.get("check_interval", 60)

            while self.running:
                try:
                    # Run a trading cycle
                    results = self.run_trading_cycle()

                    # Log action results
                    action = results.get("action", "hold")
                    if action != "hold":
                        self.logger.info(
                            f"► Trading cycle completed with action: [{action.upper()}], "
                            f"confidence: {results.get('confidence', 0.0):.2f}"
                        )

                    # Wait for the next check interval
                    await asyncio.sleep(check_interval)

                except Exception as cycle_error:
                    self.logger.error(f"✗ Error in trading cycle: {cycle_error}")
                    await asyncio.sleep(check_interval)

            self.logger.info("■ Trading loop stopped")

        except Exception as e:
            self.logger.error(f"✗ Error in trading loop: {e}")

    def stop(self) -> None:
        """Stop the trader.

        This method sets the running flag to False to stop the trading loop
        and performs any necessary cleanup.
        """
        try:
            self.logger.info("■ Stopping scalping trader...")
            self.running = False

            # Close any open positions
            self._close_all_positions()

            self.logger.info("✓ Scalping trader stopped")
        except Exception as e:
            self.logger.error(f"✗ Error stopping trader: {e}")

    def _close_all_positions(self) -> None:
        """Close all active positions when the trader is stopped."""
        try:
            if not self.active_trades:
                self.logger.info("► No active trades to close")
                return

            self.logger.info(f"► Closing {len(self.active_trades)} active trades...")

            # Get current price
            try:
                ticker = self.api_client.get_ticker(self.pair)
                current_price = float(ticker["last_trade"])
            except Exception:
                self.logger.warning(
                    "⚠ Could not get current price, using last known price"
                )
                current_price = 0.0  # Will be updated for each trade

            # Close each active trade
            for trade in self.active_trades[:]:  # Create a copy to safely iterate
                if not current_price and "entry_price" in trade:
                    # Use entry price if we couldn't get current price
                    current_price = trade["entry_price"]

                # Calculate profit/loss if possible
                pnl_pct = 0.0
                if "entry_price" in trade and current_price:
                    if trade["action"] == "buy":
                        pnl_pct = (
                            (current_price - trade["entry_price"])
                            / trade["entry_price"]
                            * 100
                        )
                    else:  # sell
                        pnl_pct = (
                            (trade["entry_price"] - current_price)
                            / trade["entry_price"]
                            * 100
                        )

                # Close the trade
                self._close_trade(trade, "trader_stopped", current_price, pnl_pct)

            self.logger.info("All positions closed")
        except Exception as e:
            self.logger.error(f"Error closing positions: {e}")
