"""
Real-time trader implementation.

This module provides a real-time trader that operates continuously,
processing market data and executing trades in real-time.
"""

import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
import signal
import json
from pathlib import Path

from core.api_client import LunoAPIClient, LunoWebSocketClient
from core.data_fetcher import MarketDataFetcher
from models.moving_average import SimpleMovingAveragePredictor, EMAPredictor
from models.indicators import RSIPredictor, MACDPredictor
from config.settings import get_settings
from config.logging_config import setup_logging
from tools.visualization.status_display import refresh_status_display

from .base_trader import BaseTrader


class RealTimeTrader(BaseTrader):
    """Real-time trader implementation for continuous trading."""

    def __init__(
        self,
        api_client: Optional[LunoAPIClient] = None,
        data_fetcher: Optional[MarketDataFetcher] = None,
        config: Optional[Dict[str, Any]] = None,
        pair: Optional[str] = None,
        logger=None,
    ):
        """Initialize the real-time trader.

        Args:
            api_client: LunoAPIClient instance
            data_fetcher: MarketDataFetcher instance
            config: Configuration dictionary
            pair: Trading pair (e.g., 'XBTZAR')
            logger: Logger instance
        """
        # Load settings if no config provided
        if config is None:
            config = get_settings()

        # Get pair from config if not provided
        if pair is None:
            pair = config.get("trading", {}).get("pair", "XBTZAR")

        # Initialize base trader
        super().__init__(
            api_client=api_client,
            data_fetcher=data_fetcher,
            config=config,
            pair=pair,
            logger=logger
            or setup_logging("realtime_trader", "data/logs/realtime_trader.log"),
        )

        # Real-time specific attributes
        self.running = False
        self.ws_client = None
        self.last_check_time = None
        self.check_interval = config.get("trading", {}).get(
            "check_interval", 300
        )  # seconds
        self.min_trade_interval = config.get("trading", {}).get(
            "min_trade_interval", 30
        )  # seconds
        self.stop_loss = config.get("trading", {}).get("stop_loss", 0.02)  # 2%
        self.take_profit = config.get("trading", {}).get("take_profit", 0.03)  # 3%
        self.dry_run = config.get("trading", {}).get(
            "dry_run", True
        )  # Default to dry run for safety

        # Status tracking
        self.status = {
            "running": False,
            "last_update": None,
            "last_price": None,
            "last_signal": None,
            "last_trade": None,
            "error": None,
        }

        # Setup predictors based on config
        self._setup_predictors()

    def _setup_predictors(self):
        """Set up prediction models based on configuration."""
        default_predictor = self.config.get("trading", {}).get(
            "default_predictor", "ensemble"
        )

        # Clear existing predictors
        self.predictors = {}

        if default_predictor == "ensemble" or default_predictor == "all":
            # Add all available predictors
            self.add_predictor("sma", SimpleMovingAveragePredictor())
            self.add_predictor("ema", EMAPredictor())
            self.add_predictor("rsi", RSIPredictor())
            self.add_predictor("macd", MACDPredictor())
        else:
            # Add only the specified predictor
            if default_predictor == "sma":
                self.add_predictor("sma", SimpleMovingAveragePredictor())
            elif default_predictor == "ema":
                self.add_predictor("ema", EMAPredictor())
            elif default_predictor == "rsi":
                self.add_predictor("rsi", RSIPredictor())
            elif default_predictor == "macd":
                self.add_predictor("macd", MACDPredictor())

        self.logger.info(f"Set up predictors: {', '.join(self.predictors.keys())}")

    def _on_websocket_message(self, data: Dict[str, Any]):
        """Handle WebSocket message.

        Args:
            data: Message data from WebSocket
        """
        try:
            # Log the received data with better formatting
            self.logger.debug(
                f"WebSocket message received: {json.dumps(data)[:100]}..."
            )

            # Update our last price if a trade was received
            if "trade_updates" in data and data["trade_updates"]:
                trade = data["trade_updates"][0]
                if "price" in trade:
                    price = float(trade["price"])
                    self.status["previous_price"] = self.status.get("last_price", 0)
                    self.status["last_price"] = price
                    self.logger.debug(f"Updated last price to {price:.2f} from WebSocket")
                    
                    # Update display if enabled
                    if self.display_enabled and self.last_display_refresh is not None:
                        current_time = datetime.now()
                        if (current_time - self.last_display_refresh).total_seconds() >= self.display_refresh_interval:
                            refresh_status_display(self)
                            self.last_display_refresh = current_time

            # Also check for significant price movements
            if (
                self.last_check_time
                and (datetime.now() - self.last_check_time).total_seconds()
                > self.min_trade_interval
            ):
                # Only check for stop loss/take profit if we have a position
                if (
                    self.current_position > 0
                    and self.entry_price > 0
                    and self.status["last_price"]
                ):
                    current_price = self.status["last_price"]
                    price_change = (current_price - self.entry_price) / self.entry_price

                    # Check for stop loss
                    if price_change <= -self.stop_loss:
                        self.logger.info(f"[LOSS] Stop loss triggered: {price_change:.2%}")
                        if not self.dry_run:
                            signal = {
                                "action": "sell",
                                "confidence": 1.0,
                                "reason": "stop_loss",
                            }
                            self.execute_trade(signal, amount=self.current_position)
                        else:
                            self.logger.info(
                                f"[DRY RUN] Would trigger stop loss at {current_price:.2f}"
                            )

                    # Check for take profit
                    elif price_change >= self.take_profit:
                        self.logger.info(f"[PROFIT] Take profit triggered: {price_change:.2%}")
                        if not self.dry_run:
                            signal = {
                                "action": "sell",
                                "confidence": 1.0,
                                "reason": "take_profit",
                            }
                            self.execute_trade(signal, amount=self.current_position)
                        else:
                            self.logger.info(
                                f"[DRY RUN] Would take profit at {current_price:.2f}"
                            )

                    self.last_check_time = datetime.now()

        except Exception as e:
            self.logger.error(f"Error processing WebSocket message: {e}")
            self.status["error"] = str(e)

    def _setup_websocket(self):
        """Set up WebSocket connection for real-time data."""
        try:
            # Only set up WebSocket if it's enabled in config
            if self.config.get("api", {}).get("use_websocket", True):
                self.logger.info(f"Setting up WebSocket connection for {self.pair}...")
                self.ws_client = LunoWebSocketClient(self.pair)
                self.ws_client.add_callback(self._on_websocket_message)
                self.ws_client.start()  # Use start instead of run_forever
                self.logger.info("WebSocket connection started")
            else:
                self.logger.info("WebSocket disabled in configuration")
        except Exception as e:
            self.logger.error(f"Error setting up WebSocket: {e}")
            self.status["error"] = str(e)

    def start(self):
        """Start the real-time trader."""
        if self.running:
            self.logger.warning("Trader is already running")
            return

        self.logger.info(f"▶ Starting real-time trader for {self.pair}")
        if self.dry_run:
            self.logger.info(f"[DRY RUN] Mode enabled - no actual trades will be executed")
        else:
            self.logger.info(f"[LIVE MODE] Real trades will be executed")
            
        # Update status
        self.status["status"] = "Running"
        self.status["last_price"] = 0
        self.status["previous_price"] = 0
        
        # Initial status display
        if self.display_enabled:
            refresh_status_display(self)
            self.last_display_refresh = datetime.now()

        # Initialize
        if not self.initialize():
            self.logger.error("Failed to initialize trader")
            return

        # Set up WebSocket connection
        self._setup_websocket()

        # Mark as running
        self.running = True
        self.status["running"] = True
        self.last_check_time = datetime.now()

        # Log trading parameters
        self.logger.info(f"► Check interval: {self.check_interval} seconds")
        self.logger.info(f"► Min trade interval: {self.min_trade_interval} seconds")
        self.logger.info(f"► Stop loss: {self.stop_loss:.2%}")
        self.logger.info(f"► Take profit: {self.take_profit:.2%}")

    def stop(self):
        """Stop the real-time trader."""
        self.logger.info("■ Stopping real-time trader")
        self.running = False
        self.status["running"] = False
        self.status["status"] = "Stopped"

        # Close WebSocket if active
        if self.ws_client:
            self.ws_client.stop()
            self.ws_client = None

        # Update display one last time
        if self.display_enabled:
            refresh_status_display(self)

        # Save trade history
        self._save_trade_history()

    def _save_trade_history(self):
        """Save trade history to file."""
        try:
            if self.trade_history:
                # Create directory if it doesn't exist
                history_dir = Path("data/history")
                history_dir.mkdir(exist_ok=True, parents=True)

                # Format trades for saving
                formatted_trades = []
                for trade in self.trade_history:
                    # Convert datetime to string
                    if isinstance(trade.get("timestamp"), datetime):
                        trade = (
                            trade.copy()
                        )  # Make a copy to avoid modifying the original
                        trade["timestamp"] = trade["timestamp"].isoformat()
                    formatted_trades.append(trade)

                # Create filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{self.pair}_trades_{timestamp}.json"
                filepath = history_dir / filename

                # Save to JSON
                with open(filepath, "w") as f:
                    json.dump(formatted_trades, f, indent=2)

                self.logger.info(f"✓ Saved trade history to {filepath}")
        except Exception as e:
            self.logger.error(f"Error saving trade history: {e}")

    def execute_trade(
        self, signal: Dict[str, Any], amount: float = 0.0
    ) -> Dict[str, Any]:
        """Execute a trade based on the signal, with dry run support.

        Args:
            signal: Trading signal.
            amount: Amount to trade. If 0, use the amount from config.

        Returns:
            Dict with trade execution results.
        """
        # If in dry run mode, simulate the trade instead of executing it
        if self.dry_run:
            self.logger.info(
                f"[DRY RUN] Would execute [{signal.get('action', 'unknown').upper()}] with amount {amount or self.config.get('trading', {}).get('trade_amount', 0.001)}"
            )

            # Create a simulated result
            result = {
                "success": True,
                "action": signal.get("action", "unknown"),
                "timestamp": datetime.now(),
                "simulated": True,
                "dry_run": True,
            }

            # Add more details based on action
            if signal.get("action") == "buy":
                # Get current price for simulation
                try:
                    ticker = self.api_client.get_ticker(self.pair)
                    price = float(ticker["ask"])

                    # Calculate amount
                    trade_amount = amount or self.config.get("trading", {}).get(
                        "trade_amount", 0.001
                    )

                    # Update result
                    result["amount"] = trade_amount
                    result["price"] = price
                    result["cost"] = price * trade_amount

                    # Simulate position update
                    self.current_position += trade_amount
                    self.entry_price = price
                    self.last_trade_time = datetime.now()

                    # Log the trade
                    self.trade_history.append(
                        {
                            "timestamp": datetime.now(),
                            "action": "buy",
                            "price": price,
                            "amount": trade_amount,
                            "simulated": True,
                        }
                    )

                    self.logger.info(
                        f"[DRY RUN] Simulated [BUY]: {trade_amount} @ {price:.2f}"
                    )
                except Exception as e:
                    self.logger.error(f"Error in dry run buy simulation: {e}")
                    result["success"] = False
                    result["error"] = str(e)

            elif signal.get("action") == "sell":
                # Get current price for simulation
                try:
                    ticker = self.api_client.get_ticker(self.pair)
                    price = float(ticker["bid"])

                    # Calculate amount
                    trade_amount = min(
                        amount
                        or self.config.get("trading", {}).get("trade_amount", 0.001),
                        self.current_position,
                    )

                    # Update result
                    result["amount"] = trade_amount
                    result["price"] = price
                    result["proceeds"] = price * trade_amount

                    # Simulate position update
                    self.current_position -= trade_amount
                    self.last_trade_time = datetime.now()

                    # Log the trade
                    self.trade_history.append(
                        {
                            "timestamp": datetime.now(),
                            "action": "sell",
                            "price": price,
                            "amount": trade_amount,
                            "simulated": True,
                        }
                    )

                    self.logger.info(
                        f"[DRY RUN] Simulated [SELL]: {trade_amount} @ {price:.2f}"
                    )
                except Exception as e:
                    self.logger.error(f"Error in dry run sell simulation: {e}")
                    result["success"] = False
                    result["error"] = str(e)

            # Update status
            self.status["last_trade"] = result

            return result
        else:
            # Execute real trade using base class implementation
            result = super().execute_trade(signal, amount)
            self.status["last_trade"] = result
            return result

    async def run_trading_loop(self):
        """Run the trading loop until stopped."""
        self.start()

        try:
            while self.running:
                try:
                    # Check if it's time for a trading cycle
                    current_time = datetime.now()
                    if (
                        self.last_check_time is None
                        or (current_time - self.last_check_time).total_seconds()
                        >= self.check_interval
                    ):

                        self.logger.info("■ Running trading cycle...")
                        cycle_result = self.run_trading_cycle()
                        self.last_check_time = current_time

                        # Update status
                        self.status["last_update"] = current_time
                        self.status["last_signal"] = cycle_result.get("signal")

                        if "market_data" in cycle_result and cycle_result[
                            "market_data"
                        ].get("price"):
                            self.status["last_price"] = cycle_result["market_data"][
                                "price"
                            ]

                        # Log the result
                        action = cycle_result['signal'].get('action', 'none')
                        trade_action = cycle_result['trade'].get('action', 'none')
                        self.logger.info(
                            f"► Cycle result: signal=[{action.upper() if action else 'NONE'}], trade=[{trade_action.upper() if trade_action else 'NONE'}]"
                        )
                
                        # Update display after cycle
                        if self.display_enabled:
                            refresh_status_display(self)
                            self.last_display_refresh = datetime.now()

                    # Sleep for a bit to avoid using too much CPU
                    # Shorter sleep time and possibly refresh display
                    await asyncio.sleep(0.1)
            
                    # Update display periodically
                    if self.display_enabled and self.last_display_refresh is not None:
                        current_time = datetime.now()
                        if (current_time - self.last_display_refresh).total_seconds() >= self.display_refresh_interval:
                            refresh_status_display(self)
                            self.last_display_refresh = current_time

                except Exception as e:
                    self.logger.error(f"Error in trading loop: {e}")
                    self.status["error"] = str(e)
                    await asyncio.sleep(10)  # Sleep longer after an error
        finally:
            self.stop()

    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the trader.

        Returns:
            Dict with status information
        """
        # Update status with latest information
        self.status.update(
            {
                "running": self.running,
                "pair": self.pair,
                "position": self.current_position,
                "entry_price": self.entry_price,
                "dry_run": self.dry_run,
                "predictors": list(self.predictors.keys()),
                "trade_count": len(self.trade_history),
            }
        )

        return self.status
