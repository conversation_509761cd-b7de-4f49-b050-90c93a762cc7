"""
Aggressive Day Trader implementation using optimized EMA and SMA settings.

This module provides a real-time trader that implements the aggressive day trading
strategy with Fast EMA (5), Medium EMA (13), and SMA (20) parameters.
"""

import time
from datetime import datetime
from typing import Dict, Any, Optional

from models.strategies.aggressive_day_trading import AggressiveDayTradingStrategy
from .realtime_trader import RealTimeTrader
from core.api_client import LunoAPIClient
from core.data_fetcher import MarketDataFetcher


class AggressiveDayTrader(RealTimeTrader):
    """Aggressive Day Trader implementing optimized EMA and SMA parameters for day trading."""

    def __init__(
        self,
        api_client: Optional[LunoAPIClient] = None,
        data_fetcher: Optional[MarketDataFetcher] = None,
        config: Optional[Dict[str, Any]] = None,
        pair: Optional[str] = None,
        logger=None,
    ):
        """Initialize the aggressive day trader.

        Args:
            api_client: LunoAPIClient instance
            data_fetcher: MarketDataFetcher instance
            config: Configuration dictionary
            pair: Trading pair (e.g., 'XBTZAR')
            logger: Logger instance
        """
        # Initialize base real-time trader
        super().__init__(
            api_client=api_client,
            data_fetcher=data_fetcher,
            config=config,
            pair=pair,
            logger=logger,
        )

        # Override with aggressive day trading specific settings
        self.logger.info(
            "Initializing Aggressive Day Trader with optimized EMA/SMA parameters"
        )

        # Get risk parameters from config or use defaults
        trading_config = config.get("trading", {}) if config else {}
        stop_loss_pct = trading_config.get("stop_loss", 0.015)  # Default 1.5%
        take_profit_pct = trading_config.get("take_profit", 0.025)  # Default 2.5%

        # Create our aggressive day trading strategy
        self.strategy = AggressiveDayTradingStrategy(
            stop_loss_pct=stop_loss_pct, take_profit_pct=take_profit_pct
        )

        # Set shorter check interval for more frequent trading opportunities
        self.check_interval = trading_config.get(
            "check_interval", 180
        )  # 3 minutes default

        # Lower the confidence threshold to catch more signals
        self.confidence_threshold = trading_config.get(
            "confidence_threshold", 0.5
        )  # 50% confidence

        self.logger.info(f"► Stop loss: {stop_loss_pct:.2%}")
        self.logger.info(f"► Take profit: {take_profit_pct:.2%}")
        self.logger.info(f"► Check interval: {self.check_interval} seconds")
        self.logger.info(f"► Confidence threshold: {self.confidence_threshold:.1%}")

    def run_trading_cycle(self) -> Dict[str, Any]:
        """Run a single trading cycle using the aggressive day trading strategy.

        Returns:
            Dict with cycle results.
        """
        # Update market data
        self.update_market_data()

        if self.market_data is None or len(self.market_data) < 20:
            self.logger.warning("Insufficient market data for analysis")
            return {
                "timestamp": datetime.now(),
                "signal": {
                    "action": "hold",
                    "confidence": 0,
                    "reason": "insufficient data",
                },
                "trade": {
                    "success": False,
                    "action": "none",
                    "reason": "insufficient data",
                },
            }

        # Use our aggressive day trading strategy to analyze the market
        analysis_result = self.strategy.analyze(self.market_data)

        # Convert the strategy output to a signal format compatible with the trader
        signal = {
            "action": analysis_result["signal"].lower(),
            "confidence": analysis_result["confidence"],
            "reason": analysis_result["reason"],
            "source": "aggressive_day_strategy",
            "timestamp": datetime.now(),
        }

        # Log analysis details
        self.logger.info(
            f"Fast EMA (5): {analysis_result['indicators']['fast_ema']:.2f}"
        )
        self.logger.info(
            f"Medium EMA (13): {analysis_result['indicators']['medium_ema']:.2f}"
        )
        self.logger.info(f"SMA (20): {analysis_result['indicators']['sma']:.2f}")
        self.logger.info(f"Current Price: {analysis_result['indicators']['price']:.2f}")
        self.logger.info(
            f"Signal: {signal['action'].upper()} (confidence: {signal['confidence']:.2f})"
        )

        # Execute trade if signal confidence is above threshold
        if signal["confidence"] >= self.confidence_threshold and signal["action"] in [
            "buy",
            "sell",
        ]:
            # Apply risk parameters from strategy
            if signal["action"] == "buy":
                self.stop_loss = analysis_result["risk_params"]["stop_loss"]
                self.take_profit = analysis_result["risk_params"]["take_profit"]

            # Execute trade
            trade_result = self.execute_trade(signal)
        else:
            trade_result = {
                "success": False,
                "action": "none",
                "reason": (
                    f"low_confidence ({signal['confidence']:.2f} < {self.confidence_threshold})"
                    if signal["confidence"] < self.confidence_threshold
                    else "hold_signal"
                ),
            }

        # Return combined results
        return {
            "timestamp": datetime.now(),
            "market_data": {
                "price": (
                    self.market_data.iloc[-1]["close"]
                    if self.market_data is not None and len(self.market_data) > 0
                    else None
                )
            },
            "signal": signal,
            "trade": trade_result,
            "indicators": analysis_result["indicators"],
            "risk_params": analysis_result["risk_params"],
        }
