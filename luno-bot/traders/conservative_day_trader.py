"""Conservative Day Trader implementation optimized for XBTMYR."""

from datetime import datetime
from typing import Dict, Any, Optional

from models.strategies.conservative_day_trading import ConservativeDayTradingStrategy
from .realtime_trader import RealTimeTrader
from core.api_client import LunoAPIClient
from core.data_fetcher import MarketDataFetcher


class ConservativeDayTrader(RealTimeTrader):
    """Conservative Day Trader implementing triple EMA and SMA for XBTMYR."""

    def __init__(
        self,
        api_client: Optional[LunoAPIClient] = None,
        data_fetcher: Optional[MarketDataFetcher] = None,
        config: Optional[Dict[str, Any]] = None,
        pair: Optional[str] = None,
        logger=None,
    ):
        """Initialize the conservative day trader.

        Args:
            api_client: LunoAPIClient instance
            data_fetcher: MarketDataFetcher instance
            config: Configuration dictionary
            pair: Trading pair (e.g., 'XBTMYR')
            logger: Logger instance
        """
        # Initialize base real-time trader
        super().__init__(
            api_client=api_client,
            data_fetcher=data_fetcher,
            config=config,
            pair=pair,
            logger=logger,
        )

        # Override with conservative day trading specific settings
        self.logger.info(
            "Initializing Conservative Day Trader with triple EMA and SMA parameters"
        )

        # Get risk parameters from config or use defaults
        trading_config = config.get("trading", {}) if config else {}
        stop_loss_pct = trading_config.get("stop_loss", 0.012)  # Default 1.2%
        take_profit_pct = trading_config.get("take_profit", 0.020)  # Default 2.0%

        # Create our conservative day trading strategy
        self.strategy = ConservativeDayTradingStrategy(
            stop_loss_pct=stop_loss_pct, take_profit_pct=take_profit_pct
        )

        # Set longer check interval for more deliberate trading opportunities
        self.check_interval = trading_config.get(
            "check_interval", 900
        )  # 15 minutes default

        # Higher confidence threshold for conservative signals
        self.confidence_threshold = trading_config.get(
            "confidence_threshold", 0.8
        )  # 80% confidence

        self.logger.info(f"► Stop loss: {stop_loss_pct:.2%}")
        self.logger.info(f"► Take profit: {take_profit_pct:.2%}")
        self.logger.info(f"► Check interval: {self.check_interval} seconds")
        self.logger.info(f"► Confidence threshold: {self.confidence_threshold:.1%}")

    def run_trading_cycle(self) -> Dict[str, Any]:
        """Run a single trading cycle using the conservative day trading strategy.

        Returns:
            Dict with cycle results.
        """
        # Update market data
        self.update_market_data()

        # Log market data details for debugging
        if self.market_data is not None:
            self.logger.info(f"Market data shape: {self.market_data.shape}")
            if len(self.market_data) > 0:
                latest = self.market_data.iloc[-1]
                self.logger.info(f"Latest close price: {latest.get('close', 'N/A')}")
                self.logger.info(f"Latest timestamp: {latest.get('timestamp', 'N/A')}")
            else:
                self.logger.warning("Market data is empty (zero rows)")
        else:
            self.logger.warning("Market data is None")

        if self.market_data is None or len(self.market_data) < 50:
            self.logger.warning("Insufficient market data for analysis")
            return {
                "timestamp": datetime.now(),
                "signal": {
                    "action": "hold",
                    "confidence": 0,
                    "reason": "insufficient data",
                },
                "trade": {
                    "success": False,
                    "action": "none",
                    "reason": "insufficient data",
                },
            }

        try:
            # Use our conservative day trading strategy to analyze the market
            analysis_result = self.strategy.analyze(self.market_data)
            
            # Verify analysis result has required fields
            if 'indicators' not in analysis_result or 'price' not in analysis_result['indicators']:
                self.logger.error(f"Invalid analysis result: missing price data. Result: {analysis_result}")
                return {
                    "timestamp": datetime.now(),
                    "signal": {"action": "hold", "confidence": 0, "reason": "invalid analysis"},
                    "trade": {"success": False, "action": "none", "reason": "invalid analysis"},
                    "market_data": {"price": 0}
                }
            
            # Log the price explicitly
            self.logger.info(f"Analysis price: {analysis_result['indicators']['price']}")
        except Exception as e:
            self.logger.error(f"Error analyzing market data: {e}")
            return {
                "timestamp": datetime.now(),
                "signal": {"action": "hold", "confidence": 0, "reason": f"analysis error: {e}"},
                "trade": {"success": False, "action": "none", "reason": f"analysis error: {e}"},
                "market_data": {"price": 0}
            }

        # Convert the strategy output to a signal format compatible with the trader
        signal = {
            "action": analysis_result["signal"].lower(),
            "confidence": analysis_result["confidence"],
            "reason": analysis_result["reason"],
            "source": "conservative_day_strategy",
            "timestamp": datetime.now(),
        }

        # Log analysis details
        self.logger.info(
            f"Fast EMA (9): {analysis_result['indicators']['fast_ema']:.2f}"
        )
        self.logger.info(
            f"Medium EMA (21): {analysis_result['indicators']['medium_ema']:.2f}"
        )
        self.logger.info(
            f"Slow EMA (34): {analysis_result['indicators']['slow_ema']:.2f}"
        )
        self.logger.info(f"SMA (50): {analysis_result['indicators']['sma']:.2f}")
        self.logger.info(f"Current Price: {analysis_result['indicators']['price']:.2f}")
        self.logger.info(
            f"Signal: {signal['action'].upper()} (confidence: {signal['confidence']:.2f})"
        )

        # Execute trade if signal confidence is above threshold
        if signal["confidence"] >= self.confidence_threshold and signal["action"] in [
            "buy",
            "sell",
        ]:
            # Apply risk parameters from strategy
            if signal["action"] == "buy":
                self.stop_loss = analysis_result["risk_params"]["stop_loss"]
                self.take_profit = analysis_result["risk_params"]["take_profit"]

            # Execute trade
            trade_result = self.execute_trade(signal)
        else:
            trade_result = {
                "success": False,
                "action": "none",
                "reason": (
                    f"low_confidence ({signal['confidence']:.2f} < {self.confidence_threshold})"
                    if signal["confidence"] < self.confidence_threshold
                    else "hold_signal"
                ),
            }

        # Return combined results
        return {
            "timestamp": datetime.now(),
            "market_data": {
                "price": (
                    # Use price from analysis result if available
                    analysis_result['indicators']['price']
                    if 'indicators' in analysis_result and 'price' in analysis_result['indicators']
                    # Fall back to market data if needed
                    else (self.market_data.iloc[-1]["close"] 
                          if self.market_data is not None and len(self.market_data) > 0 
                          else 0)
                )
            },
            "signal": signal,
            "trade": trade_result,
            "indicators": analysis_result["indicators"],
            "risk_params": analysis_result["risk_params"],
        }
