#!/usr/bin/env python
"""
Initialization Script for Luno Continuous Trader

This script prepares the environment for running the continuous trader by:
1. Creating necessary directories
2. Setting up default configuration if needed
3. Verifying imports and dependencies
4. Testing basic functionality
"""

import os
import sys
import json
import logging
import importlib
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] %(levelname)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("init_trader")

# Directories to create
DIRECTORIES = [
    "data/logs",
    "data/orders",
    "data/history",
    "data/snapshots",
    "config"
]

def create_directories():
    """Create necessary directories if they don't exist."""
    logger.info("Creating necessary directories...")
    for directory in DIRECTORIES:
        path = Path(directory)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            logger.info(f"  Created: {directory}")
        else:
            logger.info(f"  Already exists: {directory}")

def check_imports():
    """Check if required modules can be imported."""
    logger.info("Checking required imports...")
    required_modules = [
        "config.settings",
        "core.utils.logging_utils",
        "core.api_client",
        "core.data_fetcher",
        "traders.aggressive_day_trader",
        "traders.conservative_day_trader",
        "models.strategies.aggressive_day_trading",
        "models.strategies.conservative_day_trading",
    ]
    
    all_imports_ok = True
    for module_name in required_modules:
        try:
            importlib.import_module(module_name)
            logger.info(f"  ✓ {module_name}")
        except ImportError as e:
            logger.error(f"  ✗ {module_name}: {e}")
            all_imports_ok = False
    
    return all_imports_ok

def ensure_api_config():
    """Ensure API configuration exists."""
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        logger.warning(".env file not found. Creating a template...")
        with open(env_file, "w") as f:
            f.write("# Luno API credentials\n")
            f.write("LUNO_API_KEY=your_api_key_here\n")
            f.write("LUNO_API_SECRET=your_api_secret_here\n")
        logger.info("Created .env template. Please edit it with your API credentials.")
    else:
        logger.info(".env file exists. Make sure it contains your API credentials.")

def ensure_websocket_config():
    """Ensure websocket configuration exists in settings."""
    try:
        from config.settings import get_settings, update_settings
        
        settings = get_settings()
        if "websocket" not in settings:
            logger.info("Adding websocket configuration to settings...")
            settings["websocket"] = {"enabled": True}
            update_settings(settings)
            logger.info("Websocket configuration added.")
        else:
            logger.info("Websocket configuration exists.")
    except ImportError:
        logger.error("Could not import settings module.")
        return False
    except Exception as e:
        logger.error(f"Error updating settings: {e}")
        return False
    
    return True

def ensure_scripts_executable():
    """Make sure scripts are executable."""
    scripts = [
        "run_continuous.sh",
        "run_continuous_traders.py",
        "check_imports.py"
    ]
    
    logger.info("Making scripts executable...")
    for script in scripts:
        if Path(script).exists():
            os.chmod(script, 0o755)
            logger.info(f"  Made executable: {script}")
        else:
            logger.warning(f"  Script not found: {script}")

def main():
    """Main initialization function."""
    logger.info("=" * 60)
    logger.info("Luno Continuous Trader - Initialization")
    logger.info("=" * 60)
    
    # Create directories
    create_directories()
    
    # Check imports
    imports_ok = check_imports()
    
    # Ensure configuration
    ensure_api_config()
    websocket_ok = ensure_websocket_config()
    
    # Make scripts executable
    ensure_scripts_executable()
    
    # Final status
    logger.info("=" * 60)
    if imports_ok and websocket_ok:
        logger.info("✓ Initialization completed successfully!")
        logger.info("You can now run the continuous trader:")
        logger.info("  ./run_continuous.sh --dry-run")
        logger.info("=" * 60)
        return 0
    else:
        logger.error("✗ Initialization completed with errors.")
        logger.error("Please fix the issues above before running the continuous trader.")
        logger.info("=" * 60)
        return 1

if __name__ == "__main__":
    sys.exit(main())