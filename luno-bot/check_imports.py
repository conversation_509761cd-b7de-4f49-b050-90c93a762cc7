#!/usr/bin/env python
"""
Import Checker for Luno Trading Bot

This script checks if all required modules and classes for the continuous trading system
are available and can be properly imported.
"""

import importlib
import sys
import time

def check_import(module_name, class_name=None):
    """Check if a module or class can be imported."""
    try:
        module = importlib.import_module(module_name)
        if class_name:
            getattr(module, class_name)
            return True, f"✅ Successfully imported {module_name}.{class_name}"
        return True, f"✅ Successfully imported {module_name}"
    except ImportError as e:
        return False, f"❌ Failed to import {module_name}: {e}"
    except AttributeError as e:
        return False, f"❌ Failed to import {class_name} from {module_name}: {e}"
    except Exception as e:
        return False, f"❌ Unexpected error importing {module_name}: {e}"

def main():
    """Run all import checks."""
    print("=" * 60)
    print("Luno Trading Bot - Import Checker")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print("-" * 60)
    
    imports_to_check = [
        # Core modules
        ("config.settings", "get_settings"),
        ("config.settings", "update_settings"),
        ("core.api_client", "LunoAPIClient"),
        ("core.data_fetcher", "MarketDataFetcher"),
        ("core.utils.logging_utils", "setup_logger"),
        
        # Trading strategies
        ("traders.aggressive_day_trader", "AggressiveDayTrader"),
        ("traders.conservative_day_trader", "ConservativeDayTrader"),
        ("traders.realtime_trader", "RealTimeTrader"),
        ("traders.base_trader", None),
        
        # Models
        ("models.strategies.aggressive_day_trading", "AggressiveDayTradingStrategy"),
        ("models.strategies.conservative_day_trading", "ConservativeDayTradingStrategy"),
        
        # Standard library dependencies
        ("argparse", None),
        ("asyncio", None),
        ("logging", None),
        ("multiprocessing", None),
        ("os", None),
        ("pathlib", "Path"),
        ("time", None),
        ("datetime", "datetime"),
    ]
    
    all_success = True
    
    for module_name, class_name in imports_to_check:
        success, message = check_import(module_name, class_name)
        print(message)
        if not success:
            all_success = False
        time.sleep(0.1)  # Small delay to make output more readable
    
    print("-" * 60)
    if all_success:
        print("🎉 All imports successful! The continuous trading system should be ready to run.")
    else:
        print("⚠️ Some imports failed. Please fix these issues before running the trading system.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()