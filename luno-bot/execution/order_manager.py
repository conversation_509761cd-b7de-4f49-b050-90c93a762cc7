"""
Order management functionality for the Luno Trading Bot.

This module provides order tracking, status checking, and management capabilities.
"""

from typing import Dict, Any, Optional, List
import logging
from datetime import datetime, timedelta
import time
import json
from pathlib import Path

from core.api_client import LunoAPIClient
from config.logging_config import setup_logging


class OrderManager:
    """Manages trading orders and their lifecycle."""

    def __init__(
        self,
        api_client: Optional[LunoAPIClient] = None,
        config: Optional[Dict[str, Any]] = None,
        logger=None,
    ):
        """Initialize the order manager.

        Args:
            api_client: LunoAPIClient instance
            config: Configuration dictionary
            logger: Logger instance
        """
        self.api_client = api_client or LunoAPIClient()
        self.config = config or {}
        self.logger = logger or setup_logging("order_manager", "data/logs/orders.log")

        # Order tracking
        self.orders = {}  # Dict mapping order_id to order details
        self.pending_orders = []  # List of pending order IDs
        self.completed_orders = []  # List of completed order IDs

        # Create orders directory if it doesn't exist
        self.orders_dir = Path("data/orders")
        self.orders_dir.mkdir(exist_ok=True, parents=True)

        self.logger.info("Order manager initialized")

    def place_order(
        self, pair: str, order_type: str, amount: float, price: Optional[float] = None
    ) -> Dict[str, Any]:
        """Place a new order.

        Args:
            pair: Trading pair
            order_type: 'buy' or 'sell'
            amount: Amount to trade
            price: Limit price (optional, if None then market order)

        Returns:
            Order details
        """
        # Normalize order type
        order_type = order_type.upper()
        if order_type not in ["BUY", "SELL"]:
            raise ValueError(f"Invalid order type: {order_type}")

        try:
            # Place the order
            order_response = self.api_client.place_order(
                pair=pair, type=order_type, volume=amount, price=price
            )

            # Get order ID
            order_id = order_response.get("order_id")
            if not order_id:
                self.logger.error(f"No order ID returned: {order_response}")
                return {"success": False, "error": "no_order_id"}

            # Create order record
            order = {
                "order_id": order_id,
                "pair": pair,
                "type": order_type,
                "amount": amount,
                "price": price,
                "created_at": datetime.now(),
                "status": "PENDING",
                "fills": [],
                "last_checked": None,
                "completed_at": None,
            }

            # Store the order
            self.orders[order_id] = order
            self.pending_orders.append(order_id)

            # Save to file
            self._save_order(order)

            self.logger.info(
                f"Placed {order_type} order: {amount} @ {price or 'market'} ({order_id})"
            )

            return {"success": True, "order": order}

        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            return {"success": False, "error": str(e)}

    def check_order_status(self, order_id: str) -> Dict[str, Any]:
        """Check the status of an order.

        Args:
            order_id: ID of the order to check

        Returns:
            Updated order details
        """
        if order_id not in self.orders:
            self.logger.warning(f"Order {order_id} not found in tracked orders")
            return {"success": False, "error": "order_not_found"}

        try:
            # Update the last checked time
            self.orders[order_id]["last_checked"] = datetime.now()

            # Get the order details from the API
            order_response = self.api_client.get_order(order_id)

            # Update our tracked order with latest status
            order = self.orders[order_id]
            order["status"] = order_response.get("state", "UNKNOWN")

            # Check if the order is completed
            if order["status"] in ["COMPLETE", "CANCELLED", "EXPIRED"]:
                if order_id in self.pending_orders:
                    self.pending_orders.remove(order_id)
                    self.completed_orders.append(order_id)

                # Set completion time if not already set
                if not order.get("completed_at"):
                    order["completed_at"] = datetime.now()

                # Update fills if available
                if "trades" in order_response:
                    order["fills"] = order_response["trades"]

            # Save updated order
            self._save_order(order)

            self.logger.debug(f"Order {order_id} status: {order['status']}")

            return {"success": True, "order": order}

        except Exception as e:
            self.logger.error(f"Error checking order status: {e}")
            return {"success": False, "error": str(e)}

    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order.

        Args:
            order_id: ID of the order to cancel

        Returns:
            Cancellation result
        """
        if order_id not in self.orders:
            self.logger.warning(f"Order {order_id} not found in tracked orders")
            return {"success": False, "error": "order_not_found"}

        try:
            # Cancel the order
            cancel_response = self.api_client.cancel_order(order_id)

            # Update our tracked order
            order = self.orders[order_id]
            order["status"] = "CANCELLED"
            order["completed_at"] = datetime.now()

            # Update order lists
            if order_id in self.pending_orders:
                self.pending_orders.remove(order_id)
                self.completed_orders.append(order_id)

            # Save updated order
            self._save_order(order)

            self.logger.info(f"Cancelled order {order_id}")

            return {"success": True, "order": order}

        except Exception as e:
            self.logger.error(f"Error cancelling order: {e}")
            return {"success": False, "error": str(e)}

    def check_pending_orders(self) -> List[Dict[str, Any]]:
        """Check the status of all pending orders.

        Returns:
            List of updated order details
        """
        updated_orders = []

        for order_id in list(
            self.pending_orders
        ):  # Make a copy to avoid modification during iteration
            result = self.check_order_status(order_id)
            if result["success"]:
                updated_orders.append(result["order"])

        return updated_orders

    def get_order_history(
        self, pair: Optional[str] = None, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get the history of completed orders.

        Args:
            pair: Filter by trading pair (optional)
            limit: Maximum number of orders to return

        Returns:
            List of completed orders
        """
        # Get the most recent completed orders
        orders = []
        for order_id in reversed(self.completed_orders):  # Most recent first
            order = self.orders.get(order_id)
            if order:
                # Filter by pair if specified
                if pair and order["pair"] != pair:
                    continue

                # Add to result
                orders.append(order)

                # Check limit
                if len(orders) >= limit:
                    break

        return orders

    def load_orders(self):
        """Load previously saved orders from files."""
        try:
            # Clear current orders
            self.orders = {}
            self.pending_orders = []
            self.completed_orders = []

            # Find all order files
            order_files = list(self.orders_dir.glob("order_*.json"))

            if not order_files:
                self.logger.info("No order files found to load")
                return

            # Load each file
            for file_path in order_files:
                try:
                    with open(file_path, "r") as f:
                        order = json.load(f)

                    # Convert string timestamps to datetime
                    if isinstance(order.get("created_at"), str):
                        order["created_at"] = datetime.fromisoformat(
                            order["created_at"]
                        )
                    if isinstance(order.get("completed_at"), str):
                        order["completed_at"] = datetime.fromisoformat(
                            order["completed_at"]
                        )
                    if isinstance(order.get("last_checked"), str):
                        order["last_checked"] = datetime.fromisoformat(
                            order["last_checked"]
                        )

                    # Add to orders dict
                    self.orders[order["order_id"]] = order

                    # Add to appropriate list
                    if order["status"] in ["COMPLETE", "CANCELLED", "EXPIRED"]:
                        self.completed_orders.append(order["order_id"])
                    else:
                        self.pending_orders.append(order["order_id"])

                except Exception as e:
                    self.logger.error(f"Error loading order file {file_path}: {e}")

            self.logger.info(
                f"Loaded {len(self.orders)} orders: {len(self.pending_orders)} pending, {len(self.completed_orders)} completed"
            )

        except Exception as e:
            self.logger.error(f"Error loading orders: {e}")

    def _save_order(self, order: Dict[str, Any]):
        """Save an order to file.

        Args:
            order: Order details to save
        """
        try:
            # Create a copy for saving to avoid modifying the original
            save_order = order.copy()

            # Convert datetime objects to strings
            if isinstance(save_order.get("created_at"), datetime):
                save_order["created_at"] = save_order["created_at"].isoformat()
            if isinstance(save_order.get("completed_at"), datetime):
                save_order["completed_at"] = save_order["completed_at"].isoformat()
            if isinstance(save_order.get("last_checked"), datetime):
                save_order["last_checked"] = save_order["last_checked"].isoformat()

            # Create filename
            order_id = save_order["order_id"]
            filename = f"order_{order_id}.json"
            filepath = self.orders_dir / filename

            # Save to file
            with open(filepath, "w") as f:
                json.dump(save_order, f, indent=2)

        except Exception as e:
            self.logger.error(
                f"Error saving order {order.get('order_id', 'unknown')}: {e}"
            )

    def get_active_orders_summary(self) -> Dict[str, Any]:
        """Get a summary of active orders.

        Returns:
            Summary of active orders
        """
        summary = {
            "pending_count": len(self.pending_orders),
            "buy_orders": 0,
            "sell_orders": 0,
            "total_buy_value": 0.0,
            "total_sell_value": 0.0,
            "orders": [],
        }

        for order_id in self.pending_orders:
            order = self.orders.get(order_id)
            if not order:
                continue

            # Add to order list
            summary["orders"].append(
                {
                    "order_id": order["order_id"],
                    "type": order["type"],
                    "pair": order["pair"],
                    "amount": order["amount"],
                    "price": order["price"],
                    "status": order["status"],
                    "created_at": order.get("created_at"),
                }
            )

            # Update counts and values
            if order["type"] == "BUY":
                summary["buy_orders"] += 1
                summary["total_buy_value"] += order["amount"] * (order["price"] or 0)
            elif order["type"] == "SELL":
                summary["sell_orders"] += 1
                summary["total_sell_value"] += order["amount"] * (order["price"] or 0)

        return summary
