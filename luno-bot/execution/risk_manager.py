"""
Risk management functionality for trading.

This module provides risk management capabilities to protect trading capital
and prevent excessive losses.
"""

from typing import Dict, Any, Optional, List, Tuple
import logging
from datetime import datetime, timedelta

from config.logging_config import setup_logging


class RiskManager:
    """Risk manager that enforces risk controls for trading."""

    def __init__(self, config: Optional[Dict[str, Any]] = None, logger=None):
        """Initialize the risk manager.

        Args:
            config: Configuration dictionary with risk parameters
            logger: Logger instance
        """
        self.config = config or {}
        self.logger = logger or setup_logging("risk_manager", "data/logs/risk.log")

        # Extract risk parameters from config
        self.max_trade_size = self.config.get(
            "max_trade_size", 0.01
        )  # Max single trade size
        self.max_daily_trades = self.config.get(
            "max_daily_trades", 10
        )  # Max trades per day
        self.max_open_trades = self.config.get(
            "max_open_trades", 3
        )  # Max concurrent open trades
        self.max_daily_drawdown = self.config.get(
            "max_daily_drawdown", 0.05
        )  # 5% max daily drawdown
        self.max_position_size = self.config.get(
            "max_position_size", 0.1
        )  # Max position as % of portfolio

        # Track trades and drawdown
        self.daily_trades = []
        self.open_trades = []
        self.daily_drawdown = 0.0
        self.portfolio_value = 0.0
        self.initial_daily_value = 0.0
        self.last_reset_day = None

        self.logger.info("Risk manager initialized with parameters:")
        self.logger.info(f"  Max trade size: {self.max_trade_size}")
        self.logger.info(f"  Max daily trades: {self.max_daily_trades}")
        self.logger.info(f"  Max open trades: {self.max_open_trades}")
        self.logger.info(f"  Max daily drawdown: {self.max_daily_drawdown}")
        self.logger.info(f"  Max position size: {self.max_position_size}")

    def reset_daily_metrics(self):
        """Reset daily tracking metrics."""
        today = datetime.now().date()
        if self.last_reset_day != today:
            self.logger.info(f"Resetting daily risk metrics for {today}")
            self.daily_trades = []
            self.daily_drawdown = 0.0
            self.initial_daily_value = self.portfolio_value
            self.last_reset_day = today

    def update_portfolio_value(self, value: float):
        """Update the portfolio value and calculate drawdown.

        Args:
            value: Current portfolio value
        """
        # Initialize if not set
        if self.portfolio_value == 0.0:
            self.portfolio_value = value
            self.initial_daily_value = value
            self.last_reset_day = datetime.now().date()
            return

        # Update value
        self.portfolio_value = value

        # Calculate drawdown if we have an initial value
        if self.initial_daily_value > 0:
            self.daily_drawdown = max(
                0, (self.initial_daily_value - value) / self.initial_daily_value
            )

            # Log significant drawdown
            if self.daily_drawdown > 0.02:  # 2% drawdown
                self.logger.warning(f"Daily drawdown: {self.daily_drawdown:.2%}")

    def evaluate_trade(
        self,
        action: str,
        pair: str,
        price: float,
        amount: float,
        account_balance: float,
    ) -> Tuple[bool, str]:
        """Evaluate if a trade meets risk criteria.

        Args:
            action: Trade action ('buy' or 'sell')
            pair: Trading pair
            price: Trade price
            amount: Trade amount
            account_balance: Current account balance

        Returns:
            Tuple of (allowed, reason) where allowed is a boolean and reason is a string
        """
        # Reset daily metrics if needed
        self.reset_daily_metrics()

        # Convert to lowercase for comparison
        action = action.lower()

        # Calculate trade value
        trade_value = price * amount

        # Check if we've exceeded daily trade count
        if len(self.daily_trades) >= self.max_daily_trades:
            return False, "daily_trade_limit_reached"

        # Check if we've hit max drawdown
        if self.daily_drawdown >= self.max_daily_drawdown:
            return False, "daily_drawdown_exceeded"

        # For buy orders, check position size and value
        if action == "buy":
            # Calculate position size relative to account
            position_size_pct = (
                trade_value / account_balance if account_balance > 0 else 1.0
            )

            # Check if this trade would exceed max position size
            if position_size_pct > self.max_position_size:
                return False, "position_size_exceeded"

            # Check if we already have max open trades
            if len(self.open_trades) >= self.max_open_trades:
                return False, "max_open_trades_reached"

        # For all trades, check max trade size
        max_trade_value = account_balance * self.max_trade_size
        if trade_value > max_trade_value:
            return False, "trade_size_exceeded"

        # If all checks passed, allow the trade
        # Record this trade
        trade_record = {
            "timestamp": datetime.now(),
            "action": action,
            "pair": pair,
            "price": price,
            "amount": amount,
            "value": trade_value,
        }

        self.daily_trades.append(trade_record)

        if action == "buy":
            self.open_trades.append(trade_record)
        elif action == "sell":
            # Remove a corresponding buy if found (this is simplified)
            if self.open_trades:
                self.open_trades.pop(0)

        return True, "trade_allowed"

    def get_position_limit(self, account_balance: float, current_price: float) -> float:
        """Calculate the maximum position size allowed given account balance.

        Args:
            account_balance: Current account balance
            current_price: Current asset price

        Returns:
            Maximum amount that can be bought
        """
        # Max trade value based on account balance and max position size
        max_trade_value = account_balance * self.max_position_size

        # Convert to amount based on current price
        max_amount = max_trade_value / current_price if current_price > 0 else 0

        return max_amount

    def get_trade_recommendation(
        self,
        signal: Dict[str, Any],
        account_balance: float,
        current_price: float,
        current_position: float,
    ) -> Dict[str, Any]:
        """Get risk-adjusted trade recommendation based on signal.

        Args:
            signal: Trading signal
            account_balance: Current account balance
            current_price: Current asset price
            current_position: Current position size

        Returns:
            Risk-adjusted trade recommendation
        """
        action = signal.get("action", "hold").lower()
        confidence = signal.get("confidence", 0.0)

        # Default recommendation is to follow the signal
        recommendation = {
            "action": action,
            "amount": 0.0,
            "risk_adjusted": False,
            "original_signal": signal,
        }

        # If not buy or sell, just return the recommendation
        if action not in ["buy", "sell"]:
            return recommendation

        # For buy orders
        if action == "buy":
            # Calculate max allowed position
            max_position = self.get_position_limit(account_balance, current_price)

            # Scale amount by confidence
            suggested_amount = max_position * min(confidence, 1.0)

            # Round to sensible precision (assume 8 decimal places for crypto)
            suggested_amount = round(suggested_amount, 8)

            # Ensure minimum trade size (e.g., 0.0001 BTC)
            min_trade = 0.0001
            if suggested_amount < min_trade:
                if confidence > 0.3:  # Only trade if confidence is decent
                    suggested_amount = min_trade
                else:
                    # Not enough confidence for minimum trade
                    recommendation["action"] = "hold"
                    recommendation["reason"] = "amount_too_small"
                    return recommendation

            # Set recommended amount
            recommendation["amount"] = suggested_amount
            recommendation["risk_adjusted"] = True

        # For sell orders
        elif action == "sell":
            # For simplicity, recommend selling the entire position
            # Could be more sophisticated with partial sells based on confidence
            if current_position > 0:
                recommendation["amount"] = current_position
                recommendation["risk_adjusted"] = True
            else:
                # Can't sell if we have no position
                recommendation["action"] = "hold"
                recommendation["reason"] = "no_position"

        return recommendation
