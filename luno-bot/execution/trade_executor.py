"""
Trade execution functionality.

This module provides high-level trade execution that combines risk management
and order management to safely execute trades.
"""

from typing import Dict, Any, Optional, List, Tuple
import logging
from datetime import datetime
import time

from core.api_client import LunoAPIClient
from config.logging_config import setup_logging
from .risk_manager import RiskManager
from .order_manager import OrderManager


class TradeExecutor:
    """Executes trades with risk management and order tracking."""

    def __init__(
        self,
        api_client: Optional[LunoAPIClient] = None,
        risk_manager: Optional[RiskManager] = None,
        order_manager: Optional[OrderManager] = None,
        config: Optional[Dict[str, Any]] = None,
        logger=None,
    ):
        """Initialize the trade executor.

        Args:
            api_client: LunoAPIClient instance
            risk_manager: RiskManager instance
            order_manager: OrderManager instance
            config: Configuration dictionary
            logger: Logger instance
        """
        self.api_client = api_client or LunoAPIClient()
        self.config = config or {}
        self.logger = logger or setup_logging("trade_executor", "data/logs/trades.log")

        # Set up managers
        self.risk_manager = risk_manager or RiskManager(config=self.config)
        self.order_manager = order_manager or OrderManager(
            api_client=self.api_client, config=self.config
        )

        # Trade execution settings
        self.max_retries = self.config.get("max_retries", 3)
        self.retry_delay = self.config.get("retry_delay", 2)  # seconds
        self.default_pair = self.config.get("trading", {}).get("pair", "XBTZAR")

        # Track account balances
        self.account_balance = 0.0
        self.positions = {}  # Dict mapping asset to amount
        self.update_account_balances()

        self.logger.info("Trade executor initialized")

    def update_account_balances(self) -> Dict[str, float]:
        """Update the cached account balances.

        Returns:
            Dict mapping asset to balance
        """
        try:
            # Get account balances
            response = self.api_client.get_balances()

            # Reset positions
            self.positions = {}

            # Parse the response
            if "balance" in response:
                for balance in response["balance"]:
                    asset = balance["asset"]
                    amount = float(balance["balance"])

                    # Store in positions dict
                    self.positions[asset] = amount

                    # If ZAR, update account balance
                    if asset == "ZAR":
                        self.account_balance = amount

            # Update risk manager
            portfolio_value = sum(
                self.positions.values()
            )  # Simplified, doesn't consider asset prices
            self.risk_manager.update_portfolio_value(portfolio_value)

            self.logger.debug(f"Updated account balances: {self.positions}")

            return self.positions

        except Exception as e:
            self.logger.error(f"Error updating account balances: {e}")
            return {}

    def execute_trade(
        self,
        action: str,
        pair: str = None,
        amount: float = None,
        price: float = None,
    ) -> Dict[str, Any]:
        """Execute a trade with risk management.

        Args:
            action: 'buy' or 'sell'
            pair: Trading pair (e.g., 'XBTZAR')
            amount: Amount to trade
            price: Limit price (None for market order)

        Returns:
            Dict with trade execution results
        """
        # Use default pair if not specified
        pair = pair or self.default_pair

        # Normalize action
        action = action.lower()
        if action not in ["buy", "sell"]:
            return {"success": False, "error": "invalid_action"}

        # Get current price if not specified
        if price is None:
            try:
                ticker = self.api_client.get_ticker(pair)
                if action == "buy":
                    price = float(ticker["ask"])
                else:
                    price = float(ticker["bid"])
            except Exception as e:
                self.logger.error(f"Error getting current price: {e}")
                return {"success": False, "error": f"price_error: {e}"}

        # Determine amount if not specified
        if amount is None or amount <= 0:
            # Use default trade amount from config
            amount = self.config.get("trading", {}).get("trade_amount", 0.001)

            # For sell, limit to available balance
            if action == "sell":
                # Extract asset from pair (e.g., 'XBT' from 'XBTZAR')
                asset = pair[:3]
                available = self.positions.get(asset, 0.0)
                if amount > available:
                    amount = available
                    if amount <= 0:
                        return {"success": False, "error": "insufficient_balance"}

        # Update account balances
        self.update_account_balances()

        # Check with risk manager
        allowed, reason = self.risk_manager.evaluate_trade(
            action, pair, price, amount, self.account_balance
        )

        if not allowed:
            self.logger.warning(f"Trade rejected by risk manager: {reason}")
            return {"success": False, "error": f"risk_rejection: {reason}"}

        # Execute the trade
        for attempt in range(1, self.max_retries + 1):
            try:
                # Place the order
                result = self.order_manager.place_order(
                    pair=pair, order_type=action, amount=amount, price=price
                )

                if result["success"]:
                    order = result["order"]
                    self.logger.info(
                        f"Executed {action} order for {amount} @ {price} ({order['order_id']})"
                    )

                    # Update account balances after trade
                    self.update_account_balances()

                    return {
                        "success": True,
                        "action": action,
                        "pair": pair,
                        "amount": amount,
                        "price": price,
                        "order_id": order["order_id"],
                        "timestamp": datetime.now(),
                    }
                else:
                    error = result.get("error", "unknown_error")
                    self.logger.error(
                        f"Error placing order (attempt {attempt}/{self.max_retries}): {error}"
                    )

                    if attempt < self.max_retries:
                        time.sleep(self.retry_delay)
                    else:
                        return {"success": False, "error": error}

            except Exception as e:
                self.logger.error(
                    f"Exception executing trade (attempt {attempt}/{self.max_retries}): {e}"
                )

                if attempt < self.max_retries:
                    time.sleep(self.retry_delay)
                else:
                    return {"success": False, "error": str(e)}

        # If we get here, all retries failed
        return {"success": False, "error": "max_retries_exceeded"}

    def execute_signal(
        self, signal: Dict[str, Any], pair: str = None
    ) -> Dict[str, Any]:
        """Execute a trade based on a trading signal.

        Args:
            signal: Trading signal with action, confidence, etc.
            pair: Trading pair (e.g., 'XBTZAR')

        Returns:
            Dict with trade execution results
        """
        # Use default pair if not specified
        pair = pair or self.default_pair

        # Extract action and confidence
        action = signal.get("action", "hold").lower()
        confidence = signal.get("confidence", 0.0)

        # If action is hold, do nothing
        if action == "hold":
            return {"success": True, "action": "hold", "executed": False}

        # Update account balances
        self.update_account_balances()

        # Get risk-adjusted recommendation
        current_price = None
        try:
            ticker = self.api_client.get_ticker(pair)
            current_price = float(ticker["ask"])  # Use ask for both to be conservative
        except Exception as e:
            self.logger.error(f"Error getting current price: {e}")
            return {"success": False, "error": f"price_error: {e}"}

        # Get current position
        asset = pair[:3]
        current_position = self.positions.get(asset, 0.0)

        # Get risk-adjusted recommendation
        recommendation = self.risk_manager.get_trade_recommendation(
            signal, self.account_balance, current_price, current_position
        )

        # If recommendation says hold, do nothing
        if recommendation["action"] == "hold":
            return {
                "success": True,
                "action": "hold",
                "executed": False,
                "reason": recommendation.get("reason", "risk_adjusted"),
            }

        # Execute the trade with the recommended amount
        return self.execute_trade(
            action=recommendation["action"],
            pair=pair,
            amount=recommendation["amount"],
            price=None,  # Use market price
        )

    def cancel_all_orders(self, pair: Optional[str] = None) -> Dict[str, Any]:
        """Cancel all pending orders.

        Args:
            pair: Only cancel orders for this pair (optional)

        Returns:
            Dict with cancellation results
        """
        # Get pending orders
        summary = self.order_manager.get_active_orders_summary()

        # Track results
        results = {"cancelled": 0, "failed": 0, "orders": []}

        # Cancel each order
        for order_info in summary["orders"]:
            # Skip if pair doesn't match
            if pair and order_info["pair"] != pair:
                continue

            # Cancel the order
            result = self.order_manager.cancel_order(order_info["order_id"])

            if result["success"]:
                results["cancelled"] += 1
                results["orders"].append(
                    {"order_id": order_info["order_id"], "status": "cancelled"}
                )
            else:
                results["failed"] += 1
                results["orders"].append(
                    {
                        "order_id": order_info["order_id"],
                        "status": "failed",
                        "error": result.get("error", "unknown"),
                    }
                )

        self.logger.info(
            f"Cancelled {results['cancelled']} orders, {results['failed']} failed"
        )

        return {"success": results["failed"] == 0, "summary": results}

    def monitor_open_orders(self) -> Dict[str, Any]:
        """Check and update all open orders.

        Returns:
            Dict with monitoring results
        """
        # Check pending orders
        updated_orders = self.order_manager.check_pending_orders()

        # Categorize results
        results = {
            "total_checked": len(updated_orders),
            "completed": 0,
            "cancelled": 0,
            "pending": 0,
            "orders": updated_orders,
        }

        # Count by status
        for order in updated_orders:
            status = order.get("status", "UNKNOWN")
            if status == "COMPLETE":
                results["completed"] += 1
            elif status == "CANCELLED" or status == "EXPIRED":
                results["cancelled"] += 1
            else:
                results["pending"] += 1

        # Update account balances if any orders completed
        if results["completed"] > 0:
            self.update_account_balances()

        return results
