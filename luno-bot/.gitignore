# Environment variables
.env
.env.local
.env.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Data and logs
data/
logs/
*.log
*.csv
*.sqlite
*.db

# Jupyter Notebook
.ipynb_checkpoints

# Virtual Environment
venv/
env/
ENV/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Luno trading bot specific
price_chart.png
