#!/usr/bin/env python3
"""
Luno Trading Bot - Backtesting Entry Point

This script provides the entry point for running backtests on historical data,
allowing testing of different trading strategies and parameters.
"""

import argparse
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import json

# Import from our restructured modules
from core.data_fetcher import MarketDataFetcher
from models.moving_average import SimpleMovingAveragePredictor, EMAPredictor
from models.indicators import RSIPredictor, MACDPredictor
from models.indicators.scalping import ScalpingPredictor
from models.regression import LinearRegressionPredictor
from models.strategies.aggressive_day_trading import AggressiveDayTradingStrategy
from config.settings import get_settings
from config.logging_config import setup_logging

# Set up logging
logger = setup_logging("backtest", "data/logs/backtest.log")

# Map of predictor names to classes
PREDICTOR_MAP = {
    "sma": SimpleMovingAveragePredictor,
    "ema": EMAPredictor,
    "rsi": RSIPredictor,
    "macd": MACDPredictor,
    "linreg": LinearRegressionPredictor,
    "scalping": <PERSON>alpingPredictor,
    "aggressive_day": AggressiveDayTradingStrategy,
}


def setup_args():
    """Set up command line arguments."""
    parser = argparse.ArgumentParser(description="Luno Trading Bot - Backtester")
    parser.add_argument(
        "--pair",
        type=str,
        default="XBTMYR",
        help="Trading pair to backtest (default: XBTMYR)",
    )
    parser.add_argument(
        "--days",
        type=int,
        default=30,
        help="Number of days of historical data to use (default: 30)",
    )
    parser.add_argument(
        "--interval",
        type=int,
        default=3600,
        help="Candle interval in seconds (default: 3600 = 1 hour)",
    )
    parser.add_argument(
        "--predictor",
        type=str,
        choices=[
            "sma",
            "ema",
            "rsi",
            "macd",
            "linreg",
            "ensemble",
            "scalping",
            "aggressive_day",
            "all",
        ],
        default="sma",
        help="Prediction model to use (default: sma)",
    )
    parser.add_argument(
        "--amount",
        type=float,
        default=0.001,
        help="Trade amount in base currency (default: 0.001)",
    )
    parser.add_argument(
        "--initial-balance",
        type=float,
        default=1000.0,
        help="Initial account balance in quote currency (default: 1000.0)",
    )
    parser.add_argument(
        "--stop-loss",
        type=float,
        default=0.02,
        help="Stop-loss percentage (default: 0.02 = 2%%)",
    )
    parser.add_argument(
        "--take-profit",
        type=float,
        default=0.03,
        help="Take-profit percentage (default: 0.03 = 3%%)",
    )
    parser.add_argument(
        "--risk-per-trade",
        type=float,
        default=0.01,
        help="Fraction of account to risk per trade (default: 0.01 = 1%%)",
    )
    parser.add_argument(
        "--export-trades", type=str, default=None, help="CSV file to export trade log"
    )
    parser.add_argument(
        "--export-equity",
        type=str,
        default=None,
        help="CSV file to export equity curve",
    )
    parser.add_argument(
        "--optimize",
        action="store_true",
        help="Run parameter optimization (grid search)",
    )
    parser.add_argument(
        "--use-market-structure",
        action="store_true",
        help="Include market structure analysis in backtests",
    )
    parser.add_argument(
        "--sr-lookback",
        type=int,
        default=30,
        help="Lookback period for support/resistance detection (default: 30)",
    )
    parser.add_argument(
        "--trend-periods",
        type=int,
        default=20,
        help="Periods to consider for trend analysis (default: 20)",
    )

    # Add aggressive day trading specific parameters
    aggressive_day_group = parser.add_argument_group(
        "Aggressive Day Trading Parameters"
    )
    aggressive_day_group.add_argument(
        "--ema-fast",
        type=int,
        default=5,
        help="Fast EMA window size for aggressive day trading (default: 5)",
    )
    aggressive_day_group.add_argument(
        "--ema-medium",
        type=int,
        default=13,
        help="Medium EMA window size for aggressive day trading (default: 13)",
    )
    aggressive_day_group.add_argument(
        "--sma-trend",
        type=int,
        default=20,
        help="SMA window size for trend confirmation (default: 20)",
    )
    aggressive_day_group.add_argument(
        "--aggressive-take-profit",
        type=float,
        default=0.025,
        help="Take profit percentage for aggressive day trading (default: 0.025 = 2.5%%)",
    )
    aggressive_day_group.add_argument(
        "--aggressive-stop-loss",
        type=float,
        default=0.015,
        help="Stop loss percentage for aggressive day trading (default: 0.015 = 1.5%%)",
    )
    aggressive_day_group.add_argument(
        "--aggressive-interval",
        type=int,
        default=5,
        help="Candle interval in minutes for aggressive day trading (default: 5)",
    )

    # Add scalping-specific parameters
    scalping_group = parser.add_argument_group("Scalping Strategy Parameters")
    scalping_group.add_argument(
        "--price-deviation",
        type=float,
        default=0.0015,
        help="Price deviation threshold for scalping signals (default: 0.0015 = 0.15%%)",
    )
    scalping_group.add_argument(
        "--volume-spike",
        type=float,
        default=1.5,
        help="Volume spike threshold multiplier for scalping (default: 1.5x)",
    )
    scalping_group.add_argument(
        "--momentum-lookback",
        type=int,
        default=5,
        help="Number of candles to use for momentum calculation (default: 5)",
    )
    scalping_group.add_argument(
        "--max-trade-duration",
        type=int,
        default=30,
        help="Maximum trade duration in minutes for scalping (default: 30)",
    )
    scalping_group.add_argument(
        "--scalping-take-profit",
        type=float,
        default=0.5,
        help="Take profit percentage for scalping (default: 0.5 = 0.5%%)",
    )
    scalping_group.add_argument(
        "--scalping-stop-loss",
        type=float,
        default=0.3,
        help="Stop loss percentage for scalping (default: 0.3 = 0.3%%)",
    )

    return parser.parse_args()


def get_historical_data(pair, days, interval):
    """Get historical data for backtesting."""
    logger.info(
        f"Fetching historical data for {pair} over {days} days with {interval}s interval..."
    )

    # Create data fetcher
    data_fetcher = MarketDataFetcher()

    # Get candle data
    candles_df = data_fetcher.get_candle_data(pair, duration=interval, days_back=days)
    logger.info(f"Fetched {len(candles_df)} candles")

    # Calculate indicators
    data_with_indicators = data_fetcher.calculate_indicators(candles_df)

    return data_with_indicators


def identify_support_resistance(data, window=30, threshold=0.02):
    """Identify support and resistance levels in the data.

    Args:
        data: DataFrame with OHLC data
        window: Window size for local minima/maxima detection
        threshold: Minimum price difference to consider levels distinct

    Returns:
        DataFrame with added support and resistance columns
    """
    logger.info("Identifying support and resistance levels...")

    # Create a copy of the data
    df = data.copy()

    # Initialize support and resistance columns
    df["support"] = np.nan
    df["resistance"] = np.nan

    # Find local minima and maxima
    for i in range(window, len(df) - window):
        # Check if current point is a local minimum
        if all(df["low"].iloc[i] <= df["low"].iloc[i - window : i]) and all(
            df["low"].iloc[i] <= df["low"].iloc[i + 1 : i + window + 1]
        ):
            df.loc[df.index[i], "support"] = df["low"].iloc[i]

        # Check if current point is a local maximum
        if all(df["high"].iloc[i] >= df["high"].iloc[i - window : i]) and all(
            df["high"].iloc[i] >= df["high"].iloc[i + 1 : i + window + 1]
        ):
            df.loc[df.index[i], "resistance"] = df["high"].iloc[i]

    # Fill forward support and resistance levels for use in trading decisions
    df["active_support"] = df["support"].fillna(method="ffill")
    df["active_resistance"] = df["resistance"].fillna(method="ffill")

    # Group nearby levels (within threshold %)
    support_levels = []
    resistance_levels = []

    for i, row in df.iterrows():
        if not np.isnan(row["support"]):
            # Check if this level is close to an existing one
            is_near_existing = False
            for level in support_levels:
                if abs(row["support"] - level) / level < threshold:
                    is_near_existing = True
                    break

            if not is_near_existing:
                support_levels.append(row["support"])

        if not np.isnan(row["resistance"]):
            # Check if this level is close to an existing one
            is_near_existing = False
            for level in resistance_levels:
                if abs(row["resistance"] - level) / level < threshold:
                    is_near_existing = True
                    break

            if not is_near_existing:
                resistance_levels.append(row["resistance"])

    logger.info(
        f"Found {len(support_levels)} support levels and {len(resistance_levels)} resistance levels"
    )

    return df, support_levels, resistance_levels


def analyze_market_trend(data, period=20):
    """Analyze market trend based on price action and moving averages.

    Args:
        data: DataFrame with OHLC and indicator data
        period: Period for trend determination

    Returns:
        DataFrame with trend analysis added
    """
    logger.info("Analyzing market trends...")

    # Create a copy of the data
    df = data.copy()

    # Calculate short and long-term EMAs for trend detection if not already present
    if "ema_9" not in df.columns:
        df["ema_9"] = df["close"].ewm(span=9, adjust=False).mean()

    if "ema_21" not in df.columns:
        df["ema_21"] = df["close"].ewm(span=21, adjust=False).mean()

    # Calculate trend based on EMA relationships and price action
    df["trend"] = "sideways"  # Default

    # Bullish trend: shorter EMAs above longer EMAs and price above both
    bullish_condition = (df["ema_9"] > df["ema_21"]) & (df["close"] > df["ema_9"])
    df.loc[bullish_condition, "trend"] = "bullish"

    # Bearish trend: shorter EMAs below longer EMAs and price below both
    bearish_condition = (df["ema_9"] < df["ema_21"]) & (df["close"] < df["ema_9"])
    df.loc[bearish_condition, "trend"] = "bearish"

    # Calculate higher highs and lower lows for trend confirmation
    df["higher_high"] = False
    df["lower_low"] = False

    for i in range(period, len(df)):
        # Check for higher highs in uptrend
        if df["trend"].iloc[i] == "bullish":
            prev_highs = df["high"].iloc[i - period : i]
            if df["high"].iloc[i] > prev_highs.max():
                df.loc[df.index[i], "higher_high"] = True

        # Check for lower lows in downtrend
        if df["trend"].iloc[i] == "bearish":
            prev_lows = df["low"].iloc[i - period : i]
            if df["low"].iloc[i] < prev_lows.min():
                df.loc[df.index[i], "lower_low"] = True

    # Add trend strength
    df["trend_strength"] = 0

    # Increase strength for consecutive trend days
    for i in range(1, len(df)):
        if df["trend"].iloc[i] == df["trend"].iloc[i - 1]:
            df.loc[df.index[i], "trend_strength"] = df["trend_strength"].iloc[i - 1] + 1

    # Normalize trend strength
    max_strength = df["trend_strength"].max()
    if max_strength > 0:
        df["trend_strength"] = df["trend_strength"] / max_strength

    # Identify swing points
    df["swing_high"] = False
    df["swing_low"] = False

    for i in range(5, len(df) - 5):
        # Swing high: current high higher than all neighboring highs
        if all(df["high"].iloc[i] > df["high"].iloc[i - 5 : i]) and all(
            df["high"].iloc[i] > df["high"].iloc[i + 1 : i + 6]
        ):
            df.loc[df.index[i], "swing_high"] = True

        # Swing low: current low lower than all neighboring lows
        if all(df["low"].iloc[i] < df["low"].iloc[i - 5 : i]) and all(
            df["low"].iloc[i] < df["low"].iloc[i + 1 : i + 6]
        ):
            df.loc[df.index[i], "swing_low"] = True

    logger.info("Market trend analysis completed")

    return df


def run_backtest(
    data,
    predictor_name,
    initial_balance=1000.0,
    amount=0.001,
    stop_loss=0.02,
    take_profit=0.03,
    risk_per_trade=0.01,
    use_market_structure=False,
    sr_lookback=30,
    trend_periods=20,
):
    """Run a backtest with the specified parameters.

    Args:
        data: DataFrame with historical data and indicators
        predictor_name: Name of the predictor to use
        initial_balance: Initial account balance
        amount: Amount to trade per transaction
        stop_loss: Stop-loss percentage
        take_profit: Take-profit percentage
        risk_per_trade: Fraction of account to risk per trade
        use_market_structure: Whether to use market structure in decision making
        sr_lookback: Lookback period for support/resistance detection
        trend_periods: Periods to consider for trend analysis

    Returns:
        Dict with backtest results
    """
    logger.info(f"Running backtest with {predictor_name} predictor...")

    # Add market structure analysis if required
    if use_market_structure:
        # Identify support and resistance levels
        data_with_sr, support_levels, resistance_levels = identify_support_resistance(
            data, window=sr_lookback
        )

        # Analyze market trends
        data_with_structure = analyze_market_trend(data_with_sr, period=trend_periods)

        # Use the enhanced data
        data = data_with_structure

        logger.info("Market structure analysis incorporated into backtest data")

    # Create predictor
    if predictor_name == "ensemble":
        # Create all predictors and use average prediction
        predictors = [cls() for cls in PREDICTOR_MAP.values()]
        for predictor in predictors:
            predictor.train(data, target_col="close")
    else:
        # Create single predictor
        predictor_class = PREDICTOR_MAP.get(predictor_name)
        if predictor_class is None:
            raise ValueError(f"Unknown predictor: {predictor_name}")

        if predictor_name == "aggressive_day":
            # Create model directory
            model_dir = Path("models/aggressive_day")
            model_dir.mkdir(exist_ok=True, parents=True)

            # Get aggressive day trading specific parameters from args if available
            import sys

            args = sys.argv
            ema_fast = 5
            ema_medium = 13
            sma_trend = 20

            # Parse command line args for EMA and SMA parameters
            for i, arg in enumerate(args):
                if arg == "--ema-fast" and i + 1 < len(args):
                    ema_fast = int(args[i + 1])
                elif arg == "--ema-medium" and i + 1 < len(args):
                    ema_medium = int(args[i + 1])
                elif arg == "--sma-trend" and i + 1 < len(args):
                    sma_trend = int(args[i + 1])

            # Initialize the strategy with parameters
            predictor = predictor_class(
                model_dir=str(model_dir),
                stop_loss_pct=stop_loss,
                take_profit_pct=take_profit,
            )

            # Set up model parameters first
            model = {
                "indicators": {
                    "fast_ema": {"window": ema_fast},
                    "medium_ema": {"window": ema_medium},
                    "sma": {"window": sma_trend},
                },
                "parameters": {
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                },
            }

            # Initialize predictor with parameters
            predictor = predictor_class(
                model_dir=str(model_dir),
                stop_loss_pct=stop_loss,
                take_profit_pct=take_profit,
            )
            predictor.model = model  # Override with custom parameters

            # Reinitialize the predictors with updated window sizes
            predictor.fast_ema = EMAPredictor(window_size=ema_fast)
            predictor.medium_ema = EMAPredictor(window_size=ema_medium)
            predictor.trend_sma = SimpleMovingAveragePredictor(window_size=sma_trend)

            logger.info(
                f"Using aggressive day trading parameters: Fast EMA={ema_fast}, Medium EMA={ema_medium}, SMA={sma_trend}, SL={stop_loss*100:.2f}%, TP={take_profit*100:.2f}%"
            )

            predictors = [predictor]
        else:
            predictors = [predictor_class()]

        predictors[0].train(data, target_col="close")

    # Initialize backtest state
    balance = initial_balance
    position = 0  # Current position size
    entry_price = 0  # Price at which position was entered
    trades = []  # List of executed trades
    equity_curve = []  # Track account value over time

    # Loop through data (skip first window_size rows for training)
    window_size = 20  # Minimum data points needed for prediction
    for i in range(window_size, len(data) - 1):
        # Get current state
        current_data = data.iloc[: i + 1]
        current_price = current_data["close"].iloc[-1]
        current_date = current_data["timestamp"].iloc[-1]

        # Make prediction
        if predictor_name == "ensemble":
            # Average predictions from all models
            predictions = [p.predict(current_data)[0] for p in predictors]
            prediction = np.mean(predictions)
        else:
            # Use single predictor
            prediction = predictors[0].predict(current_data)[0]

        # Determine action based on prediction vs current price
        price_change = (prediction - current_price) / current_price

        # Default action is HOLD
        action = "HOLD"
        trade_amount = 0
        trade_price = 0

        # Check for stop-loss or take-profit if in position
        if position > 0:
            price_change_since_entry = (current_price - entry_price) / entry_price

            if price_change_since_entry <= -stop_loss:
                # Stop loss triggered
                action = "SELL"
                trade_amount = position
                trade_price = current_price
                reason = "STOP_LOSS"
            elif price_change_since_entry >= take_profit:
                # Take profit triggered
                action = "SELL"
                trade_amount = position
                trade_price = current_price
                reason = "TAKE_PROFIT"

        # If using market structure, apply additional filters
        if use_market_structure and action == "HOLD":
            current_trend = current_data["trend"].iloc[-1]

            # Get current support, resistance, and trend information
            if "active_support" in current_data.columns:
                current_support = current_data["active_support"].iloc[-1]
                if pd.isna(current_support):
                    current_support = 0
            else:
                current_support = 0

            if "active_resistance" in current_data.columns:
                current_resistance = current_data["active_resistance"].iloc[-1]
                if pd.isna(current_resistance):
                    current_resistance = float("inf")
            else:
                current_resistance = float("inf")

            # Only buy in bullish trends or near support levels
            if price_change > 0.01 and position == 0:
                if current_trend == "bullish" or (
                    current_price <= current_support * 1.02
                ):
                    # Bullish signal confirmed by market structure, BUY
                    action = "BUY"

                    # Calculate position size based on risk
                    risk_amount = balance * risk_per_trade

                    # If we're at support, we can set a tighter stop loss
                    if current_price <= current_support * 1.02:
                        adjusted_stop_loss = (
                            current_support - current_price
                        ) / current_price
                        if adjusted_stop_loss > 0 and adjusted_stop_loss < stop_loss:
                            max_position = risk_amount / (
                                current_price * adjusted_stop_loss
                            )
                        else:
                            max_position = risk_amount / (current_price * stop_loss)
                    else:
                        max_position = risk_amount / (current_price * stop_loss)

                    # Limit to specified amount or less
                    trade_amount = min(amount, max_position)
                    trade_price = current_price

                    if current_trend == "bullish":
                        reason = "PREDICTION_BULLISH_TREND"
                    else:
                        reason = "PREDICTION_AT_SUPPORT"

            # Only sell in bearish trends or near resistance levels
            elif price_change < -0.01 and position > 0:
                if current_trend == "bearish" or (
                    current_price >= current_resistance * 0.98
                ):
                    # Bearish signal confirmed by market structure, SELL
                    action = "SELL"
                    trade_amount = position
                    trade_price = current_price

                    if current_trend == "bearish":
                        reason = "PREDICTION_BEARISH_TREND"
                    else:
                        reason = "PREDICTION_AT_RESISTANCE"

        # If not using market structure or no market structure condition was met
        elif action == "HOLD":
            if price_change > 0.01 and position == 0:
                # Bullish signal, BUY
                action = "BUY"

                # Calculate position size based on risk
                risk_amount = balance * risk_per_trade
                max_position = risk_amount / (current_price * stop_loss)

                # Limit to specified amount or less
                trade_amount = min(amount, max_position)
                trade_price = current_price
                reason = "PREDICTION"
            elif price_change < -0.01 and position > 0:
                # Bearish signal, SELL
                action = "SELL"
                trade_amount = position
                trade_price = current_price
                reason = "PREDICTION"

        # Execute the action
        if action == "BUY":
            cost = trade_amount * trade_price
            if cost <= balance:
                balance -= cost
                position += trade_amount
                entry_price = trade_price

                # Record the trade
                trades.append(
                    {
                        "date": current_date,
                        "action": action,
                        "price": trade_price,
                        "amount": trade_amount,
                        "cost": cost,
                        "balance": balance,
                        "position": position,
                        "reason": reason,
                    }
                )

                logger.debug(f"BUY: {trade_amount} @ {trade_price} = {cost} ({reason})")

        elif action == "SELL":
            proceeds = trade_amount * trade_price
            balance += proceeds
            position -= trade_amount

            # Record the trade
            trades.append(
                {
                    "date": current_date,
                    "action": action,
                    "price": trade_price,
                    "amount": trade_amount,
                    "proceeds": proceeds,
                    "balance": balance,
                    "position": position,
                    "reason": reason,
                }
            )

            logger.debug(
                f"SELL: {trade_amount} @ {trade_price} = {proceeds} ({reason})"
            )

        # Update equity curve
        equity = balance + (position * current_price)
        equity_curve.append(equity)

    # Close any remaining position at the last price
    if position > 0:
        last_price = data["close"].iloc[-1]
        last_date = data["timestamp"].iloc[-1]
        proceeds = position * last_price
        balance += proceeds

        trades.append(
            {
                "date": last_date,
                "action": "SELL",
                "price": last_price,
                "amount": position,
                "proceeds": proceeds,
                "balance": balance,
                "position": 0,
                "reason": "CLOSE",
            }
        )

        logger.debug(f"CLOSE: {position} @ {last_price} = {proceeds}")

    # Calculate performance metrics
    final_equity = balance
    profit_loss = final_equity - initial_balance
    profit_loss_pct = (profit_loss / initial_balance) * 100

    # Count number of trades
    num_trades = len(trades)
    num_profitable = sum(
        1
        for t in trades
        if t["action"] == "SELL" and t.get("proceeds", 0) > t.get("cost", 0)
    )

    win_rate = num_profitable / num_trades if num_trades > 0 else 0

    # Create DataFrames for export
    trades_df = pd.DataFrame(trades)
    equity_df = pd.DataFrame(
        {
            "equity": equity_curve,
            "timestamp": data["timestamp"]
            .iloc[window_size : len(data) - 1]
            .reset_index(drop=True),
        }
    )

    # Package results
    results = {
        "predictor": predictor_name,
        "initial_balance": initial_balance,
        "final_equity": final_equity,  # Changed from final_balance to final_equity for consistency
        "profit_loss": profit_loss,
        "profit_loss_pct": profit_loss_pct,  # Changed from profit_loss_percent to profit_loss_pct for consistency
        "num_trades": num_trades,
        "win_rate": win_rate,
        "stop_loss": stop_loss,
        "take_profit": take_profit,
        "risk_per_trade": risk_per_trade,
        "trades": trades_df,
        "equity_curve": equity_df,
    }

    logger.info(f"Backtest results for {predictor_name}:")
    logger.info(f"  Initial balance: {initial_balance:.2f}")
    logger.info(f"  Final balance: {final_equity:.2f}")
    logger.info(f"  Profit/Loss: {profit_loss:.2f} ({profit_loss_pct:.2f}%)")
    logger.info(f"  # Trades: {num_trades}")
    logger.info(f"  Win rate: {win_rate:.2f}")

    return results


def run_scalping_backtest(
    data,
    initial_balance=1000.0,
    amount=0.001,
    scalping_take_profit=0.5,
    scalping_stop_loss=0.3,
    max_trade_duration_minutes=30,
    price_deviation_threshold=0.0015,
    volume_spike_threshold=1.5,
    momentum_lookback=5,
    overbought_threshold=70.0,
    oversold_threshold=30.0,
):
    """Run a backtest specifically for the scalping strategy.

    Args:
        data: DataFrame with OHLC and indicator data
        initial_balance: Initial balance in quote currency
        amount: Amount to trade in base currency
        scalping_take_profit: Take profit percentage for scalping (e.g., 0.5 = 0.5%)
        scalping_stop_loss: Stop loss percentage for scalping (e.g., 0.3 = 0.3%)
        max_trade_duration_minutes: Maximum trade duration in minutes
        price_deviation_threshold: Minimum price deviation to consider for signals
        volume_spike_threshold: Volume spike multiplier threshold
        momentum_lookback: Number of candles for momentum calculation
        overbought_threshold: RSI threshold for overbought condition
        oversold_threshold: RSI threshold for oversold condition

    Returns:
        Dictionary with backtest results
    """
    logger.info("Running scalping strategy backtest...")

    # Create a copy of the data to avoid modifying the original
    df = data.copy()

    # Debug: Log data shape and columns
    logger.info(f"Backtest data shape: {df.shape}")
    logger.info(f"Available columns: {df.columns.tolist()}")

    # Initialize the scalping predictor
    predictor = ScalpingPredictor(
        price_deviation_threshold=price_deviation_threshold,
        volume_spike_threshold=volume_spike_threshold,
        momentum_lookback=momentum_lookback,
        overbought_threshold=overbought_threshold,
        oversold_threshold=oversold_threshold,
    )

    # Train the predictor (though it's rule-based, so not much training needed)
    predictor.train(df)

    # Initialize tracking variables
    balance = initial_balance
    base_currency = 0.0
    trade_history = []
    equity_history = [initial_balance]
    timestamps = [df.iloc[0]["timestamp"]]
    open_trades = []
    trade_id_counter = 1

    # Convert max trade duration from minutes to number of candles
    # assuming each candle is the interval from the data
    candle_minutes = (
        df["timestamp"].iloc[1] - df["timestamp"].iloc[0]
    ).total_seconds() / 60
    max_trade_duration_candles = max(
        1, int(max_trade_duration_minutes / candle_minutes)
    )

    # Debug logs
    logger.info(f"Candle duration: {candle_minutes:.1f} minutes")
    logger.info(
        f"Max trade duration: {max_trade_duration_minutes} minutes = {max_trade_duration_candles} candles"
    )
    logger.info(f"Price deviation threshold: {price_deviation_threshold}")
    logger.info(f"Volume spike threshold: {volume_spike_threshold}")

    # For debugging: track all signals
    all_signals = []

    # Loop through each candle (start from momentum_lookback to have enough history)
    for i in range(momentum_lookback, len(df) - 1):
        current_time = df.iloc[i]["timestamp"]
        current_price = df.iloc[i]["close"]
        next_price = df.iloc[i + 1]["close"]  # For execution in next candle

        # Update equity history
        equity = balance + base_currency * current_price
        equity_history.append(equity)
        timestamps.append(current_time)

        # Check open trades for take profit, stop loss, or time exit conditions
        for trade in list(open_trades):  # Use a copy for safe iteration
            # Calculate trade duration in candles
            trade_duration = i - trade["entry_candle"]

            # Calculate profit/loss percentage
            if trade["action"] == "buy":
                profit_pct = (
                    (current_price - trade["entry_price"]) / trade["entry_price"] * 100
                )
            else:  # sell
                profit_pct = (
                    (trade["entry_price"] - current_price) / trade["entry_price"] * 100
                )

            # Check take profit condition
            if profit_pct >= scalping_take_profit:
                # Close the trade at take profit
                if trade["action"] == "buy":
                    balance += (
                        trade["amount"] * current_price * 0.999
                    )  # Accounting for 0.1% fees
                    base_currency -= trade["amount"]
                else:  # sell
                    balance -= (
                        trade["amount"] * current_price * 1.001
                    )  # Accounting for 0.1% fees
                    base_currency += trade["amount"]

                # Record the trade
                trade_history.append(
                    {
                        "id": trade["id"],
                        "action": trade["action"],
                        "entry_time": trade["entry_time"],
                        "entry_price": trade["entry_price"],
                        "exit_time": current_time,
                        "exit_price": current_price,
                        "amount": trade["amount"],
                        "profit_pct": profit_pct,
                        "exit_reason": "take_profit",
                        "trade_duration": trade_duration,
                    }
                )

                open_trades.remove(trade)
                logger.info(f"Take profit exit at {current_time}: {profit_pct:.2f}%")

            # Check stop loss condition
            elif profit_pct <= -scalping_stop_loss:
                # Close the trade at stop loss
                if trade["action"] == "buy":
                    balance += (
                        trade["amount"] * current_price * 0.999
                    )  # Accounting for 0.1% fees
                    base_currency -= trade["amount"]
                else:  # sell
                    balance -= (
                        trade["amount"] * current_price * 1.001
                    )  # Accounting for 0.1% fees
                    base_currency += trade["amount"]

                # Record the trade
                trade_history.append(
                    {
                        "id": trade["id"],
                        "action": trade["action"],
                        "entry_time": trade["entry_time"],
                        "entry_price": trade["entry_price"],
                        "exit_time": current_time,
                        "exit_price": current_price,
                        "amount": trade["amount"],
                        "profit_pct": profit_pct,
                        "exit_reason": "stop_loss",
                        "trade_duration": trade_duration,
                    }
                )

                open_trades.remove(trade)
                logger.info(f"Stop loss exit at {current_time}: {profit_pct:.2f}%")

            # Check time-based exit condition
            elif trade_duration >= max_trade_duration_candles:
                # Close the trade based on time
                if trade["action"] == "buy":
                    balance += (
                        trade["amount"] * current_price * 0.999
                    )  # Accounting for 0.1% fees
                    base_currency -= trade["amount"]
                else:  # sell
                    balance -= (
                        trade["amount"] * current_price * 1.001
                    )  # Accounting for 0.1% fees
                    base_currency += trade["amount"]

                # Record the trade
                trade_history.append(
                    {
                        "id": trade["id"],
                        "action": trade["action"],
                        "entry_time": trade["entry_time"],
                        "entry_price": trade["entry_price"],
                        "exit_time": current_time,
                        "exit_price": current_price,
                        "amount": trade["amount"],
                        "profit_pct": profit_pct,
                        "exit_reason": "time_exit",
                        "trade_duration": trade_duration,
                    }
                )

                open_trades.remove(trade)
                logger.info(f"Time-based exit at {current_time}: {profit_pct:.2f}%")

        # Get current data slice for signal generation
        current_slice = df.iloc[: i + 1]

        # Generate signal for current candle
        signal = predictor.generate_signals(current_slice)

        # Record signal for debugging
        all_signals.append(
            {
                "timestamp": current_time,
                "action": signal["action"],
                "confidence": signal["confidence"],
                "reason": signal["reason"],
                "price": current_price,
            }
        )

        # Execute signal in the next candle
        if signal["action"] in ["buy", "sell"] and signal["confidence"] >= 0.2:
            logger.info(
                f"Signal generated at candle {i}: {signal['action']} with confidence {signal['confidence']}"
            )

            # Check if we have enough balance/currency
            if signal["action"] == "buy" and balance >= amount * next_price * 1.001:
                # Buy signal: use balance to buy base currency
                trade_amount = amount  # Amount in base currency
                entry_price = next_price

                # Update balances (accounting for 0.1% fee)
                balance -= trade_amount * entry_price * 1.001
                base_currency += trade_amount

                # Record the trade
                open_trades.append(
                    {
                        "id": trade_id_counter,
                        "action": "buy",
                        "entry_time": df.iloc[i + 1]["timestamp"],
                        "entry_price": entry_price,
                        "amount": trade_amount,
                        "entry_candle": i + 1,
                    }
                )

                trade_id_counter += 1
                logger.info(
                    f"Buy signal executed at {df.iloc[i+1]['timestamp']} at price {entry_price}"
                )

            elif signal["action"] == "sell" and base_currency >= amount:
                # Sell signal: sell base currency for quote currency
                trade_amount = amount  # Amount in base currency
                entry_price = next_price

                # Update balances (accounting for 0.1% fee)
                balance += trade_amount * entry_price * 0.999
                base_currency -= trade_amount

                # Record the trade
                open_trades.append(
                    {
                        "id": trade_id_counter,
                        "action": "sell",
                        "entry_time": df.iloc[i + 1]["timestamp"],
                        "entry_price": entry_price,
                        "amount": trade_amount,
                        "entry_candle": i + 1,
                    }
                )

                trade_id_counter += 1
                logger.info(
                    f"Sell signal executed at {df.iloc[i+1]['timestamp']} at price {entry_price}"
                )

    # Analyze signals for debugging
    buy_signals = [s for s in all_signals if s["action"] == "buy"]
    sell_signals = [s for s in all_signals if s["action"] == "sell"]
    hold_signals = [s for s in all_signals if s["action"] == "hold"]

    logger.info(
        f"Signal summary: {len(buy_signals)} buy, {len(sell_signals)} sell, {len(hold_signals)} hold"
    )

    # Log high confidence signals that weren't executed
    high_conf_buys = [s for s in buy_signals if s["confidence"] >= 0.5]
    high_conf_sells = [s for s in sell_signals if s["confidence"] >= 0.5]

    if high_conf_buys:
        logger.info(f"Found {len(high_conf_buys)} high confidence buy signals")
        for i, signal in enumerate(high_conf_buys[:5]):  # Log first 5
            logger.info(
                f"Buy signal {i+1}: {signal['timestamp']} - Confidence: {signal['confidence']:.2f} - {signal['reason']}"
            )

    if high_conf_sells:
        logger.info(f"Found {len(high_conf_sells)} high confidence sell signals")
        for i, signal in enumerate(high_conf_sells[:5]):  # Log first 5
            logger.info(
                f"Sell signal {i+1}: {signal['timestamp']} - Confidence: {signal['confidence']:.2f} - {signal['reason']}"
            )

    # Close any remaining open trades at the last price
    last_price = df.iloc[-1]["close"]
    last_time = df.iloc[-1]["timestamp"]

    for trade in open_trades:
        # Calculate profit/loss percentage
        if trade["action"] == "buy":
            profit_pct = (
                (last_price - trade["entry_price"]) / trade["entry_price"] * 100
            )
        else:  # sell
            profit_pct = (
                (trade["entry_price"] - last_price) / trade["entry_price"] * 100
            )

        # Close the trade
        if trade["action"] == "buy":
            balance += trade["amount"] * last_price * 0.999
            base_currency -= trade["amount"]
        else:  # sell
            balance -= trade["amount"] * last_price * 1.001
            base_currency += trade["amount"]

        # Record the trade
        trade_history.append(
            {
                "id": trade["id"],
                "action": trade["action"],
                "entry_time": trade["entry_time"],
                "entry_price": trade["entry_price"],
                "exit_time": last_time,
                "exit_price": last_price,
                "amount": trade["amount"],
                "profit_pct": profit_pct,
                "exit_reason": "backtest_end",
                "trade_duration": len(df) - 1 - trade["entry_candle"],
            }
        )

    # Calculate final equity and stats
    final_equity = balance + base_currency * last_price
    profit_loss = final_equity - initial_balance
    profit_loss_pct = (profit_loss / initial_balance) * 100

    # Count trades by type
    total_trades = len(trade_history)
    winning_trades = sum(1 for trade in trade_history if trade["profit_pct"] > 0)
    losing_trades = sum(1 for trade in trade_history if trade["profit_pct"] <= 0)

    # Calculate win rate
    win_rate = winning_trades / total_trades if total_trades > 0 else 0

    # Calculate average profit per trade
    avg_profit = (
        sum(trade["profit_pct"] for trade in trade_history) / total_trades
        if total_trades > 0
        else 0
    )

    # Calculate average trade duration
    avg_duration = (
        sum(trade["trade_duration"] for trade in trade_history) / total_trades
        if total_trades > 0
        else 0
    )

    # Group trades by exit reason
    exit_reasons = {}
    for trade in trade_history:
        reason = trade["exit_reason"]
        if reason not in exit_reasons:
            exit_reasons[reason] = 0
        exit_reasons[reason] += 1

    # Create a DataFrame for the equity curve
    equity_df = pd.DataFrame({"timestamp": timestamps, "equity": equity_history})

    # Calculate Sharpe ratio (using daily returns)
    daily_returns = []
    for i in range(1, len(equity_history)):
        daily_return = (equity_history[i] - equity_history[i - 1]) / equity_history[
            i - 1
        ]
        daily_returns.append(daily_return)

    sharpe_ratio = 0
    if daily_returns:
        mean_return = np.mean(daily_returns)
        std_return = np.std(daily_returns)
        # Annualized Sharpe ratio with risk-free rate of 0
        sharpe_ratio = (
            (mean_return / std_return) * np.sqrt(252) if std_return > 0 else 0
        )

    # Calculate max drawdown
    max_drawdown = 0
    peak = equity_history[0]

    for equity in equity_history:
        if equity > peak:
            peak = equity
        drawdown = (peak - equity) / peak
        max_drawdown = max(max_drawdown, drawdown)

    # Prepare the results
    results = {
        "initial_balance": initial_balance,
        "final_equity": final_equity,
        "profit_loss": profit_loss,
        "profit_loss_pct": profit_loss_pct,
        "total_trades": total_trades,
        "winning_trades": winning_trades,
        "losing_trades": losing_trades,
        "win_rate": win_rate,
        "avg_profit": avg_profit,
        "avg_duration": avg_duration,
        "exit_reasons": exit_reasons,
        "sharpe_ratio": sharpe_ratio,
        "max_drawdown": max_drawdown,
        "trade_history": trade_history,
        "equity_history": equity_df,
    }

    logger.info(
        f"Backtest completed with {total_trades} trades and {profit_loss_pct:.2f}% profit/loss"
    )

    return results


def display_results(results, strategy_name):
    """Display backtest results in a formatted way.

    Args:
        results: Dictionary with backtest results
        strategy_name: Name of the strategy for display
    """
    logger.info("\n" + "=" * 50)
    logger.info(f"BACKTEST RESULTS FOR {strategy_name.upper()}")
    logger.info("=" * 50)

    # Display overall performance
    logger.info(f"Initial Balance: {results['initial_balance']:.2f}")
    # Handle both key names for backwards compatibility
    final_equity = results.get("final_equity", results.get("final_balance", 0))
    logger.info(f"Final Equity: {final_equity:.2f}")
    # Handle both key naming conventions for profit_loss_pct
    profit_loss_pct = results.get(
        "profit_loss_pct", results.get("profit_loss_percent", 0)
    )
    logger.info(f"Profit/Loss: {results['profit_loss']:.2f} ({profit_loss_pct:.2f}%)")

    # Display trade statistics
    # Handle different key names for trade counts
    total_trades = results.get("total_trades", results.get("num_trades", 0))
    winning_trades = results.get("winning_trades", 0)
    losing_trades = results.get("losing_trades", 0)
    win_rate = results.get("win_rate", 0)

    logger.info(f"Total Trades: {total_trades}")
    logger.info(f"Winning Trades: {winning_trades} ({win_rate * 100:.2f}%)")
    logger.info(f"Losing Trades: {losing_trades}")
    # Handle different key names for profit and duration metrics
    avg_profit = results.get("avg_profit", 0)
    avg_duration = results.get("avg_duration", 0)

    logger.info(f"Average Profit per Trade: {avg_profit:.2f}%")
    logger.info(f"Average Trade Duration: {avg_duration:.2f} candles")

    # Display exit reasons if available
    if "exit_reasons" in results:
        logger.info("\nExit Reasons:")
        for reason, count in results["exit_reasons"].items():
            logger.info(f"  {reason}: {count} trades")

    # Display risk metrics if available
    logger.info("")
    if "sharpe_ratio" in results:
        logger.info(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    if "max_drawdown" in results:
        logger.info(f"Max Drawdown: {results['max_drawdown'] * 100:.2f}%")

    logger.info("=" * 50)


def export_trades(trade_history, filename):
    """Export trade history to a CSV file.

    Args:
        trade_history: List of trade dictionaries
        filename: Output filename
    """
    # Convert trade history to DataFrame
    trades_df = pd.DataFrame(trade_history)

    # Save to CSV
    trades_df.to_csv(filename, index=False)
    logger.info(f"Exported {len(trades_df)} trades to {filename}")


def export_equity(equity_df, filename):
    """Export equity curve to a CSV file.

    Args:
        equity_df: DataFrame with timestamp and equity columns
        filename: Output filename
    """
    # Save to CSV
    equity_df.to_csv(filename, index=False)
    logger.info(f"Exported equity curve to {filename}")


def optimize_scalping_parameters(data, initial_balance, amount):
    """Optimize parameters for the scalping strategy using grid search.

    Args:
        data: DataFrame with OHLC and indicator data
        initial_balance: Initial balance in quote currency
        amount: Amount to trade in base currency
    """
    logger.info("\nOptimizing scalping parameters...")

    # Define parameter grid
    take_profit_values = [0.3, 0.5, 0.7, 1.0]
    stop_loss_values = [0.2, 0.3, 0.4, 0.5]
    price_deviation_values = [0.001, 0.0015, 0.002, 0.003]
    volume_spike_values = [1.2, 1.5, 2.0]

    # Track best parameters and results
    best_params = None
    best_result = None
    best_profit = -float("inf")

    # Grid search
    total_combinations = (
        len(take_profit_values)
        * len(stop_loss_values)
        * len(price_deviation_values)
        * len(volume_spike_values)
    )
    current_combination = 0

    for take_profit in take_profit_values:
        for stop_loss in stop_loss_values:
            for price_deviation in price_deviation_values:
                for volume_spike in volume_spike_values:
                    current_combination += 1
                    logger.info(
                        f"Testing combination {current_combination}/{total_combinations}: "
                        f"TP={take_profit}%, SL={stop_loss}%, "
                        f"PD={price_deviation*100:.2f}%, VS={volume_spike}x"
                    )

                    # Run backtest with current parameters
                    results = run_scalping_backtest(
                        data,
                        initial_balance=initial_balance,
                        amount=amount,
                        scalping_take_profit=take_profit,
                        scalping_stop_loss=stop_loss,
                        price_deviation_threshold=price_deviation,
                        volume_spike_threshold=volume_spike,
                    )

                    # Check if this is the best result so far
                    if results["profit_loss_pct"] > best_profit:
                        best_profit = results["profit_loss_pct"]
                        best_result = results
                        best_params = {
                            "take_profit": take_profit,
                            "stop_loss": stop_loss,
                            "price_deviation": price_deviation,
                            "volume_spike": volume_spike,
                        }

    # Display best results
    logger.info("\n" + "=" * 50)
    logger.info("OPTIMIZATION RESULTS")
    logger.info("=" * 50)
    logger.info(f"Best Parameters:")
    for param, value in best_params.items():
        logger.info(f"  {param}: {value}")
    logger.info(f"\nBest Profit/Loss: {best_profit:.2f}%")
    logger.info(f"Total Trades: {best_result['total_trades']}")
    logger.info(f"Win Rate: {best_result['win_rate'] * 100:.2f}%")
    logger.info("=" * 50)

    return best_params, best_result


def optimize_aggressive_day_parameters(data, initial_balance, amount):
    """Optimize parameters for the aggressive day trading strategy using grid search.

    Args:
        data: DataFrame with OHLC and indicator data
        initial_balance: Initial balance in quote currency
        amount: Amount to trade in base currency
    """
    logger.info("\nOptimizing aggressive day trading parameters...")

    # Define parameter grid
    ema_fast_values = [3, 5, 8]
    ema_medium_values = [10, 13, 15]
    sma_trend_values = [18, 20, 25]
    take_profit_values = [0.015, 0.025, 0.035]
    stop_loss_values = [0.01, 0.015, 0.02]

    # Track best parameters and results
    best_params = None
    best_result = None
    best_profit = -float("inf")

    # Grid search
    total_combinations = (
        len(ema_fast_values)
        * len(ema_medium_values)
        * len(sma_trend_values)
        * len(take_profit_values)
        * len(stop_loss_values)
    )
    current_combination = 0

    for ema_fast in ema_fast_values:
        for ema_medium in ema_medium_values:
            for sma_trend in sma_trend_values:
                for take_profit in take_profit_values:
                    for stop_loss in stop_loss_values:
                        current_combination += 1
                        logger.info(
                            f"Testing combination {current_combination}/{total_combinations}: "
                            f"Fast EMA={ema_fast}, Medium EMA={ema_medium}, SMA={sma_trend}, "
                            f"TP={take_profit*100:.1f}%, SL={stop_loss*100:.1f}%"
                        )

                        # Create strategy with current parameters
                        strategy = AggressiveDayTradingStrategy(
                            stop_loss_pct=stop_loss,
                            take_profit_pct=take_profit,
                        )
                        strategy.fast_ema = EMAPredictor(window_size=ema_fast)
                        strategy.medium_ema = EMAPredictor(window_size=ema_medium)
                        strategy.trend_sma = SimpleMovingAveragePredictor(
                            window_size=sma_trend
                        )

                        # Run backtest
                        results = run_backtest(
                            data,
                            "aggressive_day",
                            initial_balance=initial_balance,
                            amount=amount,
                            stop_loss=stop_loss,
                            take_profit=take_profit,
                        )

                        # Check if this is the best result so far
                        if results["profit_loss_pct"] > best_profit:
                            best_profit = results["profit_loss_pct"]
                            best_result = results
                            best_params = {
                                "ema_fast": ema_fast,
                                "ema_medium": ema_medium,
                                "sma_trend": sma_trend,
                                "take_profit": take_profit,
                                "stop_loss": stop_loss,
                            }

    # Display best results
    logger.info("\n" + "=" * 50)
    logger.info("OPTIMIZATION RESULTS FOR AGGRESSIVE DAY TRADING")
    logger.info("=" * 50)
    logger.info(f"Best Parameters:")
    for param, value in best_params.items():
        if param in ["take_profit", "stop_loss"]:
            logger.info(f"  {param}: {value*100:.1f}%")
        else:
            logger.info(f"  {param}: {value}")
    logger.info(f"\nBest Profit/Loss: {best_profit:.2f}%")
    logger.info(f"Total Trades: {best_result['total_trades']}")
    logger.info(f"Win Rate: {best_result['win_rate'] * 100:.2f}%")
    logger.info("=" * 50)

    return best_params, best_result


def optimize_parameters(data, predictor_name, initial_balance, amount):
    """Optimize parameters for the standard strategies.

    Args:
        data: DataFrame with OHLC and indicator data
        predictor_name: Name of the predictor to optimize
        initial_balance: Initial balance in quote currency
        amount: Amount to trade in base currency
    """
    logger.info(f"\nOptimizing parameters for {predictor_name}...")

    # Define parameter grid
    stop_loss_values = [0.01, 0.02, 0.03, 0.05]
    take_profit_values = [0.02, 0.03, 0.05, 0.08]
    risk_values = [0.01, 0.02, 0.05]

    # Track best parameters and results
    best_params = None
    best_result = None
    best_profit = -float("inf")

    # Grid search
    total_combinations = (
        len(stop_loss_values) * len(take_profit_values) * len(risk_values)
    )
    current_combination = 0

    for stop_loss in stop_loss_values:
        for take_profit in take_profit_values:
            for risk in risk_values:
                current_combination += 1
                logger.info(
                    f"Testing combination {current_combination}/{total_combinations}: "
                    f"SL={stop_loss*100:.0f}%, TP={take_profit*100:.0f}%, Risk={risk*100:.0f}%"
                )

                # Run backtest with current parameters
                results = run_backtest(
                    data,
                    predictor_name,
                    initial_balance=initial_balance,
                    amount=amount,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    risk_per_trade=risk,
                )

                # Check if this is the best result so far
                if results["profit_loss_pct"] > best_profit:
                    best_profit = results["profit_loss_pct"]
                    best_result = results
                    best_params = {
                        "stop_loss": stop_loss,
                        "take_profit": take_profit,
                        "risk_per_trade": risk,
                    }

    # Display best results
    logger.info("\n" + "=" * 50)
    logger.info(f"OPTIMIZATION RESULTS FOR {predictor_name.upper()}")
    logger.info("=" * 50)
    logger.info(f"Best Parameters:")
    for param, value in best_params.items():
        logger.info(f"  {param}: {value}")
    logger.info(f"\nBest Profit/Loss: {best_profit:.2f}%")
    logger.info(f"Total Trades: {best_result['total_trades']}")
    logger.info(f"Win Rate: {best_result['win_rate'] * 100:.2f}%")
    logger.info("=" * 50)

    return best_params, best_result


def run_ensemble_backtest(
    data,
    initial_balance=1000.0,
    amount=0.001,
    stop_loss=0.02,
    take_profit=0.03,
    risk_per_trade=0.01,
    use_market_structure=False,
    sr_lookback=30,
    trend_periods=20,
):
    """Run an ensemble backtest that combines multiple predictors.

    Args:
        data: DataFrame with historical data and indicators
        initial_balance: Initial account balance
        amount: Amount to trade per transaction
        stop_loss: Stop-loss percentage
        take_profit: Take-profit percentage
        risk_per_trade: Fraction of account to risk per trade
        use_market_structure: Whether to use market structure in decision making
        sr_lookback: Lookback period for support/resistance detection
        trend_periods: Periods to consider for trend analysis

    Returns:
        Dict with backtest results
    """
    logger.info("Running backtest with ensemble predictor...")

    # Use the existing run_backtest with 'ensemble' as predictor name
    return run_backtest(
        data,
        "ensemble",
        initial_balance=initial_balance,
        amount=amount,
        stop_loss=stop_loss,
        take_profit=take_profit,
        risk_per_trade=risk_per_trade,
        use_market_structure=use_market_structure,
        sr_lookback=sr_lookback,
        trend_periods=trend_periods,
    )


def main():
    """Main function."""
    # Create necessary directories
    Path("data/logs").mkdir(exist_ok=True, parents=True)
    Path("data/backtest").mkdir(exist_ok=True, parents=True)

    # Get command line arguments
    args = setup_args()

    # Get historical data
    data = get_historical_data(args.pair, args.days, args.interval)

    # Results container
    all_results = {}

    if args.predictor == "all":
        # Run all predictors
        for pred_name in PREDICTOR_MAP.keys():
            if pred_name == "scalping":
                # Run scalping backtest with specific parameters
                results = run_scalping_backtest(
                    data,
                    initial_balance=args.initial_balance,
                    amount=args.amount,
                    scalping_take_profit=args.scalping_take_profit,
                    scalping_stop_loss=args.scalping_stop_loss,
                    max_trade_duration_minutes=args.max_trade_duration,
                    price_deviation_threshold=args.price_deviation,
                    volume_spike_threshold=args.volume_spike,
                    momentum_lookback=args.momentum_lookback,
                )
            else:
                # Run standard backtest
                results = run_backtest(
                    data,
                    pred_name,
                    initial_balance=args.initial_balance,
                    amount=args.amount,
                    stop_loss=args.stop_loss,
                    take_profit=args.take_profit,
                    risk_per_trade=args.risk_per_trade,
                    use_market_structure=args.use_market_structure,
                    sr_lookback=args.sr_lookback,
                    trend_periods=args.trend_periods,
                )

            all_results[pred_name] = results

            # Display results
            display_results(results, pred_name)

            # Export trade history if requested
            if args.export_trades:
                export_trades(
                    results["trade_history"], f"{pred_name}_{args.export_trades}"
                )

            # Export equity curve if requested
            if args.export_equity:
                export_equity(
                    results["equity_history"], f"{pred_name}_{args.export_equity}"
                )

    elif args.predictor == "ensemble":
        # Run ensemble backtest (combine multiple predictors)
        results = run_ensemble_backtest(
            data,
            initial_balance=args.initial_balance,
            amount=args.amount,
            stop_loss=args.stop_loss,
            take_profit=args.take_profit,
            risk_per_trade=args.risk_per_trade,
            use_market_structure=args.use_market_structure,
            sr_lookback=args.sr_lookback,
            trend_periods=args.trend_periods,
        )

        all_results["ensemble"] = results

        # Display results
        display_results(results, "ensemble")

        # Export trade history if requested
        if args.export_trades:
            export_trades(results["trade_history"], args.export_trades)

        # Export equity curve if requested
        if args.export_equity:
            export_equity(results["equity_history"], args.export_equity)

    elif args.predictor == "aggressive_day":
        # Run aggressive day trading backtest
        results = run_backtest(
            data,
            args.predictor,
            initial_balance=args.initial_balance,
            amount=args.amount,
            stop_loss=args.aggressive_stop_loss,
            take_profit=args.aggressive_take_profit,
            risk_per_trade=args.risk_per_trade,
            use_market_structure=args.use_market_structure,
            sr_lookback=args.sr_lookback,
            trend_periods=args.trend_periods,
        )

        all_results["aggressive_day"] = results

        # Display results
        display_results(results, "aggressive_day")

        # Export trade history if requested
        if args.export_trades:
            export_trades(results["trade_history"], args.export_trades)

        # Export equity curve if requested
        if args.export_equity:
            export_equity(results["equity_history"], args.export_equity)

    elif args.predictor == "scalping":
        # Run scalping backtest
        results = run_scalping_backtest(
            data,
            initial_balance=args.initial_balance,
            amount=args.amount,
            scalping_take_profit=args.scalping_take_profit,
            scalping_stop_loss=args.scalping_stop_loss,
            max_trade_duration_minutes=args.max_trade_duration,
            price_deviation_threshold=args.price_deviation,
            volume_spike_threshold=args.volume_spike,
            momentum_lookback=args.momentum_lookback,
        )

        all_results["scalping"] = results

        # Display results
        display_results(results, "scalping")

        # Export trade history if requested
        if args.export_trades:
            export_trades(results["trade_history"], args.export_trades)

        # Export equity curve if requested
        if args.export_equity:
            export_equity(results["equity_history"], args.export_equity)

    else:
        # Run a single predictor
        results = run_backtest(
            data,
            args.predictor,
            initial_balance=args.initial_balance,
            amount=args.amount,
            stop_loss=args.stop_loss,
            take_profit=args.take_profit,
            risk_per_trade=args.risk_per_trade,
            use_market_structure=args.use_market_structure,
            sr_lookback=args.sr_lookback,
            trend_periods=args.trend_periods,
        )

        all_results[args.predictor] = results

        # Display results
        display_results(results, args.predictor)

        # Export trade history if requested
        if args.export_trades:
            export_trades(results["trade_history"], args.export_trades)

        # Export equity curve if requested
        if args.export_equity:
            export_equity(results["equity_history"], args.export_equity)

    # If parameter optimization was requested
    if args.optimize:
        if args.predictor == "aggressive_day":
            # Optimize aggressive day trading parameters
            optimize_aggressive_day_parameters(data, args.initial_balance, args.amount)
        elif args.predictor == "scalping":
            # Optimize scalping parameters
            optimize_scalping_parameters(data, args.initial_balance, args.amount)
        else:
            # Optimize standard parameters
            optimize_parameters(data, args.predictor, args.initial_balance, args.amount)

    return 0


if __name__ == "__main__":
    sys.exit(main())
