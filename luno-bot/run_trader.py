#!/usr/bin/env python3
"""
Luno Trading Bot - Trading Entry Point

This script provides the entry point for running the trading functionality,
allowing real-time trading with various strategies.
"""

import argparse
import asyncio
import sys
import signal
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

# Import from our restructured modules
from core.api_client import LunoAPIClient
from core.data_fetcher import MarketDataFetcher
from traders.realtime_trader import RealTimeTrader
from traders.scalping_trader import ScalpingTrader
from traders.aggressive_day_trader import AggressiveDayTrader
from config.settings import get_settings, update_settings
from config.logging_config import setup_logging

# Set up logging
logger = setup_logging("trader", "data/logs/trader.log")


def setup_args():
    """Set up command line arguments."""
    parser = argparse.ArgumentParser(description="Luno Trading Bot - Trader")
    parser.add_argument(
        "--strategy",
        type=str,
        choices=["standard", "scalping", "aggressive_day", "conservative_day"],
        default="standard",
        help="Trading strategy to use (default: standard)",
    )
    parser.add_argument(
        "--pair", type=str, default=None, help="Trading pair (default: from settings)"
    )
    parser.add_argument(
        "--amount",
        type=float,
        default=None,
        help="Amount to trade (default: from settings)",
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=None,
        help="Price movement threshold (default: from settings)",
    )
    parser.add_argument(
        "--predictor",
        type=str,
        choices=["sma", "ema", "rsi", "macd", "linreg", "ensemble", "scalping"],
        default=None,
        help="Prediction model to use (default: from settings)",
    )

    # Market structure analysis options
    market_structure_group = parser.add_argument_group("Market Structure Analysis")
    market_structure_group.add_argument(
        "--use-market-structure",
        action="store_true",
        help="Use market structure analysis",
    )
    market_structure_group.add_argument(
        "--lookback-period",
        type=int,
        default=None,
        help="Number of candles to analyze for trends (default: from settings)",
    )
    market_structure_group.add_argument(
        "--swing-threshold",
        type=float,
        default=None,
        help="Minimum price change to identify swing points (default: from settings)",
    )
    market_structure_group.add_argument(
        "--confidence-threshold",
        type=float,
        default=None,
        help="Minimum confidence for market structure signals (default: from settings)",
    )

    # Aggressive day trading specific options
    aggressive_day_group = parser.add_argument_group("Aggressive Day Trading Strategy")
    aggressive_day_group.add_argument(
        "--ema-fast",
        type=int,
        default=None,
        help="Fast EMA window size for aggressive day trading (default: 5)",
    )
    aggressive_day_group.add_argument(
        "--ema-medium",
        type=int,
        default=None,
        help="Medium EMA window size for aggressive day trading (default: 13)",
    )
    aggressive_day_group.add_argument(
        "--sma-trend",
        type=int,
        default=None,
        help="SMA window size for trend confirmation in aggressive day trading (default: 20)",
    )

    # Scalping specific options
    scalping_group = parser.add_argument_group("Scalping Strategy")
    scalping_group.add_argument(
        "--candle-interval",
        type=int,
        default=None,
        help="Candle interval in seconds for scalping (default: 60)",
    )
    scalping_group.add_argument(
        "--price-deviation",
        type=float,
        default=None,
        help="Price deviation threshold for scalping signals (default: 0.0015)",
    )
    scalping_group.add_argument(
        "--volume-spike",
        type=float,
        default=None,
        help="Volume spike threshold multiplier for scalping (default: 1.5)",
    )
    scalping_group.add_argument(
        "--max-trade-duration",
        type=int,
        default=None,
        help="Maximum trade duration in minutes for scalping (default: 30)",
    )
    scalping_group.add_argument(
        "--scalping-take-profit",
        type=float,
        default=None,
        help="Take profit percentage for scalping (default: 0.5)",
    )
    scalping_group.add_argument(
        "--scalping-stop-loss",
        type=float,
        default=None,
        help="Stop loss percentage for scalping (default: 0.3)",
    )

    parser.add_argument(
        "--no-websocket",
        action="store_true",
        help="Disable WebSocket for real-time data (use polling instead)",
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="Run in dry-run mode (no actual trades)"
    )
    parser.add_argument(
        "--check-interval",
        type=int,
        default=None,
        help="Interval between trading checks in seconds (default: from settings)",
    )
    parser.add_argument(
        "--stop-loss",
        type=float,
        default=None,
        help="Stop loss percentage (default: from settings)",
    )
    parser.add_argument(
        "--take-profit",
        type=float,
        default=None,
        help="Take profit percentage (default: from settings)",
    )

    # Display options
    display_group = parser.add_argument_group("Display Options")
    display_group.add_argument(
        "--display",
        action="store_true",
        help="Enable enhanced visual display (default)",
    )
    display_group.add_argument(
        "--no-display",
        action="store_true",
        help="Disable enhanced visual display",
    )
    display_group.add_argument(
        "--refresh-rate",
        type=float,
        default=None,
        help="Display refresh rate in seconds (default: 5.0)",
    )
    return parser.parse_args()


def update_settings_from_args(args):
    """Update settings based on command line arguments."""
    settings = get_settings()
    updates = {}

    # Only update settings that were explicitly set in arguments
    if args.pair is not None:
        updates["trading"] = updates.get("trading", {})
        updates["trading"]["pair"] = args.pair

    if args.amount is not None:
        updates["trading"] = updates.get("trading", {})
        updates["trading"]["trade_amount"] = args.amount

    if args.threshold is not None:
        updates["trading"] = updates.get("trading", {})
        updates["trading"]["threshold"] = args.threshold

    if args.predictor is not None:
        updates["trading"] = updates.get("trading", {})
        updates["trading"]["default_predictor"] = args.predictor

    # Strategy selection
    updates["trading"] = updates.get("trading", {})
    updates["trading"]["strategy"] = args.strategy

    # Market structure settings
    if args.use_market_structure:
        updates["trading"] = updates.get("trading", {})
        updates["trading"]["use_market_structure"] = True

    if args.lookback_period is not None:
        updates["market_structure"] = updates.get("market_structure", {})
        updates["market_structure"]["lookback_period"] = args.lookback_period

    if args.swing_threshold is not None:
        updates["market_structure"] = updates.get("market_structure", {})
        updates["market_structure"]["swing_threshold"] = args.swing_threshold

    if args.confidence_threshold is not None:
        updates["market_structure"] = updates.get("market_structure", {})
        updates["market_structure"]["confidence_threshold"] = args.confidence_threshold

    # Aggressive day trading settings
    if args.strategy == "aggressive_day" or any(
        [
            args.ema_fast,
            args.ema_medium,
            args.sma_trend,
        ]
    ):
        updates["aggressive_day_trading"] = updates.get("aggressive_day_trading", {})

        if args.ema_fast is not None:
            updates["aggressive_day_trading"]["ema_fast"] = args.ema_fast

        if args.ema_medium is not None:
            updates["aggressive_day_trading"]["ema_medium"] = args.ema_medium

        if args.sma_trend is not None:
            updates["aggressive_day_trading"]["sma"] = args.sma_trend

    # Scalping-specific settings
    if args.strategy == "scalping" or any(
        [
            args.candle_interval,
            args.price_deviation,
            args.volume_spike,
            args.max_trade_duration,
            args.scalping_take_profit,
            args.scalping_stop_loss,
        ]
    ):
        updates["scalping"] = updates.get("scalping", {})

        if args.candle_interval is not None:
            updates["scalping"]["candle_interval"] = args.candle_interval

        if args.price_deviation is not None:
            updates["scalping"]["price_deviation_threshold"] = args.price_deviation

        if args.volume_spike is not None:
            updates["scalping"]["volume_spike_threshold"] = args.volume_spike

        if args.max_trade_duration is not None:
            updates["scalping"]["max_trade_duration_minutes"] = args.max_trade_duration

        if args.scalping_take_profit is not None:
            updates["scalping"]["take_profit"] = args.scalping_take_profit

        if args.scalping_stop_loss is not None:
            updates["scalping"]["stop_loss"] = args.scalping_stop_loss

    if args.no_websocket:
        updates["api"] = updates.get("api", {})
        updates["api"]["use_websocket"] = False

    if args.check_interval is not None:
        updates["trading"] = updates.get("trading", {})
        updates["trading"]["check_interval"] = args.check_interval

    if args.stop_loss is not None:
        updates["trading"] = updates.get("trading", {})
        updates["trading"]["stop_loss"] = args.stop_loss

    if args.take_profit is not None:
        updates["trading"] = updates.get("trading", {})
        updates["trading"]["take_profit"] = args.take_profit

    # Set dry run mode
    updates["trading"] = updates.get("trading", {})
    updates["trading"]["dry_run"] = args.dry_run

    # Display settings
    display_enabled = True
    if args.no_display:
        display_enabled = False
    elif args.display:
        display_enabled = True

    updates["display"] = updates.get("display", {})
    updates["display"]["enabled"] = display_enabled

    if args.refresh_rate is not None:
        updates["display"]["refresh_interval"] = args.refresh_rate

    # Apply updates if any
    if updates:
        update_settings(updates)

    return get_settings()


async def run_trader(dry_run=False):
    """Run the trader."""
    # Initialize these outside the try block to ensure they're available for cleanup
    trader = None
    loop = asyncio.get_running_loop()

    try:
        # Get settings
        settings = get_settings()

        # Extract relevant settings
        trading_settings = settings.get("trading", {})
        pair = trading_settings.get("pair", "XBTZAR")
        strategy = trading_settings.get("strategy", "standard")

        logger.info(f"Starting Luno Trading Bot for {pair} with {strategy} strategy")
        logger.info(f"Dry run mode: {dry_run}")

        # Initialize components
        api_client = LunoAPIClient()
        data_fetcher = MarketDataFetcher(api_client=api_client)

        # Create the appropriate trader based on strategy
        if strategy == "aggressive_day":
            trader = AggressiveDayTrader(
                api_client=api_client,
                data_fetcher=data_fetcher,
                config=settings,
                pair=pair,
            )
        elif strategy == "scalping":
            trader = ScalpingTrader(
                api_client=api_client,
                data_fetcher=data_fetcher,
                config=settings,
                pair=pair,
            )
        elif strategy == "conservative_day":
            from traders.conservative_day_trader import ConservativeDayTrader

            trader = ConservativeDayTrader(
                api_client=api_client,
                data_fetcher=data_fetcher,
                config=settings,
                pair=pair,
            )
        else:  # Default to standard strategy
            trader = RealTimeTrader(
                api_client=api_client,
                data_fetcher=data_fetcher,
                config=settings,
                pair=pair,
            )

        # Register signal handlers for graceful shutdown
        shutdown_event = asyncio.Event()

        def signal_handler():
            logger.info("Shutdown signal received, stopping trader...")
            shutdown_event.set()

        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, signal_handler)

        # Create a task for the trading loop
        trading_task = asyncio.create_task(trader.run_trading_loop())

        # Wait for either the trading loop to complete or a shutdown signal
        done, pending = await asyncio.wait(
            [trading_task, asyncio.create_task(shutdown_event.wait())],
            return_when=asyncio.FIRST_COMPLETED,
        )

        # Cleanup the pending task
        for task in pending:
            task.cancel()

        # Properly stop the trader
        if trader:
            logger.info("Stopping trader...")
            trader.stop()

        # Wait a moment for cleanup to complete
        await asyncio.sleep(1)

        return 0

    except Exception as e:
        logger.error(f"Error running trader: {e}")

        # Ensure trader is stopped even on error
        if trader:
            try:
                trader.stop()
            except Exception as cleanup_error:
                logger.error(f"Error during trader cleanup: {cleanup_error}")

        return 1


def display_startup_info(settings: Dict[str, Any], dry_run: bool):
    """Display startup information with enhanced visual formatting."""
    trading_settings = settings.get("trading", {})
    ms_settings = settings.get("market_structure", {})
    scalping_settings = settings.get("scalping", {})
    use_market_structure = trading_settings.get("use_market_structure", False)
    strategy = trading_settings.get("strategy", "standard")

    # Create a nice header with double box drawing
    logger.info("\n" + "╔" + "═" * 60 + "╗")
    logger.info("║" + " " * 21 + "LUNO TRADING BOT" + " " * 22 + "║")
    logger.info("╠" + "═" * 60 + "╣")

    # Display current time
    from datetime import datetime

    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info("║" + f" Started at: {current_time}".ljust(60) + "║")
    logger.info("╠" + "═" * 60 + "╣")

    # Main configuration
    logger.info("║" + " MAIN CONFIGURATION".ljust(60) + "║")
    logger.info("╟" + "─" * 60 + "╢")
    logger.info("║" + f" Strategy: {strategy.upper()}".ljust(60) + "║")
    logger.info(
        "║" + f" Trading pair: {trading_settings.get('pair', 'XBTZAR')}".ljust(60) + "║"
    )
    logger.info(
        "║"
        + f" Trade amount: {trading_settings.get('trade_amount', 0.001)}".ljust(60)
        + "║"
    )

    # Strategy specific settings
    if strategy == "standard":
        logger.info(
            "║"
            + f" Predictor: {trading_settings.get('default_predictor', 'ensemble')}".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Check interval: {trading_settings.get('check_interval', 300)} seconds".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Stop loss: {trading_settings.get('stop_loss', 0.02) * 100:.2f}%".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Take profit: {trading_settings.get('take_profit', 0.03) * 100:.2f}%".ljust(
                60
            )
            + "║"
        )

    # Connection settings
    logger.info("╟" + "─" * 60 + "╢")
    logger.info(
        "║"
        + f" WebSocket enabled: {settings.get('api', {}).get('use_websocket', True)}".ljust(
            60
        )
        + "║"
    )

    # Highlight dry run mode
    if dry_run:
        logger.info("╟" + "─" * 60 + "╢")
        logger.info(
            "║"
            + " [DRY RUN MODE ENABLED] No real trades will be executed".ljust(60)
            + "║"
        )

    # Display market structure settings if enabled
    if use_market_structure:
        logger.info("╠" + "═" * 60 + "╣")
        logger.info("║" + " MARKET STRUCTURE ANALYSIS".ljust(60) + "║")
        logger.info("╟" + "─" * 60 + "╢")
        logger.info(
            "║"
            + f" Lookback period: {ms_settings.get('lookback_period', 20)} candles".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Swing threshold: {ms_settings.get('swing_threshold', 0.005) * 100:.2f}%".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Confidence threshold: {ms_settings.get('confidence_threshold', 0.7) * 100:.1f}%".ljust(
                60
            )
            + "║"
        )

    # Display aggressive day trading settings if using that strategy
    if strategy == "aggressive_day":
        aggressive_day_settings = settings.get("aggressive_day_trading", {})
        logger.info("╠" + "═" * 60 + "╣")
        logger.info("║" + " AGGRESSIVE DAY TRADING SETTINGS".ljust(60) + "║")
        logger.info("╟" + "─" * 60 + "╢")
        logger.info(
            "║"
            + f" Fast EMA: {aggressive_day_settings.get('ema_fast', 5)} periods".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Medium EMA: {aggressive_day_settings.get('ema_medium', 13)} periods".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" SMA: {aggressive_day_settings.get('sma', 20)} periods".ljust(60)
            + "║"
        )
        logger.info(
            "║"
            + f" Check interval: {trading_settings.get('check_interval', 180)} seconds".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Take profit: {aggressive_day_settings.get('take_profit', 0.025) * 100:.2f}%".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Stop loss: {aggressive_day_settings.get('stop_loss', 0.015) * 100:.2f}%".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Volume spike threshold: {aggressive_day_settings.get('volume_spike_threshold', 1.5)}x".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Confidence threshold: {trading_settings.get('confidence_threshold', 0.5) * 100:.1f}%".ljust(
                60
            )
            + "║"
        )

    # Display scalping settings if using scalping strategy
    elif strategy == "scalping":
        logger.info("╠" + "═" * 60 + "╣")
        logger.info("║" + " SCALPING STRATEGY SETTINGS".ljust(60) + "║")
        logger.info("╟" + "─" * 60 + "╢")
        logger.info(
            "║"
            + f" Candle interval: {scalping_settings.get('candle_interval', 60)} seconds".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Price deviation: {scalping_settings.get('price_deviation_threshold', 0.0015) * 100:.3f}%".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Volume spike threshold: {scalping_settings.get('volume_spike_threshold', 1.5)}x".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Momentum lookback: {scalping_settings.get('momentum_lookback', 5)} candles".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" RSI Overbought: {scalping_settings.get('overbought_threshold', 70.0)}".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" RSI Oversold: {scalping_settings.get('oversold_threshold', 30.0)}".ljust(
                60
            )
            + "║"
        )
        logger.info(
            "║"
            + f" Take profit: {scalping_settings.get('take_profit', 0.5)}%".ljust(60)
            + "║"
        )
        logger.info(
            "║"
            + f" Stop loss: {scalping_settings.get('stop_loss', 0.3)}%".ljust(60)
            + "║"
        )
        logger.info(
            "║"
            + f" Max trade duration: {scalping_settings.get('max_trade_duration_minutes', 30)} minutes".ljust(
                60
            )
            + "║"
        )

    # Footer
    logger.info("╠" + "═" * 60 + "╣")
    logger.info("║" + " Press Ctrl+C to stop the trader".ljust(60) + "║")
    logger.info("╚" + "═" * 60 + "╝\n")


def main():
    """Main function."""
    # Create necessary directories
    Path("data/logs").mkdir(exist_ok=True, parents=True)
    Path("data/orders").mkdir(exist_ok=True, parents=True)
    Path("data/history").mkdir(exist_ok=True, parents=True)

    # Get command line arguments
    args = setup_args()

    # Update settings from arguments
    settings = update_settings_from_args(args)

    # Apply display settings (no-color mode for logging if display is enabled)
    display_enabled = settings.get("display", {}).get("enabled", True)

    # Display startup information
    display_startup_info(settings, args.dry_run)

    # Run the trader
    return asyncio.run(run_trader(dry_run=args.dry_run))


if __name__ == "__main__":
    sys.exit(main())
