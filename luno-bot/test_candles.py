#!/usr/bin/env python3
"""
Test script to debug candle data fetching issues with XBTMYR pair.
"""

from core.api_client import LunoAPIClient
import json
from datetime import datetime, timedelta
import time


def main():
    try:
        # Initialize API client
        api_client = LunoAPIClient()
        pair = "XBTMYR"

        print(f"Testing candle data fetch for {pair}...")

        # Calculate timestamp for 7 days ago
        since = int((datetime.now() - timedelta(days=7)).timestamp())

        # Test different duration values
        durations = [60, 300, 900, 1800, 3600, 86400]

        for duration in durations:
            print(f"\nTesting with duration: {duration} seconds")
            try:
                # Attempt to fetch candles with this duration
                candles_data = api_client.get_candles(
                    pair=pair, duration=duration, since=since
                )

                # Print result
                print(f"Response for duration {duration}:")
                if isinstance(candles_data, dict) and "candles" in candles_data:
                    candles = candles_data["candles"]
                    print(f"Success! Retrieved {len(candles)} candles")

                    # Show first candle if available
                    if candles:
                        print(f"First candle: {candles[0]}")
                    else:
                        print("No candles returned in response")
                else:
                    print(f"Unexpected response format: {candles_data}")

            except Exception as e:
                print(f"Error with duration {duration}: {e}")

            # Add a small delay between requests
            time.sleep(1)

        print("\nTesting complete!")

    except Exception as e:
        print(f"Error initializing test: {e}")


if __name__ == "__main__":
    main()
