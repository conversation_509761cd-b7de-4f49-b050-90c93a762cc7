#!/bin/bash

# Complete Cleanup Script for Luno Trading Bot
# This script performs a more thorough cleanup of the project

echo "====================================================="
echo "Complete Cleanup for Luno Trading Bot"
echo "====================================================="

# Create backup directory
echo "Creating backup directory..."
mkdir -p backup/complete_backup
BACKUP_DIR="backup/complete_backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup old structure
echo "Backing up old directory structure..."
if [ -d "luno_bot" ]; then
    cp -r luno_bot "$BACKUP_DIR/"
    echo "✓ Old 'luno_bot' directory backed up to $BACKUP_DIR/luno_bot"
fi

# Prompt user before removing old structure
echo ""
echo "WARNING: This will remove the old 'luno_bot' directory structure."
echo "All code has been migrated to the new feature-based structure."
echo "A backup has been created at $BACKUP_DIR/luno_bot"
echo ""
read -p "Do you want to continue with removal? (y/n): " confirm

if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
    echo "Removing old directory structure..."
    rm -rf luno_bot
    echo "✓ Removed old 'luno_bot' directory"
else
    echo "Skipping removal of old 'luno_bot' directory"
fi

# Clean up any temporary files
echo "Cleaning up temporary files..."
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} +
find . -name "*.log" -size +10M -delete
echo "✓ Temporary files cleaned up"

# Optional: Reorganize data directories
echo ""
echo "Do you want to reorganize data directories?"
echo "This will consolidate data files into a more organized structure."
read -p "Continue with data reorganization? (y/n): " confirm_data

if [[ $confirm_data == [yY] || $confirm_data == [yY][eE][sS] ]]; then
    echo "Reorganizing data directories..."
    
    # Ensure all required directories exist
    mkdir -p data/logs
    mkdir -p data/orders
    mkdir -p data/history
    mkdir -p data/snapshots
    mkdir -p data/reports
    mkdir -p data/historical
    
    # Move any loose data files to appropriate directories
    find . -maxdepth 1 -name "*.log" -exec mv {} data/logs/ \;
    find . -maxdepth 1 -name "*_order_*.json" -exec mv {} data/orders/ \;
    find . -maxdepth 1 -name "*_trades_*.json" -exec mv {} data/history/ \;
    find . -maxdepth 1 -name "*_snapshot_*.json" -exec mv {} data/snapshots/ \;
    
    echo "✓ Data directories reorganized"
else
    echo "Skipping data reorganization"
fi

echo ""
echo "====================================================="
echo "Cleanup complete!"
echo "====================================================="
echo ""
echo "Project structure has been updated to follow the feature-based organization:"
echo ""
echo "  luno-bot/"
echo "  ├── config/             # Configuration settings"
echo "  ├── core/               # Core functionality"
echo "  ├── execution/          # Trade execution components"
echo "  ├── models/             # Prediction models"
echo "  ├── tools/              # Utility tools"
echo "  ├── traders/            # Trading strategies"
echo "  ├── run_trader.py       # Main entry point"
echo "  └── run_backtest.py     # Backtesting functionality"
echo ""
echo "IMPORTANT UPDATES:"
echo "1. The API client (core/api_client.py) now uses the official Luno SDK"
echo "2. WebSocket functionality has been updated to use luno_streams"
echo "3. A test script (test_luno_sdk.py) has been added to verify SDK integration"
echo ""
echo "To test the SDK integration, run: python test_luno_sdk.py"
echo "To start using the bot, run: python run_trader.py --dry-run"
echo "For more information, see the README.md file." 