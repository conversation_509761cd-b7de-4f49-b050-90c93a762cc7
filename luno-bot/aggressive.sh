#!/bin/bash
#
# Aggressive Trader Launcher for Luno Trading Bot
# This script is a convenient wrapper to run the aggressive trader in live mode.
#
# Usage:
#   ./aggressive.sh                        # Run with default settings
#   ./aggressive.sh --pair ETHZAR          # Run with a different pair
#   ./aggressive.sh --amount 0.002         # Run with custom amount
#   ./aggressive.sh --yes-i-know-this-uses-real-money # Skip confirmation
#

# Directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Ensure we're in the right directory
cd "$SCRIPT_DIR"

# Display banner
echo "=================================================="
echo "      LUNO BOT - AGGRESSIVE TRADER LAUNCHER       "
echo "=================================================="
echo "Starting aggressive trader with provided parameters"
echo "See AGGRESSIVE_LIVE_TRADING.md for documentation"
echo

# Execute the aggressive trader script, passing all arguments
python3 run_aggressive_live.py "$@"