#!/usr/bin/env python
"""
Conservative-Only Live Trading Script for Luno Bot

This script bypasses any dry-run settings and forces the bot to execute real trades
using ONLY the conservative trader strategy.
USE WITH CAUTION - this will use real funds from your Luno account.
"""

import argparse
import logging
import os
import signal
import sys
import time
from datetime import datetime
from pathlib import Path

# Import trading modules
from traders.conservative_day_trader import ConservativeDayTrader
from core.api_client import LunoAPIClient
from core.data_fetcher import MarketDataFetcher
from config.settings import get_settings
from core.utils.logging_utils import setup_logger

# Flag for shutdown
SHUTDOWN_REQUESTED = False

def setup_args():
    """Set up command line arguments for the force live trading script."""
    parser = argparse.ArgumentParser(description="Luno Bot - CONSERVATIVE ONLY LIVE TRADING")
    
    # Trading pair options
    parser.add_argument(
        "--pair",
        type=str,
        default="XBTZAR",
        help="Trading pair for conservative day trader (default: XBTZAR)"
    )
    
    # Time intervals
    parser.add_argument(
        "--interval",
        type=int,
        default=900,  # 15 minutes
        help="Check interval for conservative trader in seconds (default: 900)"
    )
    
    # Risk parameters
    parser.add_argument(
        "--stop-loss",
        type=float,
        default=0.012,
        help="Stop loss percentage for conservative trader (default: 0.012 - 1.2%)"
    )
    parser.add_argument(
        "--take-profit",
        type=float,
        default=0.020,
        help="Take profit percentage for conservative trader (default: 0.020 - 2.0%)"
    )
    
    # Trading amount
    parser.add_argument(
        "--amount",
        type=float,
        default=0.001,
        help="Amount to trade per transaction (default: 0.001)"
    )
    
    # Skip confirmation (dangerous!)
    parser.add_argument(
        "--yes-i-know-this-uses-real-money",
        action="store_true",
        help="Skip confirmation prompt (DANGEROUS - USE WITH CAUTION)"
    )
    
    return parser.parse_args()

def config_from_args(args):
    """Create a configuration dictionary from command line arguments."""
    # Load base settings
    settings = get_settings()
    
    # Update with strategy-specific settings
    trading_config = {
        "pair": args.pair,
        "check_interval": args.interval,
        "stop_loss": args.stop_loss,
        "take_profit": args.take_profit,
        "confidence_threshold": 0.8,  # Default for conservative trading
        "trade_amount": args.amount,
    }
    
    # Update settings
    settings["trading"] = trading_config
    
    return settings

def verify_api_keys():
    """Verify that API keys are available."""
    api_key = os.getenv("LUNO_API_KEY")
    api_secret = os.getenv("LUNO_API_SECRET")
    
    if not api_key or not api_secret:
        print("ERROR: API credentials not found!")
        print("Please set LUNO_API_KEY and LUNO_API_SECRET in your .env file")
        return False
    
    # Mask the keys for display
    masked_key = api_key[:4] + "..." + api_key[-4:] if len(api_key) > 8 else "***"
    masked_secret = "********************" if api_secret else "***"
    
    print(f"API Key: {masked_key}")
    print(f"API Secret: {masked_secret} (length: {len(api_secret) if api_secret else 0})")
    
    return True

def setup_signal_handlers():
    """Set up signal handlers for graceful shutdowns."""
    def signal_handler(sig, frame):
        global SHUTDOWN_REQUESTED
        print(f"\n[{datetime.now()}] Shutdown requested! Gracefully stopping trader...")
        SHUTDOWN_REQUESTED = True
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def run_conservative_trader(args):
    """Run only the conservative day trading strategy in LIVE mode."""
    global SHUTDOWN_REQUESTED
    
    # Configure logger
    log_path = Path("data/logs/conservative_day_trader_LIVE.log")
    logger = setup_logger(
        "conservative_day_trader",
        log_path,
        level=logging.INFO
    )
    
    # Initialize API client and data fetcher
    config = config_from_args(args)
    # Ensure it's a deep copy to avoid modifying the original settings
    config = {k: v.copy() if isinstance(v, dict) else v for k, v in config.items()}
    
    # Initialize API client - FORCE LIVE MODE
    logger.info("*** LIVE MODE ACTIVE - Using real Luno API client ***")
    api_client = LunoAPIClient(
        config.get("api", {}).get("api_key"),
        config.get("api", {}).get("api_secret")
    )
    # Explicitly set dry_run to False
    api_client.dry_run = False
    
    data_fetcher = MarketDataFetcher(
        api_client=api_client
    )
    
    # Initialize trader
    trader = ConservativeDayTrader(
        api_client=api_client,
        data_fetcher=data_fetcher,
        config=config,
        pair=args.pair,
        logger=logger
    )
    # Explicitly set dry_run to False on the trader too
    trader.dry_run = False
    
    # Run trading loop
    logger.info(f"*** LIVE MODE: Starting Conservative Day Trader for {args.pair} ***")
    logger.info("*** WARNING: This is REAL trading with REAL money ***")
    
    while not SHUTDOWN_REQUESTED:
        try:
            # Run a single trading cycle
            cycle_result = trader.run_trading_cycle()
            
            # Log the result
            logger.info(f"Cycle result: {cycle_result['signal']['action']} (confidence: {cycle_result['signal']['confidence']:.2f})")
            if cycle_result['trade']['action'] != 'none':
                logger.info(f"*** LIVE TRADE EXECUTED: {cycle_result['trade']['action']} ***")
            
            # Sleep for the check interval
            sleep_start = time.time()
            while time.time() - sleep_start < trader.check_interval and not SHUTDOWN_REQUESTED:
                time.sleep(1)
            
        except Exception as e:
            logger.error(f"Error in Conservative Day Trader cycle: {e}")
            # Sleep briefly before next attempt
            time.sleep(30)
    
    logger.info("Conservative Day Trader stopped.")

def main():
    """Main function for the conservative-only live trading script."""
    # Create necessary directories
    Path("data/logs").mkdir(exist_ok=True, parents=True)
    Path("data/orders").mkdir(exist_ok=True, parents=True)
    Path("data/history").mkdir(exist_ok=True, parents=True)
    Path("data/snapshots").mkdir(exist_ok=True, parents=True)
    
    # Parse arguments
    args = setup_args()
    
    print("\n" + "="*80)
    print("*** LUNO TRADING BOT - CONSERVATIVE ONLY LIVE TRADING MODE ***")
    print("="*80)
    print("\nWARNING: This script will execute REAL trades with REAL money on Luno.")
    print("         All dry-run settings will be ignored.")
    print("\nTrading configuration:")
    print(f"Trading Pair:    {args.pair}")
    print(f"Stop Loss:      {args.stop_loss*100}%")
    print(f"Take Profit:    {args.take_profit*100}%")
    print(f"Check Interval: {args.interval} seconds")
    print(f"Trade Amount:   {args.amount}")
    print("\nAPI Configuration:")
    
    # Verify API credentials
    if not verify_api_keys():
        print("\nERROR: API keys not configured correctly. Exiting.")
        sys.exit(1)
    
    # Ask for confirmation (unless skipped with flag)
    if not args.yes_i_know_this_uses_real_money:
        confirm = input("\n*** WARNING: THIS WILL USE REAL MONEY! ***\nDo you want to continue? (Type 'yes' to confirm): ")
        if confirm.lower() != "yes":
            print("Operation cancelled by user. Exiting.")
            sys.exit(0)
    
    # Set up signal handlers for graceful shutdown
    setup_signal_handlers()
    
    print(f"\n[{datetime.now()}] Starting conservative trader in LIVE mode")
    print(f"[{datetime.now()}] Press Ctrl+C to stop the trader")
    
    # Run conservative trader directly (no multiprocessing needed)
    try:
        run_conservative_trader(args)
    except KeyboardInterrupt:
        print(f"\n[{datetime.now()}] Keyboard interrupt received")
    finally:
        print(f"[{datetime.now()}] Trader has stopped. Exiting.")

if __name__ == "__main__":
    main()