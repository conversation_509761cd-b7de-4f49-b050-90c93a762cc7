#!/bin/bash
#
# Conservative Trader Launcher for Luno Trading Bot
# This script is a convenient wrapper to run the conservative trader in live mode.
#
# Usage:
#   ./conservative.sh                        # Run with default settings
#   ./conservative.sh --pair XBTZAR          # Run with a different pair
#   ./conservative.sh --amount 0.002         # Run with custom amount
#   ./conservative.sh --yes-i-know-this-uses-real-money # Skip confirmation
#

# Directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Ensure we're in the right directory
cd "$SCRIPT_DIR"

# Display banner
echo "=================================================="
echo "     LUNO BOT - CONSERVATIVE TRADER LAUNCHER      "
echo "=================================================="
echo "Starting conservative trader with provided parameters"
echo "See CONSERVATIVE_LIVE_TRADING.md for documentation"
echo

# Execute the conservative trader script, passing all arguments
python run_conservative_live.py "$@"