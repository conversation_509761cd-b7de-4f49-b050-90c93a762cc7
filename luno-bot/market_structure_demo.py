import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from models.market_structure import MarketStructureAnalyzer, MarketStructurePredictor

def generate_sample_data(days=100, volatility=0.02, trend=0.001):
    """Generate sample price data for testing."""
    # Create date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # Generate price data with trend and volatility
    np.random.seed(42)  # For reproducible results
    prices = 100 * (1 + np.cumsum(np.random.normal(trend, volatility, len(date_range))))
    
    # Add some patterns (higher highs, lower lows, etc.)
    # Add a strong uptrend in the middle
    mid_point = len(prices) // 2
    prices[mid_point:mid_point+20] *= np.linspace(1, 1.2, 20)
    
    # Add a downtrend near the end
    prices[-20:] *= np.linspace(1, 0.9, 20)
    
    # Create DataFrame
    data = pd.DataFrame({
        'timestamp': date_range,
        'open': prices * np.random.uniform(0.99, 1.01, len(prices)),
        'high': prices * np.random.uniform(1.01, 1.03, len(prices)),
        'low': prices * np.random.uniform(0.97, 0.99, len(prices)),
        'close': prices,
        'volume': np.random.uniform(1000, 5000, len(prices)) * (1 + 0.5 * np.sin(np.linspace(0, 8*np.pi, len(prices))))
    })
    
    return data

def resample_data(data, timeframe):
    """Resample data to a different timeframe."""
    # Map string timeframes to pandas offset aliases
    timeframe_map = {
        '1d': '1D',
        '4h': '4H',
        '1h': '1H',
        '30m': '30min',
        '15m': '15min',
        '5m': '5min'
    }
    
    if timeframe not in timeframe_map:
        raise ValueError(f"Unsupported timeframe: {timeframe}")
    
    # Set timestamp as index for resampling
    data_indexed = data.set_index('timestamp')
    
    # Resample
    resampled = data_indexed.resample(timeframe_map[timeframe]).agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }).dropna()
    
    # Reset index to get timestamp as a column again
    resampled = resampled.reset_index()
    
    return resampled

def visualize_market_structure(data, analysis):
    """Visualize market structure analysis with price chart."""
    plt.figure(figsize=(12, 8))
    
    # Plot price data
    plt.subplot(2, 1, 1)
    plt.plot(data['timestamp'], data['close'], label='Close Price')
    
    # Plot support and resistance levels
    for level, strength in analysis.get('support_levels', []):
        plt.axhline(y=level, color='g', linestyle='--', alpha=0.5 + 0.5 * strength, 
                   label=f'Support ({level:.2f}, strength: {strength:.2f})')
    
    for level, strength in analysis.get('resistance_levels', []):
        plt.axhline(y=level, color='r', linestyle='--', alpha=0.5 + 0.5 * strength,
                   label=f'Resistance ({level:.2f}, strength: {strength:.2f})')
    
    # Mark swing points
    if 'swing_high' in data.columns and 'swing_low' in data.columns:
        swing_highs = data[data['swing_high']]
        swing_lows = data[data['swing_low']]
        plt.scatter(swing_highs['timestamp'], swing_highs['high'], color='r', marker='^', label='Swing High')
        plt.scatter(swing_lows['timestamp'], swing_lows['low'], color='g', marker='v', label='Swing Low')
    
    plt.title(f'Market Structure Analysis - Trend: {analysis.get("trend", "unknown")}, ' +
             f'Structure: {analysis.get("structure", "unknown")}')
    plt.legend(loc='best')
    plt.grid(True)
    
    # Plot volume
    plt.subplot(2, 1, 2)
    plt.bar(data['timestamp'], data['volume'], color='b', alpha=0.5)
    plt.title('Volume')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

def print_signal_details(signal):
    """Print details of a trading signal."""
    print("\n" + "="*50)
    print(f"SIGNAL: {signal['action'].upper()}")
    print(f"Confidence: {signal['confidence']:.2f}")
    print(f"Reason: {signal['reason']}")
    if 'supporting_timeframes' in signal:
        print(f"Supporting timeframes: {', '.join(signal['supporting_timeframes'])}")
    print(f"Generated at: {signal['timestamp']}")
    print("="*50)

def demo_single_timeframe():
    """Demonstrate market structure analysis on a single timeframe."""
    print("\n--- SINGLE TIMEFRAME ANALYSIS DEMO ---\n")
    
    # Generate sample data
    data = generate_sample_data(days=100)
    print(f"Generated sample data with {len(data)} data points")
    
    # Initialize analyzer
    analyzer = MarketStructureAnalyzer(
        swing_window=5,
        detection_threshold=0.01,
        lookback_period=20
    )
    
    # Analyze market structure
    print("Analyzing market structure...")
    analysis = analyzer.analyze_market_structure(data)
    
    # Print analysis results
    print("\nMarket Structure Analysis Results:")
    print(f"- Trend: {analysis['trend']}")
    print(f"- Structure: {analysis['structure']}")
    print(f"- Higher Highs: {analysis['higher_highs']}")
    print(f"- Higher Lows: {analysis['higher_lows']}")
    print(f"- Lower Highs: {analysis['lower_highs']}")
    print(f"- Lower Lows: {analysis['lower_lows']}")
    print(f"- ADX: {analysis['adx']:.2f}")
    
    if analysis['support_levels']:
        print("\nSupport Levels:")
        for level, strength in analysis['support_levels']:
            print(f"  - {level:.2f} (strength: {strength:.2f})")
    
    if analysis['resistance_levels']:
        print("\nResistance Levels:")
        for level, strength in analysis['resistance_levels']:
            print(f"  - {level:.2f} (strength: {strength:.2f})")
    
    if analysis['recent_break']:
        print("\nRecent Breakout:")
        print(f"  - Level: {analysis['recent_break']['level']:.2f}")
        print(f"  - Type: {analysis['recent_break']['type']}")
        print(f"  - Direction: {analysis['recent_break']['direction']}")
        print(f"  - Strength: {analysis['recent_break']['strength']:.2f}")
        if 'is_confirmed' in analysis['recent_break']:
            print(f"  - Confirmed: {analysis['recent_break']['is_confirmed']}")
    
    # Generate signal
    predictor = MarketStructurePredictor(
        lookback_period=20,
        swing_threshold=0.01,
        confidence_threshold=0.6
    )
    
    signal = predictor.generate_signal(single_data=data)
    print_signal_details(signal)
    
    # Visualize the analysis
    visualize_market_structure(data, analysis)

def demo_multi_timeframe():
    """Demonstrate market structure analysis on multiple timeframes."""
    print("\n--- MULTI-TIMEFRAME ANALYSIS DEMO ---\n")
    
    # Generate sample data (more data points for shorter timeframes)
    base_data = generate_sample_data(days=100)
    print(f"Generated base sample data with {len(base_data)} data points")
    
    # Create multi-timeframe data
    timeframes = ['1d', '4h', '1h']
    data_dict = {
        '1d': base_data,  # Daily data
        '4h': resample_data(base_data, '4h'),  # 4-hour data
        '1h': resample_data(base_data, '1h')   # 1-hour data
    }
    
    print(f"Created {len(timeframes)} timeframes for analysis")
    for tf, df in data_dict.items():
        print(f"  - {tf}: {len(df)} data points")
    
    # Initialize analyzer with multiple timeframes
    analyzer = MarketStructureAnalyzer(
        swing_window=5,
        detection_threshold=0.01,
        lookback_period=20,
        timeframes=timeframes
    )
    
    # Analyze multiple timeframes
    print("Analyzing market structure across multiple timeframes...")
    analyses = analyzer.analyze_multiple_timeframes(data_dict)
    
    # Print aggregated analysis results
    print("\nAggregated Market Structure Analysis Results:")
    aggregated = analyses.get('aggregated', {})
    if aggregated:
        print(f"- Trend: {aggregated.get('trend', 'unknown')}")
        print(f"- Structure: {aggregated.get('structure', 'unknown')}")
        print(f"- ADX (avg): {aggregated.get('adx', 0):.2f}")
        print(f"- Higher Highs: {aggregated.get('higher_highs', False)}")
        print(f"- Higher Lows: {aggregated.get('higher_lows', False)}")
        print(f"- Lower Highs: {aggregated.get('lower_highs', False)}")
        print(f"- Lower Lows: {aggregated.get('lower_lows', False)}")
        
        if 'support_levels' in aggregated and aggregated['support_levels']:
            print("\nAggregated Support Levels:")
            for level, strength in aggregated['support_levels']:
                print(f"  - {level:.2f} (strength: {strength:.2f})")
        
        if 'resistance_levels' in aggregated and aggregated['resistance_levels']:
            print("\nAggregated Resistance Levels:")
            for level, strength in aggregated['resistance_levels']:
                print(f"  - {level:.2f} (strength: {strength:.2f})")
    
    # Generate signal using multi-timeframe analysis
    predictor = MarketStructurePredictor(
        lookback_period=20,
        swing_threshold=0.01,
        confidence_threshold=0.6,
        timeframes=timeframes
    )
    
    signal = predictor.generate_signal(data=data_dict)
    print_signal_details(signal)
    
    # Visualize the analysis for the primary timeframe
    visualize_market_structure(data_dict['1d'], aggregated)

if __name__ == "__main__":
    print("MARKET STRUCTURE ANALYSIS DEMO")
    print("==============================")
    
    # Run both demos
    print("\nRunning single timeframe demo...")
    demo_single_timeframe()
    
    print("\nRunning multi-timeframe demo...")
    demo_multi_timeframe()