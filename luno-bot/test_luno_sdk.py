#!/usr/bin/env python3
"""
Test script for the Luno SDK integration.

This script verifies that the updated API client is working correctly with the Luno SDK.
"""

import os
import sys
import json
import time
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import our client
from core.api_client import LunoAPIClient, LunoWebSocketClient

# Load environment variables
load_dotenv()


def print_section(title):
    """Print a section header."""
    print("\n" + "=" * 50)
    print(title)
    print("=" * 50)


def test_rest_api():
    """Test the REST API client."""
    print_section("Testing Luno REST API Client")

    # Create API client
    api_client = LunoAPIClient()

    # Test getting tickers
    print("\nGetting tickers...")
    try:
        tickers = api_client.get_tickers()
        print(f"Retrieved {len(tickers.get('tickers', []))} tickers")
    except Exception as e:
        print(f"Error getting tickers: {e}")

    # Test getting ticker for XBTZAR
    print("\nGetting ticker for XBTZAR...")
    try:
        ticker = api_client.get_ticker("XBTZAR")
        print(f"Bid: {ticker.get('bid')}")
        print(f"Ask: {ticker.get('ask')}")
        print(f"Last trade: {ticker.get('last_trade')}")
    except Exception as e:
        print(f"Error getting ticker: {e}")

    # Test getting balances
    print("\nGetting account balances...")
    try:
        balances = api_client.get_balances()
        for balance in balances.get("balance", []):
            print(
                f"{balance.get('asset')}: {balance.get('balance')} (reserved: {balance.get('reserved')})"
            )
    except Exception as e:
        print(f"Error getting balances: {e}")

    print("\nREST API tests completed")


def test_websocket_client():
    """Test the WebSocket client."""
    # Create WebSocket client
    client = LunoWebSocketClient(pair="XBTZAR")

    # Set up a handler
    def message_handler(message):
        print(f"Received: {message}")

    # Start the WebSocket client
    client.start(callback=message_handler)

    # Wait for some time to receive messages
    print("WebSocket connection established. Waiting for messages...")
    print("You should see updates when trades or orderbook changes occur.")
    try:
        # Wait for messages with a visible countdown
        print("Press Ctrl+C to stop manually, or wait 30 seconds.")
        for i in range(30):
            print(".", end="", flush=True)
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    finally:
        print("\nStopping WebSocket client...")
        # Properly stop the client
        client.stop()
        # Give some time for cleanup
        time.sleep(2)

    print("\nWebSocket tests completed")


def main():
    """Main function."""
    # Check for required environment variables
    if not os.getenv("LUNO_API_KEY") or not os.getenv("LUNO_API_SECRET"):
        print(
            "Error: LUNO_API_KEY and LUNO_API_SECRET environment variables must be set"
        )
        print("Please create a .env file or set them in your environment")
        return 1

    # Test REST API
    test_rest_api()

    # Ask if user wants to test WebSocket
    test_ws = (
        input("\nDo you want to test the WebSocket client? (y/n): ").lower() == "y"
    )
    if test_ws:
        test_websocket_client()

    print_section("All tests completed")
    return 0


if __name__ == "__main__":
    sys.exit(main())
