# Optimized Settings for Luno Trading Bot

## Candle Interval Strategy Optimization

This document explains the optimized settings for the Luno Trading Bot, focusing on candle intervals and related parameters for different trading strategies.

## Standard Strategy Settings

When using the standard trading strategy, the bot uses the following settings:

| Parameter | Value | Description |
|-----------|-------|-------------|
| default_candle_duration | 300 | 5-minute candles for standard strategy analysis |
| threshold | 0.007 | 0.7% price movement threshold for signals |
| stop_loss | 0.018 | 1.8% stop loss value |
| take_profit | 0.045 | 4.5% take profit value |
| default_predictor | ensemble | Combination of multiple technical indicators |
| check_interval | 2 | Check market every 2 seconds |
| min_trade_interval | 15 | Minimum 15 seconds between trades |

The 5-minute candles (300 seconds) provide a good balance between capturing significant price movements while filtering out market noise. This timeframe is particularly effective for cryptocurrency markets like Bitcoin, which can have significant price movements within a single day.

## Scalping Strategy Settings

The scalping strategy uses these specialized settings:

| Parameter | Value | Description |
|-----------|-------|-------------|
| candle_interval | 15 | 15-second candles for scalping analysis |
| price_deviation_threshold | 0.0012 | 0.12% minimum price movement for signals |
| max_trade_duration_minutes | 8 | Maximum 8 minutes per trade |
| take_profit | 1.2 | 1.2% take profit for scalping trades |
| stop_loss | 0.5 | 0.5% stop loss for quick exit from unfavorable trades |
| volume_spike_threshold | 1.5 | Volume 1.5x above average triggers signal |

The 15-second candles are optimized for capturing rapid price movements, which is essential for a successful scalping strategy. This very short timeframe allows the bot to enter and exit positions quickly, capitalizing on small price fluctuations that occur frequently in cryptocurrency markets.

## Market Structure Analysis Settings

These settings apply to both strategies when market structure analysis is enabled:

| Parameter | Value | Description |
|-----------|-------|-------------|
| lookback_period | 48 | Analyze 48 candles for pattern detection |
| swing_threshold | 0.008 | 0.8% minimum for swing point identification |
| confidence_threshold | 0.75 | 75% minimum confidence for signals |

With the shorter candle intervals, the lookback period has been increased to 48 candles. This ensures that the market structure analysis has enough historical data to identify meaningful patterns, even when using shorter timeframes.

## Performance Optimization

These settings have been optimized to potentially increase profitability by:

1. **Faster Response to Market Changes**: Shorter candle intervals and check times allow the bot to react more quickly to market movements
   
2. **Improved Risk-Reward Ratio**: The take profit to stop loss ratio for both strategies is higher than the default settings

3. **More Trading Opportunities**: Shorter candles and reduced thresholds create more potential entry points

4. **Refined Signal Quality**: Adjusted confidence and swing thresholds help filter out false signals while still capturing genuine opportunities

## How to Switch Between Strategies

To use the standard strategy:
```
python run_trader.py --strategy standard --dry-run
```

To use the optimized scalping strategy:
```
python run_trader.py --strategy scalping --dry-run
```

## Notes on Further Optimization

For optimal results, consider:

1. Running backtests with these settings to validate performance
2. Monitoring market volatility and adjusting candle intervals accordingly
3. Starting with dry-run mode before deploying with real funds
4. Adjusting the volume_spike_threshold based on current market conditions

Remember that market conditions change frequently, so regular review and adjustment of these parameters is recommended for continued success.