# Continuous Trading with <PERSON><PERSON>

This document explains how to run <PERSON><PERSON>t's trading strategies continuously, focusing on the Aggressive Day Trader and Conservative Day Trader strategies.

## Overview

The continuous trading functionality allows you to:

- Run both Aggressive Day Trading and Conservative Day Trading strategies simultaneously or in alternation
- Configure each strategy with different trading pairs and risk parameters
- Monitor performance through detailed logs
- Run in dry-run mode to test strategies without making actual trades
- Automatically restart the trading system if it crashes

## Getting Started

### Prerequisites

- Ensure your API keys are set in the `.env` file
- Make sure all dependencies are installed via `pip install -r requirements.txt`
- Run the initialization script to set up the environment: `./init_continuous_trader.py`

### Basic Usage

Start the continuous trading system in dry-run mode (no real trades):

```bash
./run_continuous.sh --dry-run
```

Run with real trading (uses real money - use with caution!):

```bash
./run_continuous.sh
```

## Trading Strategies

### Aggressive Day Trading Strategy

Designed for more frequent trading with higher risk/reward profile:

- Uses Fast EMA (5), Medium EMA (13), and SMA (20) indicators
- Lower confidence threshold (50%) to catch more trading opportunities
- Default check interval of 3 minutes
- Default stop-loss of 1.5% and take-profit of 2.5%
- Best suited for volatile market conditions

### Conservative Day Trading Strategy

Designed for more cautious trading with fewer but higher confidence trades:

- Uses triple EMA system (9, 21, 34) and SMA (50) indicators
- Higher confidence threshold (80%) to reduce false signals
- Default check interval of 15 minutes
- Default stop-loss of 1.2% and take-profit of 2.0%
- Best suited for stable or trending market conditions

## Running Modes

### Parallel Mode (Default)

Both strategies run simultaneously in separate processes:

```bash
./run_continuous.sh --mode parallel --dry-run
```

- Each strategy operates independently
- Can trade different pairs simultaneously
- Uses more system resources

### Alternating Mode

Strategies run one after another with a configurable delay:

```bash
./run_continuous.sh --mode alternating --alternating-delay 3600 --dry-run
```

- Only one strategy is active at a time
- Switches between strategies based on the configured delay
- Uses fewer system resources

## Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `--mode` | Run mode: `parallel` or `alternating` | `parallel` |
| `--dry-run` | Run in simulation mode (no real trades) | Not enabled |
| `--aggressive-pair` | Trading pair for aggressive strategy | `XBTZAR` |
| `--conservative-pair` | Trading pair for conservative strategy | `XBTZAR` |
| `--aggressive-interval` | Check interval for aggressive trader (seconds) | 180 |
| `--conservative-interval` | Check interval for conservative trader (seconds) | 900 |
| `--aggressive-stop-loss` | Stop loss for aggressive trader | 0.015 (1.5%) |
| `--aggressive-take-profit` | Take profit for aggressive trader | 0.025 (2.5%) |
| `--conservative-stop-loss` | Stop loss for conservative trader | 0.012 (1.2%) |
| `--conservative-take-profit` | Take profit for conservative trader | 0.020 (2.0%) |
| `--alternating-delay` | Time to run each strategy before switching (seconds) | 3600 (1 hour) |
| `--no-websocket` | Disable WebSocket for real-time data | WebSocket enabled |

## Example Configurations

**Different trading pairs for each strategy:**

```bash
./run_continuous.sh --aggressive-pair ETHZAR --conservative-pair XBTZAR --dry-run
```

**Custom risk parameters:**

```bash
./run_continuous.sh --aggressive-stop-loss 0.02 --aggressive-take-profit 0.03 --conservative-stop-loss 0.01 --conservative-take-profit 0.018 --dry-run
```

**Modified check intervals:**

```bash
./run_continuous.sh --aggressive-interval 120 --conservative-interval 600 --dry-run
```

## Monitoring

### Logs

The continuous trading system creates several log files:

- `data/logs/aggressive_day_trader.log` - Logs for the aggressive strategy
- `data/logs/conservative_day_trader.log` - Logs for the conservative strategy
- `data/logs/restart.log` - Logs of script restarts

Monitor these logs in real-time:

```bash
tail -f data/logs/aggressive_day_trader.log
```

### Running in the Background

To run the trading system in the background:

```bash
nohup ./run_continuous.sh --dry-run > data/logs/nohup.log 2>&1 &
```

To check if it's running:

```bash
ps aux | grep run_continuous
```

To stop the background process:

```bash
killall run_continuous.sh
```

## Troubleshooting

### Common Issues

- **API Connection Errors**: Check your internet connection and API credentials
- **Frequent Restarts**: Try using `--no-websocket` option to use polling instead
- **No Trading Signals**: Market conditions might not match strategy criteria
- **Low Balance Errors**: Ensure you have sufficient funds for trading

### Fixing Dependencies

If you encounter dependency issues:

```bash
./update_deps.sh
```

## Advanced Usage

### Combining with Market Structure Analysis

For advanced technical analysis:

```bash
./run_continuous.sh --use-market-structure --lookback-period 20 --swing-threshold 0.005 --confidence-threshold 0.7 --dry-run
```

### Running Multiple Instances

To run multiple instances with different configurations:

```bash
# Instance 1 - XBTZAR
./run_continuous.sh --aggressive-pair XBTZAR --conservative-pair XBTZAR --dry-run > data/logs/xbtzar_instance.log 2>&1 &

# Instance 2 - ETHZAR
./run_continuous.sh --aggressive-pair ETHZAR --conservative-pair ETHZAR --dry-run > data/logs/ethzar_instance.log 2>&1 &
```

## Safety Notes

1. **Always start with dry-run mode** until you're confident in your setup
2. **Test with small amounts** when transitioning to live trading
3. **Regularly monitor balances** and trading performance
4. **Set up alerts** for critical errors in the logs
5. **Back up configuration** before making major changes

For complete documentation, see `docs/CONTINUOUS_TRADING.md`.