# Aggressive Live Trading Mode

This document provides detailed information about using the `run_aggressive_live.py` script for executing real trades with the aggressive day trading strategy on the Luno exchange.

## ⚠️ IMPORTANT WARNING ⚠️

**This script is designed to execute REAL trades with REAL money on your Luno account.**

- All dry-run settings will be ignored
- Actual funds will be used for trading
- Stop loss and take profit mechanisms are in place, but market volatility can cause losses
- Start with small amounts until you're confident in the system

## Requirements

Before using this script, ensure you have:

1. A Luno account with API access
2. Sufficient balance for the trading pair you wish to use
3. API credentials stored in a `.env` file in the project root:
   ```
   LUNO_API_KEY=your_api_key_here
   LUNO_API_SECRET=your_api_secret_here
   ```
4. Python 3.7+ with all dependencies installed (see `requirements.txt`)

## Usage

### Basic Usage

Run the script with default settings:

```bash
./run_aggressive_live.py
```

This will:
- Use the XBTMYR trading pair
- Set a 1.5% stop loss
- Set a 2.5% take profit
- Trade with an amount of 0.001
- Check for trading opportunities every 3 minutes (180 seconds)

### Custom Parameters

Customize the trading behavior:

```bash
./run_aggressive_live.py --pair ETHZAR --stop-loss 0.02 --take-profit 0.04 --amount 0.01 --interval 300
```

### Skipping Confirmation

For automated deployments, you can skip the confirmation prompt:

```bash
./run_aggressive_live.py --yes-i-know-this-uses-real-money
```

**USE WITH EXTREME CAUTION**: This will start trading immediately without confirmation.

## Parameters

| Parameter | Description | Default | Example |
|-----------|-------------|---------|---------|
| `--pair` | Trading pair to use | XBTMYR | `--pair ETHZAR` |
| `--stop-loss` | Stop loss percentage (decimal) | 0.015 (1.5%) | `--stop-loss 0.02` for 2% |
| `--take-profit` | Take profit percentage (decimal) | 0.025 (2.5%) | `--take-profit 0.03` for 3% |
| `--amount` | Amount to trade per transaction | 0.001 | `--amount 0.005` |
| `--interval` | Seconds between market checks | 180 | `--interval 300` for 5 minutes |
| `--yes-i-know-this-uses-real-money` | Skip confirmation prompt | False | Use flag to enable |

## Logging

The script creates detailed logs in:
```
data/logs/aggressive_day_trader_LIVE.log
```

Monitor this file for trading activity, signals, and any errors.

## How It Works

1. **Market Analysis**: The script analyzes market data using technical indicators:
   - Fast EMA (5 periods)
   - Medium EMA (13 periods)
   - SMA (20 periods)

2. **Signal Generation**: Based on these indicators, the script generates buy/sell signals with confidence levels.

3. **Trade Execution**: When confidence exceeds the threshold (default 50%), trades are executed.

4. **Risk Management**: Stop loss and take profit levels are enforced to limit potential losses.

## Stopping the Trader

Press `Ctrl+C` at any time to gracefully stop the trader. The script will complete the current cycle and then terminate.

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Ensure your API keys are correctly set in the `.env` file
   - Verify the API key has trading permissions on Luno

2. **Insufficient Balance**
   - Check that you have sufficient balance for the trading pair
   - Reduce the trading amount with `--amount`

3. **No Trades Executing**
   - Check the log file for signal confidence levels
   - The script only trades when confidence exceeds 50%
   - Market conditions may not be favorable for the strategy

### Error Checking

If you encounter errors:

1. Check the log file: `data/logs/aggressive_day_trader_LIVE.log`
2. Verify your API permissions on Luno
3. Ensure you have sufficient balance
4. Check your internet connection

## Best Practices

1. **Start Small**: Begin with minimal amounts (`--amount 0.0005` or lower)
2. **Monitor Continuously**: Watch the logs during initial trading
3. **Test First**: Consider running in dry-run mode with `run_trader.py --dry-run` before using this script
4. **Set Reasonable Risk**: Adjust stop-loss and take-profit according to market volatility
5. **Backup API Keys**: Keep a secure backup of your API credentials

## Advanced Usage

### Running in Background

Use nohup to run the script in the background:

```bash
nohup ./run_aggressive_live.py > trading.out 2>&1 &
```

### Scheduled Trading

Use cron to schedule trading during specific hours:

```
# Run trading from 9 AM to 5 PM on weekdays
0 9 * * 1-5 cd /path/to/luno-bot && ./run_aggressive_live.py --yes-i-know-this-uses-real-money
0 17 * * 1-5 pkill -f run_aggressive_live.py
```

## Disclaimer

Trading cryptocurrencies involves significant risk. This tool is provided as-is with no guarantees of profit or protection from losses. Always trade with funds you can afford to lose.