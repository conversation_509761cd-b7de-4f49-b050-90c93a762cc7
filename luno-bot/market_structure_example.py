import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from models.market_structure import MarketStructureAnalyzer, MarketStructurePredictor

def generate_sample_data(days=100, volatility=0.02, trend=0.001):
    """Generate sample price data for testing."""
    # Create date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # Generate price data with trend and volatility
    np.random.seed(42)  # For reproducible results
    prices = 100 * (1 + np.cumsum(np.random.normal(trend, volatility, len(date_range))))
    
    # Add some patterns (higher highs, lower lows, etc.)
    # Add a strong uptrend in the middle
    mid_point = len(prices) // 2
    prices[mid_point:mid_point+20] *= np.linspace(1, 1.2, 20)
    
    # Add a downtrend near the end
    prices[-20:] *= np.linspace(1, 0.9, 20)
    
    # Create DataFrame
    data = pd.DataFrame({
        'timestamp': date_range,
        'open': prices * np.random.uniform(0.99, 1.01, len(prices)),
        'high': prices * np.random.uniform(1.01, 1.03, len(prices)),
        'low': prices * np.random.uniform(0.97, 0.99, len(prices)),
        'close': prices,
        'volume': np.random.uniform(1000, 5000, len(prices)) * (1 + 0.5 * np.sin(np.linspace(0, 8*np.pi, len(prices))))
    })
    
    return data

# Generate sample data
print("Generating sample data...")
sample_data = generate_sample_data()
print(f"Generated {len(sample_data)} data points")

# Initialize the Market Structure Analyzer
print("\nInitializing Market Structure Analyzer...")
analyzer = MarketStructureAnalyzer(
    swing_window=5,
    detection_threshold=0.01,
    lookback_period=20
)

# Analyze the market structure
print("\nAnalyzing market structure...")
analysis = analyzer.analyze_market_structure(sample_data)

# Print the analysis results
print("\nMARKET STRUCTURE ANALYSIS RESULTS:")
print(f"Trend: {analysis['trend']}")
print(f"Structure: {analysis['structure']}")
print(f"ADX (trend strength): {analysis['adx']:.2f}")
print(f"Higher Highs: {analysis['higher_highs']}")
print(f"Higher Lows: {analysis['higher_lows']}")
print(f"Lower Highs: {analysis['lower_highs']}")
print(f"Lower Lows: {analysis['lower_lows']}")

# Print support and resistance levels
print("\nSUPPORT LEVELS:")
for level, strength in analysis['support_levels']:
    print(f"  Price: {level:.2f}, Strength: {strength:.2f}")

print("\nRESISTANCE LEVELS:")
for level, strength in analysis['resistance_levels']:
    print(f"  Price: {level:.2f}, Strength: {strength:.2f}")

# Generate a trading signal
print("\nGenerating trading signal...")
predictor = MarketStructurePredictor(
    lookback_period=20,
    swing_threshold=0.01,
    confidence_threshold=0.6
)

signal = predictor.generate_signal(single_data=sample_data)

# Print the signal details
print("\nTRADING SIGNAL:")
print(f"Action: {signal['action'].upper()}")
print(f"Confidence: {signal['confidence']:.2f}")
print(f"Reason: {signal['reason']}")
print(f"Generated at: {signal['timestamp']}")

print("\nUse this Market Structure Analysis to make trading decisions!")